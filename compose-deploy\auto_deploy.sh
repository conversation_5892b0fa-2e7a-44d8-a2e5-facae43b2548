#!/bin/bash

PROGNAME=$0

# Base Path
DEPLOY_PATH=$(dirname $(readlink -f "$0"))
# For macOS Users
# DEPLOY_PATH=$PWD

# Deploy config
source $DEPLOY_PATH/.config.env

# Include all subscripts
for scripts in $DEPLOY_PATH/scripts/*.sh; do source "$scripts"; done

# assert baseService中包含一个base_postgres的容器,且postgresql的url有效. 5432为容器中的pg端口号,不是宿主机的映射端口号
#gis
psql="docker exec -i base_postgres psql postgresql://postgres@localhost:5432/gis?options=--search_path%3dpublic"
#mes
bizsql="docker exec -i base_business_postgres psql postgresql://mes@localhost:5432/mes?options=--search_path%3dpublic"
#tsdb
tssql="docker exec -i tdengine taos -h tdengine "

base_modules=("base" "data_access" "minio" "topo" "workflow" "gis" "mes")
baseDir="$DEPLOY_PATH/base"
changesetDir="$baseDir/changeset"


#使用git查询材料版本号,或让用户手动输入确认
ask_material_version() {
  local version #important
  version=$(git describe --tags 2>/dev/null)
  if [ $? -ne 0 ]; then
    read -p "请输入当前部署材料的版本号:" version
  else
    while true; do
      read -p "检测到当前部署材料的GIT版本tag为:[$version], 是否正确? [Y/N]" answer
      case $answer in
      Y) echo "$version" && return 0 ;;
      N) read -p "请输入当前部署材料的版本号:" version
        break
      ;;
      *) ;;
      esac
    done
  fi

  while true; do
    read -p "要部署的版本号为:[$version], 是否继续? [Y/N]" answer
    case $answer in
    Y) echo "$version" && return 0 ;;
    N) return 1 ;;
    *) ;;
    esac
  done
}

start() {
  echo_yellow "正在启动服务"

  # echo_yellow "新建 middle 内部 network"
  # docker network create middle
  # echo_yellow "新建 base 依赖的 volume"
  # create_base_volumes
  # echo_yellow "docker 初始化"
  # init_docker

  # 启动基础服务
  start_base_service

  if [ $DEPLOY_VIDEO_SERVICE = true ]; then start_video_service; fi
  if [ $DEPLOY_MOS_SERVICE = true ]; then start_mos_service; fi
  if [ $DEPLOY_IOT_SERVICE = true ]; then start_iot_service; fi
  if [ $DEPLOY_MES_SERVICE = true ]; then start_mes_service; fi
  if [ $DEPLOY_GIS_SERVICE = true ]; then start_gis_service; fi
  if [ $DEPLOY_APP_SERVICE = true ]; then start_app_service; fi
  if [ $DEPLOY_FLOW_SERVICE = true ]; then start_workflow_service; fi

  # 默认不启动 ssh 服务
  #start_ssh_service

  cd $DEPLOY_PATH
}


stop() {
  echo_yellow "停止全部服务"
  if [ $DEPLOY_VIDEO_SERVICE = true ]; then stop_video_service; fi
  if [ $DEPLOY_MOS_SERVICE = true ]; then stop_mos_service; fi
  if [ $DEPLOY_IOT_SERVICE = true ]; then stop_iot_service; fi
  if [ $DEPLOY_MES_SERVICE = true ]; then stop_mes_service; fi
  if [ $DEPLOY_GIS_SERVICE = true ]; then stop_gis_service; fi
  if [ $DEPLOY_APP_SERVICE = true ]; then stop_app_service; fi
  if [ $DEPLOY_FLOW_SERVICE = true ]; then stop_workflow_service; fi

  # 停止基础服务
  stop_base_service
  
  # 默认停止 ssh 服务
  #stop_ssh_service

  cd $DEPLOY_PATH
}

down() {
  echo_yellow "停止并删除全部服务,会造成部分数据丢失,谨慎操作,是否继续？（输入 yes 继续）"
  read; if [[ $REPLY != yes ]]; then exit 1; fi

  if [ $DEPLOY_VIDEO_SERVICE = true ]; then down_video_service; fi
  if [ $DEPLOY_MOS_SERVICE = true ]; then down_mos_service; fi
  if [ $DEPLOY_IOT_SERVICE = true ]; then down_iot_service; fi
  if [ $DEPLOY_MES_SERVICE = true ]; then down_mes_service; fi
  if [ $DEPLOY_GIS_SERVICE = true ]; then down_gis_service; fi
  if [ $DEPLOY_APP_SERVICE = true ]; then down_app_service; fi
  if [ $DEPLOY_FLOW_SERVICE = true ]; then down_workflow_service; fi

  # 停止基础服务
  down_base_service
  # 默认不停止 ssh 服务
  #stop_ssh_service

  cd $DEPLOY_PATH
}

restart() {
  echo_yellow "重启的所有服务,是否继续？（输入 yes 继续）"
  read; if [[ $REPLY != yes ]]; then exit 1; fi

  # 重启基础服务
  restart_base_service

  if [ $DEPLOY_VIDEO_SERVICE = true ]; then restart_video_service; fi
  if [ $DEPLOY_MOS_SERVICE = true ]; then restart_mos_service; fi
  if [ $DEPLOY_IOT_SERVICE = true ]; then restart_iot_service; fi
  if [ $DEPLOY_MES_SERVICE = true ]; then restart_mes_service; fi
  if [ $DEPLOY_GIS_SERVICE = true ]; then restart_gis_service; fi
  if [ $DEPLOY_APP_SERVICE = true ]; then restart_app_service; fi
  if [ $DEPLOY_FLOW_SERVICE = true ]; then restart_workflow_service; fi

  # 默认不启动 ssh 服务
  #start_ssh_service

  cd $DEPLOY_PATH
}


prune() {
  echo_yellow "请输入 yes 确认停止服务并删除所有数据（不可恢复！）"
  read
  if [[ $REPLY == yes ]]; then
    down && prune_docker
  fi
}

restart_nginx() {
  cd $MOS_SERVICE_PATH
  docker compose stop frontend-nginx-gateway &&
  docker compose rm -f frontend-nginx-gateway &&
  docker compose up -d frontend-nginx-gateway &&
  cd $DEPLOY_PATH
}

start_postgres() {
  echo_yellow "启动 PostgreSQL"
  cd $baseDir
  docker compose --compatibility up -d postgres
  wait_postgres
  docker compose --compatibility up -d business-postgres
  wait_biz_postgres
}

init_tsdb() {
  echo_yellow "启动 TDengine"
  cd $baseDir
  docker compose --compatibility up -d tdengine
  wait_td
  tssqlImport "$tssql"
  tssqlCheckInit
}

deploy() {
  version=$(ask_material_version)
  [ $? -ne 0 ] && exit
  check_system && check_config_env && echo_yellow "正在部署服务"
  init_docker

  start_postgres
  local curVer=$(psqlGetCurVer "$psql")
  if [ ! -z "$curVer" ]; then
    stop
    echo_error "当前机器中存在版本号为 [$curVer] 的中台服务, 无法进行全新安装, 请清理 base_postgres 容器的数据后再安装."
    exit
  fi

  init_module

  # TODO: update module , maintained by manual
  #update_module 0 $version

  psqlMarkVersion "$psql" "$version"
  if [ $DEPLOY_IOT_SERVICE = true ]; then
    init_tsdb;
  fi

  start
  echo_yellow "服务已全部启动"
}

# prune 后容器内 pg 启动时间较长, 轮询检测
wait_postgres(){
  local timeout=120
  while true; do
    if [ $timeout -lt 0 ]; then
      echo "无法访问postgres容器数据库" && exit 1
    fi
    echo "INFO: 等待 postgres 数据库启动.."
    psqlCheckConn "$psql"
    [ $? -eq 0 ] && break;
    sleep 3
    ((timeout-=3))
  done
}

# prune 后容器内 pg 启动时间较长, 轮询检测
wait_biz_postgres(){
  local timeout=120
  while true; do
    if [ $timeout -lt 0 ]; then
      echo "无法访问 business_postgres 容器数据库" && exit 1
    fi
    echo "INFO: 等待 business_postgres 数据库启动.."
    psqlCheckConn "$bizsql"
    [ $? -eq 0 ] && break;
    sleep 3
    ((timeout-=3))
  done
}

wait_td() {
  local timeout=120
  while true; do
    if [ $timeout -lt 0 ]; then
      echo "无法访问 TDengine 容器数据库" && exit 1
    fi
    echo "INFO: 等待时序数据库启动.."
    tssqlCheckConn "$tssql"
    [ $? -eq 0 ] && break;
    sleep 3
    ((timeout-=3))
  done
}

# TODO: 加入普罗米修斯的部署,以及单独启停命令
init_module() {
  psqlCheckSchema "$psql"
  psqlCreateCvsTable "$psql"

  # gis
  local sqlFile="$changesetDir/init/gis/init-auth.sql"
  [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile

  sqlFile="$changesetDir/init/gis/init-push.sql"
  [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile

  if [ $DEPLOY_IOT_SERVICE = true ]; then
    sqlFile="$changesetDir/init/gis/init-iot.sql"
    [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile

    sqlFile="$changesetDir/init/gis/init-video.sql"
    [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile;
  fi


  if [ $DEPLOY_GIS_SERVICE = true ]; then
    sqlFile="$changesetDir/init/gis/init-gis.sql"
    [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile;
  fi

  sqlFile="$changesetDir/init/gis/init-nacos.sql"
  [ -e $sqlFile ] && psqlExecSqlFile "$psql" $sqlFile

  # mes  basebackend, workflow, mes
  if [ $DEPLOY_MES_SERVICE = true ]; then
    sqlFile="$changesetDir/init/mes/init-mes.sql"
    [ -e $sqlFile ] && psqlExecSqlFile "$bizsql" $sqlFile;
  fi

  sqlFile="$changesetDir/init/mes/init-bladex.sql"
  [ -e $sqlFile ] && psqlExecSqlFile "$bizsql" $sqlFile

  if [ $DEPLOY_FLOW_SERVICE = true ]; then
    sqlFile="$changesetDir/init/mes/init-workflow.sql"
    [ -e $sqlFile ] && psqlExecSqlFile "$bizsql" $sqlFile;
  fi

  for module in "${base_modules[@]}"; do
    initSh="$changesetDir/init/init.$module.sh"
    if [ -e "$initSh" ]; then
      curPwd=$(pwd)
      echo "INFO: 执行初始化脚本$initSh"
      source $initSh
      cd $curPwd
    fi
  done
}

init_topo() {
  local initSh="$changesetDir/init/init.topo.sh"
  if [ -e "$initSh" ]; then
    echo "INFO: 执行初始化脚本$initSh"
    source $initSh
  fi
}

update_module() {
  local curVer=$1 targetVer=$2
  local versions=$(find $changesetDir -maxdepth 1 -mindepth 1 -type d -printf '%f\n' | grep -v init | sort -t '\0' -V)
  local versions=(${versions//\n/ })
  for v in ${versions[@]}; do
    # echo "DEBUG: v=$v,curVer=$curVer,targetVer=$targetVer"
    if [[ $(compare_version $v $curVer) -eq 1 ]] && [[ $(compare_version $v $targetVer) -le 0 ]]; then
      for module in "${base_modules[@]}"; do
        local sqlFile="$changesetDir/$v/$module/up.sql"
        [ -e "$sqlFile" ] && psqlExecSqlFile "$psql" $sqlFile
        upSh="$changesetDir/$v/$module/up.sh"
        if [ -e "$upSh" ]; then
          curPwd=$(pwd)
          echo "INFO: 执行升级脚本:$upSh"
          source $upSh
          cd $curPwd
        fi
      done
    fi
  done
}


update() {
  rolling=$1
  check_system && check_config_env
  local version   #important
  version=$(ask_material_version)
  [ $? -ne 0 ] && exit

  while true; do
    read -p "中台服务是否正在运行? [Y/N]" reply
    case $reply in
      Y) break;;
      N) echo_error "请启动服务后再进行升级" && exit;;
      ## TODO 当服务未运行时的升级流程?
      *) ;;
    esac
  done

  local curVer
  curVer=$(psqlGetCurVer "$psql")
  if [ -z "$curVer" ]; then
    echo_error "从数据库databasechangelog表中查不到当前部署的版本号,无法升级"
    exit
  elif [[ $(compare_version $curVer $version) -ge 0 ]]; then
    echo_error "当前应用版本号为[$curVer],无需升级"
    exit
  else
    while true; do
      read -p "要将当前服务[$curVer]升级至[$version]? [Y/N]" reply
      case $reply in
      Y)
        update_module $curVer $version
        psqlMarkVersion "$psql" $version
        [ -z "$rolling" ] && stop
        start
        echo_yellow "升级成功"
        exit
        ;;
      N)
        exit
      ;;
      *) ;;
      esac
    done
  fi

}

create_base_volumes() {
  BASE_VOLUMES=("converter-monitor-log" "data-warn-log" "video-redis" "data-monitor-image" "base-rabbitmq" "base-red-node" "base-redis" "base-postgres" "base-influxdb" "bladex-minio" "base-ftp" "base-business-postgres" "gis-biz-log" "mes-biz-log" "base-workflow" "data-monitor-log" "data-storage-log")
  for volume_name in ${BASE_VOLUMES[@]}; do
    echo_yellow $volume_name
    docker volume create $volume_name
  done
}

backup() {
  OPTION=$2
  BACKUP_PATH=$BACKUP_BASE_PATH/$(date "+%Y-%m-%d")

  # Backup docker named volumes
  for volume_name in ${DOCKER_BACKUP_VOLUMES[@]}; do
    backup_docker_volume $volume_name
  done

  # Backup deploy folder
  echo_yellow "备份部署材料"
  tar -zcvf --exclude='.git' -f $BACKUP_PATH/deploy_dir.tgz $DEPLOY_PATH

  # Backup PostgreSQL
  backup_postgres

  # TODO: InfluxDB

  # 删除 3 天前的文件
  if [[ $OPTION == autorm ]]; then
    find $BACKUP_PATH -type d -mtime +2 -exec rm -rf {} \;
  fi
}

auto_backup() {
  grep "auto_deploy.sh backup" /etc/crontab
  if [ $? -ne 0 ]; then

    cat >>/etc/crontab <<EOF
00 03 * * * root bash -c "$DEPLOY_PATH/auto_deploy.sh backup autorm" > /dev/null 2>&1
EOF
    echo "开启自动备份，备份目录: " $BACKUP_BASE_PATH
  else
    echo_yellow "备份已开启！"
  fi
}

docker_image_pull() {
  cd $BASE_SERVICE_PATH && docker_compose_pull_with_retry &

  if [ $DEPLOY_VIDEO_SERVICE = true ]; then
  cd $VIDEO_SERVICE_PATH && docker_compose_pull_with_retry &fi

  if [ $DEPLOY_IOT_SERVICE = true ];then
  cd $IOT_SERVICE_PATH && docker_compose_pull_with_retry & fi

  if [ $DEPLOY_MES_SERVICE = true ]; then
  cd $MES_SERVICE_PATH && docker_compose_pull_with_retry & fi

  if [ $DEPLOY_GIS_SERVICE = true ]; then
  cd $GIS_SERVICE_PATH && docker_compose_pull_with_retry & fi

  cd $MOS_SERVICE_PATH && docker_compose_pull_with_retry &

  if [ $DEPLOY_FLOW_SERVICE = true ]; then
  cd $WORK_FLOW_PATH && docker_compose_pull_with_retry & fi

  if [ $DEPLOY_APP_SERVICE = true ]; then
  cd $APP_SERVICE_PATH && docker_compose_pull_with_retry & fi

  cd $SSH_SERVICE_PATH && docker_compose_pull_with_retry &
  docker pull $DOCKER_BUSY_BOX_IMAGE
  cd $DEPLOY_PATH
}

show_version() {
  psqlGetCurVer "$psql"
}

docker_service() {
  command=$2
  case $command in
  "init")
    init_docker
    ;;
  "pull")
    docker_image_pull
    ;;
  *)
    echo_error "错误的指令"
    usage
    ;;
  esac
  cd $DEPLOY_PATH
}

topo_service() {
  command=$2
  case $command in
  "init")
    init_topo
    ;;
  *)
    echo_error "错误的指令"
    usage
    ;;
  esac
  cd $DEPLOY_PATH
}

nginx_service() {
  command=$2
  case $command in
  "restart")
    restart_nginx
    ;;
  *)
    echo_error "错误的指令"
    usage
    ;;
  esac
  cd $DEPLOY_PATH
}

ssh_service() {
  command=$2
  case $command in
  "start")
    start_ssh_service
    ;;
  "stop")
    stop_ssh_service
    ;;
  *)
    echo_error "错误的指令"
    usage
    ;;
  esac
  cd $DEPLOY_PATH
}

config_service() {
  command=$2
  case $command in
  "check")
    check_config_env
    ;;
  *)
    echo_error "错误的指令"
    usage
    ;;
  esac
  cd $DEPLOY_PATH
}

usage() {
  cat <<EOF >&2
USAGE:

    $PROGNAME COMMAND [OPTIONS]

COMMAND:
    deploy -i                     在全新环境上部署并启动服务,版本号为部署材料的当前版本号.
    start                         启动所有服务
    stop                          停止所有服务
    down                          停止并删除所有服务
    restart                       重启所有服务
    backup                        备份数据
    auto-backup                   开启自动备份
    update [-r] [-g -t <tag>]     版本升级
    prune                         停止所有服务并删除所有数据
    config      [COMMAND]         环境变量配置
    ssh         [COMMAND]         SSH 服务
    topo        [COMMAND]         Topo 服务
    nginx       [COMMAND]         Nginx 服务
    docker      [COMMAND]         Docker 服务
    version                       查看当前已部署的版本(需要服务运行中)

OPTIONS:
    update  -g -t <tag>:          更新指定tag号的部署材料
            -r:                   不停止服务升级

    config  check:                检查环境变量配置

    ssh     start:                启动 SSH 服务
            stop:                 停止 SSH 服务

    nginx   restart:              重启 MOS Nginx 服务

    topo    init:                 导入 Topo 图片和数据

    docker  init:                 初始化 Docker 网络和命名卷
            pull:                 拉取 Docker 镜像

EOF
  exit 1
}

command=$1
case $command in
"deploy")
  if [ -z "$2" ]; then
    echo_error "请使用$PROGNAME start命令"
    exit
  elif [ "$2" == "-i" ]; then
    deploy
  else
    usage
  fi
  ;;
"backup")
  backup $@
  ;;
"auto-backup")
  auto_backup
  ;;
"update")
  shift
  while getopts :t:rg o; do
    case $o in
    t) tag=$OPTARG ;;
    r) rolling=true ;;
    g) git_only=true ;;
    *) usage ;;
    esac
  done
  if [ ! -z "$SPUG_RELEASE" ]; then tag=$SPUG_RELEASE; fi
  if [ -n "$git_only" ]; then
    git_update $tag
    exit
  else
    update $rolling
  fi
  ;;
"start")
  start
  ;;
"stop")
  stop
  ;;
"down")
  down
  ;;
"prune")
  prune
  ;;
"restart")
  restart
  ;;
"nginx")
  nginx_service $@
  ;;
"docker")
  docker_service $@
  ;;
"topo")
  topo_service $@
  ;;
"ssh")
  ssh_service $@
  ;;
"config")
  config_service $@
  ;;
"version")
  show_version $@
  ;;
"--help")
  usage
  ;;
*)
  echo_error "错误的指令"
  usage
  ;;
esac
