#!/bin/bash
###
 # @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @Date: 2024-10-24 17:44:24
 # @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @LastEditTime: 2024-11-19 12:34:43
 # @FilePath: \yklw-test_gkpt-deploy-v2.1.0\base\changeset\init\init.topo.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

cd $MOS_SERVICE_PATH
t_num=$(docker ps|grep docker-monitor|wc -l)
if [ $t_num -eq 1 ]; then
  echo_yellow "停止 data-monitor 服务"
  docker_compose_down $MOS_SERVICE_PATH
fi
## TODO how to execute this shell exactly.
#topo_git="******************:10022/bjproduct/skeleton/repo/topo-model.git"
topo_git="https://gitee.com/bjbigdata/topo-model.git"
echo_yellow "topo模块初始化: 准备从$topo_git下载最新的topo模型图片,请输入git用户名和密码."
if git clone $topo_git my_topo_model; then
  cd my_topo_model

  ##把图片放到某个docker卷中
  echo_yellow "正在导入图片"
  target_dir=$(docker volume inspect --format '{{ .Mountpoint }}' data-monitor-image)
  yes| cp images/* $target_dir -r


  ###根据git项目的设置,使用docker运行js以生成sql语句. sql语句经常要手动执行,因此sql必须要ON CONFLICT DO NOTHING;
  # docker compose up
  echo_yellow "开始导入model Sql"
  # cd ..
  echo_yellow "当前目录pwd: "`pwd`
  for fname  in `find . -name "*.sql"`
  do
    echo_yellow "正在导入 $fname"
    psqlExecSqlFile "$psql" $fname
  done
  cd $MOS_SERVICE_PATH
  rm my_topo_model -rf
  if [ $t_num -eq 1 ]; then
    echo_yellow "启动 data-monitor 服务"
    docker_compose_up $MOS_SERVICE_PATH
  fi
else
  echo_error "无法从git获取topo图片库,跳过init.topo.sh"
fi
