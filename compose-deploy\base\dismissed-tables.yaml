- table:
    schema: public
    name: alembic_version
- table:
    schema: public
    name: area
- table:
    schema: public
    name: area_top_data_info

- table:
    schema: public
    name: card_directive

- table:
    schema: public
    name: daily_login_log
  computed_fields:
  - name: last_operator_time
    definition:
      function:
        schema: public
        name: last_operator_time
- table:
    schema: public
    name: data_attribute

- table:
    schema: public
    name: data_point
- table:
    schema: public
    name: data_point_temp
- table:
    schema: public
    name: data_quick_query
- table:
    schema: public
    name: data_quick_query_temp

- table:
    schema: public
    name: data_status_temp
    
    
- table:
    schema: public
    name: data_warming


- table:
    schema: public
    name: fire_alarm_config
  object_relationships:
  - name: fire_alarm_threshold
    using:
      foreign_key_constraint_on: threshold_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: c_point_id
  - name: safetySupervisionByTPointId
    using:
      foreign_key_constraint_on: t_point_id
  - name: work_face
    using:
      foreign_key_constraint_on: work_face_serial
  array_relationships:
  - name: fire_alarm_realtimes
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: fire_alarm_realtime
- table:
    schema: public
    name: fire_alarm_realtime
  object_relationships:
  - name: fire_alarm_config
    using:
      foreign_key_constraint_on: config_id
- table:
    schema: public
    name: fire_alarm_statistics
- table:
    schema: public
    name: fire_alarm_threshold
  array_relationships:
  - name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: threshold_id
        table:
          schema: public
          name: fire_alarm_config
- table:
    schema: public
    name: flight_route


- table:
    schema: public
    name: gas_emission_config
  object_relationships:
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
  - name: work_face
    using:
      foreign_key_constraint_on: work_face_serial
  array_relationships:
  - name: gas_emission_controlled_maps
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_controlled_map
  - name: gas_emission_methane_maps
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_methane_map
  - name: gas_emission_realtimes
    using:
      foreign_key_constraint_on:
        column: config_id
        table:
          schema: public
          name: gas_emission_realtime
- table:
    schema: public
    name: gas_emission_controlled_map
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: gas_emission_methane_map
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
  - name: safety_supervision
    using:
      foreign_key_constraint_on: point_id
- table:
    schema: public
    name: gas_emission_realtime
  object_relationships:
  - name: gas_emission_config
    using:
      foreign_key_constraint_on: config_id
- table:
    schema: public
    name: gas_emission_statistics


- table:
    schema: public
    name: location_overman
- table:
    schema: public
    name: location_overman_statistics
- table:
    schema: public
    name: login_statistics

- table:
    schema: public
    name: model_attribute_type_temp

- table:
    schema: public
    name: model_system_bak
- table:
    schema: public
    name: model_system_temp

- table:
    schema: public
    name: operation_log

- table:
    schema: public
    name: portal_monitor
- table:
    schema: public
    name: portal_system


- table:
    schema: public
    name: production_information
- table:
    schema: public
    name: pumping_wells_definition
- table:
    schema: public
    name: pumping_wells_realtime
- table:
    schema: public
    name: quake_pos_result
   
#- table:
#    schema: public
#    name: safety_supervision    
#  array_relationships:
	- name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: c_point_id
        table:
          schema: public
          name: fire_alarm_config
  - name: fireAlarmConfigsByTPointId
    using:
      foreign_key_constraint_on:
        column: t_point_id
        table:
          schema: public
          name: fire_alarm_config
  - name: gas_emission_configs
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_config
  - name: gas_emission_controlled_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_controlled_map
  - name: gas_emission_methane_maps
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_emission_methane_map
  - name: gas_patrols
    using:
      foreign_key_constraint_on:
        column: point_id
        table:
          schema: public
          name: gas_patrol


- table:
    schema: public
    name: scene

- table:
    schema: public
    name: sms
  object_relationships:
  - name: sms_template
    using:
      foreign_key_constraint_on: sms_template_id
- table:
    schema: public
    name: sms_template
  array_relationships:
  - name: sms
    using:
      foreign_key_constraint_on:
        column: sms_template_id
        table:
          schema: public
          name: sms

- table:
    schema: public
    name: user_log


- table:
    schema: public
    name: work_face
  array_relationships:
  - name: fire_alarm_configs
    using:
      foreign_key_constraint_on:
        column: work_face_serial
        table:
          schema: public
          name: fire_alarm_config
  - name: gas_emission_configs
    using:
      foreign_key_constraint_on:
        column: work_face_serial
        table:
          schema: public
          name: gas_emission_config
