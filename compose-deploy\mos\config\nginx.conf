
# 解决 Nginx 变量名称过长的问题
variables_hash_bucket_size 128;

server {
  listen       80;
  server_name  localhost;
  root   /usr/share/nginx/html;

  # 使用 Docker 网络进行 DNS 解析
  resolver 127.0.0.11 valid=10s ipv6=off;

  # Gzip
  gzip on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_types
    text/css
    text/javascript
    text/xml
    text/plain
    application/javascript
    application/x-javascript;
  proxy_cache_valid 5m;

  # 二次开发自定义配置
  include /etc/nginx/conf.d/localhost.etc/*.conf;

  # 日志
  rewrite_log on;
  error_log    /var/log/nginx/debug.log debug;

  # ==== 前端代理 ====

  # 前端 - 系统设置
  set ${DOLLAR}upstream_frontend_module_system_admin "http://frontend-module-system_admin";
  location /module-system_admin {
    rewrite /module-system_admin/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_system_admin;
  }

  # 前端 - 业务系统
  set ${DOLLAR}upstream_frontend_base_business_serve "http://frontend-base-business_serve";
  location /base-business_serve {
    rewrite /base-business_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_base_business_serve;
  }

  # 前端 - 数据联网
  set ${DOLLAR}upstream_frontend_biz_data_ims "http://frontend-biz-data_ims";
  location /base-data_ims {
    rewrite /base-data_ims/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_biz_data_ims;
  }

  # 前端 - 数据接入
  set ${DOLLAR}upstream_frontend_module_data_access "http://frontend-module-data_access";
  location /module-data_access {
    rewrite /module-data_access/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_data_access;
  }

  # 前端 - 点表列表
  set ${DOLLAR}upstream_frontend_module_data_view "http://frontend-module-data_view";
  location /module-data_view {
    rewrite /module-data_view/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_data_view;
  }

  # 前端 - 拓扑渲染器
  set ${DOLLAR}upstream_frontend_topo_render "http://frontend-module-topo_render";
  location /module-topo_render {
    rewrite /module-topo_render/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_topo_render;
  }

  # 前端 - 拓扑编辑器
  set ${DOLLAR}upstream_frontend_module_topo_editor "http://frontend-module-topo_editor";
  location /module-topo_editor {
    rewrite /module-topo_editor/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_topo_editor;
  }

  # 前端 - 页面编辑器
  set ${DOLLAR}upstream_frontend_module_page_builder "http://frontend-module-page_builder";
  location /module-page_builder {
    rewrite /module-page_builder/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_page_builder;
  }

  # 前端 - 页面渲染器
  set ${DOLLAR}upstream_frontend_module_page_render "http://frontend-module-page_render";
  location /module-page_render {
    rewrite /module-page_render/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_page_render;
  }

  # 前端 - GIS
  set ${DOLLAR}upstream_frontend_module_gis "http://frontend-module-gis";
  location /module-gis {
    rewrite /module-gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis;
  }

  # 前端 - 预警报警
  set ${DOLLAR}upstream_frontend_module_alarm_serve "http://frontend-module-alarm_serve";
  location /base-alarm_serve {
    rewrite /base-alarm_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_alarm_serve;
  }

  # 前端 - 工作流
  set ${DOLLAR}upstream_frontend_base_workflow_serve "http://frontend-base-workflow_serve";
  location /base-workflow_serve {
    rewrite /base-workflow_serve/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_base_workflow_serve;
  }

  # gis - 三维资源
  set ${DOLLAR}upstream_frontend_module_gis_threed "http://frontend-module-gis";
  location /app-gis/res {
    rewrite /app-gis/res/(.+) /last/res/${DOLLAR}1 break ;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
  }
  location /app-gis/res {
    rewrite /app-gis/res/(.+) /last/res/${DOLLAR}1 break ;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
  }
  location /app-gis/extensions {
    rewrite /app-gis/extensions/(.+) /last/extensions/${DOLLAR}1 break ;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
  }
  location /app-gis/last {
    rewrite /app-gis/last/(.+) /last/${DOLLAR}1 break ;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis_threed;
  }

  # 前端 - GIS 静态资源
  location /app-gis/images {
    rewrite /app-gis/(.+) /gis/css/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_module_gis;
  }

  # 3维模型
  set ${DOLLAR}upstream_backend_three_model "${THREE_GIS_UR}";
  location /standard {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_three_model;
  }

  # ==== 前端静态资源代理 ====

  # 拓扑渲染器背景图
  location /images/bg-net.png {
    proxy_pass ${DOLLAR}upstream_frontend_topo_render;
  }

  # 拓扑设备模型图片
  location /interface/images {
    rewrite /interface/images/(.+) /${DOLLAR}1 break;
    root /usr/share/nginx/html/topo/images;
  }

  # 项目静态资源
  location /static {
    root /usr/share/nginx/html;
  }
  location /archive/ {
    autoindex on;
    alias /usr/share/nginx/archive/;
  }

  # ==== 后端 API 代理 ====

  # 后端 - Data Monitor
  set ${DOLLAR}upstream_backend_data_monitor "http://backend-data-monitor:9001";
  location /interface {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_monitor;
  }

  # 后端 - Data Monitor
  set ${DOLLAR}upstream_backend_mes_biz "http://backend-mes-biz:9001";
  location /mes-biz {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_mes_biz;
  }


  # 后端 - Gis Biz
  set ${DOLLAR}upstream_backend_gis_biz "http://backend-gis-biz:9001";
  location /gis-biz {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_gis_biz;
  }
  # 后端 - Push Msg
  set ${DOLLAR}upstream_backend_push_msg "http://backend-push-msg:9001";
  location /push-msg {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_push_msg;
  }

  # 后端 - Workflow
  set ${DOLLAR}upstream_backend_workflow "http://backend-workflow:8004";
  location /workflow {
    client_max_body_size 100m;
    proxy_pass ${DOLLAR}upstream_backend_workflow;
  }

  # 后端 - Data Warn
  set ${DOLLAR}upstream_backend_data_warn "http://backend-data-warn:9001";
  location /api/datawarn {
    rewrite /api/(.+) /${DOLLAR}1 break;
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_warn;
  }

  # 后端 - Data Storage
  set ${DOLLAR}upstream_backend_data_storage "http://backend-data-storage:9001";
  location /dataroute {
    client_max_body_size 10m;
    proxy_pass ${DOLLAR}upstream_backend_data_storage;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # 后端 - CMS
  set ${DOLLAR}upstream_backend_cms "${UPSTREAM_CMS}";
  location /cms {
    proxy_pass ${DOLLAR}upstream_backend_cms;
  }

  # 后端 - app
  set ${DOLLAR}upstream_backend_app_api "http://backend-bd-app:9001";
  location /app-backend {
    proxy_pass ${DOLLAR}upstream_backend_app_api;
  }

  # 后端 - GIS API（洛阳）
  set ${DOLLAR}upstream_backend_gis_api "${UPSTREAM_GIS_API}";
  location /api/gis {
    client_max_body_size 10m;
    rewrite /api/gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_api;
  }

  # 后端 - 三维 API
  set ${DOLLAR}upstream_backend_gis_bim "${UPSTREAM_GIS_THREE}";
  location /bim {
    proxy_pass ${DOLLAR}upstream_backend_gis_bim;
  }
  location /app-gis/noToken {
    rewrite /app-gis/noToken/(.+) /noToken/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_bim;
  }

  # 后端 - GIS 静态资源（洛阳）
  set ${DOLLAR}upstream_backend_gis_static "${UPSTREAM_GIS_STATIC}";
  location /gis/source {
    rewrite /gis/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
  }
  location /minio {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
  }
  location /fonts/simsun.ttf {
    proxy_pass ${DOLLAR}upstream_backend_gis_static;
  }

  # 后端 - GIS 2D
  location /gisserver {
    rewrite /gisserver/(.+) /${DOLLAR}1 break;
    proxy_pass http://172.17.0.1:50003;
  }

  # 后端 - Hasura GraphQL Engine
  set ${DOLLAR}upstream_hasura "http://graphql-engine:8080";
  location /hasura {
    rewrite /hasura/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  location /preview-topo/hasura/ {
    rewrite /preview-topo/hasura/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_hasura;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # 视频服务 - Video SRS
  set ${DOLLAR}upstream_video_srs "http://video-srs:1985";
  location /api/video-service/v1 {
    rewrite /api/video-service/(.+) /api/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_srs;
  }

  # 视频服务 - Video Server
  set ${DOLLAR}upstream_video_server "http://video-server:9000";
  location /api/video-service {
    rewrite /api/video-service/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_server;
  }

  # 视频服务 - Video Server preview
  set ${DOLLAR}upstream_video_server_preview "http://video-preview:19850";
  location /api/video-service/videoPreview {
    rewrite /api/video-service/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_server_preview;
  }

  # 视频服务 - WebSocket FLV
  set ${DOLLAR}upstream_video_streaming_server "http://videojs-flow:8088";
  location /flv {
    rewrite /flv/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_video_streaming_server;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # 视频服务 - WebSocket Raw H.264 - AI Transcode
  location ~ /api/video-transcode/([0-9]+) {
    proxy_pass http://${DOLLAR}arg_HOST:$1;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # AI 设备 WebSocket Raw H.264 视频流
  location /api/ai-device-transcode {
    rewrite /api/ai-device-transcode/(.+) /${DOLLAR}1 break;
    proxy_pass http://${DOLLAR}arg_HOST:${DOLLAR}arg_PORT;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # AI 设备后端
  # 参考: http://nginx.org/en/docs/http/ngx_http_core_module.html#var_http_
  # 注意: Nginx 取 Header field 时会转换为小写，中划线会转换为下划线
  # 例如: X-Device-IP 可以通过 $http_x_device_ip 获取
  location /api/ai-device {
    rewrite /api/ai-device/(.+) /${DOLLAR}1 break;
    proxy_pass http://${DOLLAR}http_x_device_ip:${DOLLAR}http_x_device_port;
    client_max_body_size 1024m;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
  }

  # RabbitMQ
#  set ${DOLLAR}upstream_rabbitmq "http://rabbitmq:15672";
#  location ~* /rabbit/api/ {
#    rewrite ^ ${DOLLAR}request_uri;
#    rewrite ^/rabbit/api/(.*) /api/${DOLLAR}1 break;
#    proxy_pass ${DOLLAR}upstream_rabbitmq${DOLLAR}uri;
#  }
#  location /rabbit {
#    rewrite /rabbit/(.*) /${DOLLAR}1 break;
#    proxy_pass ${DOLLAR}upstream_rabbitmq;
#  }
  set ${DOLLAR}upstream_rabbitmq "http://rabbitmq:15672";
  location ^~ /rabbit/api/ {
    rewrite ^ ${DOLLAR}request_uri;
    rewrite ^/rabbit/api/(.*) /api/${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_rabbitmq${DOLLAR}uri;
  }
  location /rabbit/ {
    #rewrite /rabbit / break;
    rewrite /rabbit/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_rabbitmq;
  }

  # Red-Node
  set ${DOLLAR}upstream_rednode "http://rednode:1880";
  location /data-gw {
    client_max_body_size 10m;
    rewrite /data-gw/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_rednode;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }
#  set ${DOLLAR}upstream_rednode_2 "http://base_rednode_2:1880";
#  location /data-gw2 {
#    client_max_body_size 10m;
#    rewrite /data-gw2/(.*) /${DOLLAR}1 break;
#    proxy_pass ${DOLLAR}upstream_rednode_2;
#    proxy_http_version 1.1;
#    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
#    proxy_set_header   Connection       "upgrade";
#  }
#  set ${DOLLAR}upstream_rednode_3 "http://base_rednode_3:1880";
#  location /data-gw3 {
#    client_max_body_size 10m;
#    rewrite /data-gw3/(.*) /${DOLLAR}1 break;
#    proxy_pass ${DOLLAR}upstream_rednode_3;
#    proxy_http_version 1.1;
#    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
#    proxy_set_header   Connection       "upgrade";
#  }
#  set ${DOLLAR}upstream_rednode_4 "http://base_rednode_4:1880";
#  location /data-gw4 {
#    client_max_body_size 10m;
#    rewrite /data-gw4/(.*) /${DOLLAR}1 break;
#    proxy_pass ${DOLLAR}upstream_rednode_4;
#    proxy_http_version 1.1;
#    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
#    proxy_set_header   Connection       "upgrade";
#  }

  # 后端 - BladeX
  set ${DOLLAR}upstream_backend_bladex_process_user_permission "http://backend-process-user-permission:9401";
  location /api/ {
    rewrite /api/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_backend_bladex_process_user_permission;
    client_max_body_size 1024m;
    proxy_set_header   Host             ${DOLLAR}proxy_host;
    proxy_set_header   X-Real-IP        ${DOLLAR}remote_addr;
    proxy_set_header   X-Forwarded-For  ${DOLLAR}proxy_add_x_forwarded_for;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade          ${DOLLAR}http_upgrade;
    proxy_set_header   Connection       "upgrade";
  }

  # 前端 - 拓扑渲染器（嵌入）
  set ${DOLLAR}upstream_frontend_topo_embed "http://frontend-module-topo_embed";
  location /preview-topo {
    rewrite /preview-topo/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_frontend_topo_embed;
  }

  # 前端 - 新版topo图
  set ${DOLLAR}upstream_module_prtopo_editor "http://frontend-base-prtopo-editor";
  location /module-prtopo_editor {
    rewrite /module-prtopo_editor/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_module_prtopo_editor;
  }

  # 前端 - 新版topo图预览（嵌入）
  set ${DOLLAR}upstream_module_prtopo_embed "http://frontend-base-prtopo-embed";
  location /preview-prtopo {
    rewrite /preview-prtopo/(.+) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_module_prtopo_embed;
  }

  # 前端 - 基座
  set ${DOLLAR}upstream_frontend_module_base "http://frontend-module-base";
  location / {
    proxy_pass ${DOLLAR}upstream_frontend_module_base;
  }

  # 帆软
  set ${DOLLAR}upstream_backend_finereport "${UPSTREAM_FINEREPORT}";
  location /webroot {
    proxy_pass ${DOLLAR}upstream_backend_finereport;
  }

  # office文件预览
  set ${DOLLAR}upstream_backend_file_preview "${UPSTREAM_FILE_PREVIEW}";
  location /preview {
    proxy_connect_timeout 60s;
    client_max_body_size 150m;
    proxy_pass ${DOLLAR}upstream_backend_file_preview;
  }

  # 平台授权
  set ${DOLLAR}upstream_service_authorization "http://service-authorization:22260";
  location /supa-service-auth {
    rewrite /supa-service-auth/(.*) /${DOLLAR}1 break;
    proxy_pass ${DOLLAR}upstream_service_authorization;
  }

  # Swagger 禁用
  location ~* (?:\/swagger|\/druid) { return 404; }
  location ^~ /api/doc { return 404; }
  location ^~ /mes-biz/doc { return 404; }
  location ^~ /workflow/doc { return 404; }
  #location ^~ /interface/swagger { return 404; }
  #location ^~ /dataroute/swagger { return 404; }
  #location ^~ /api/datawarn/swagger { return 404; }
  #location ^~ /converterMonitor/swagger { return 404; }

}
