version: "3.5"

x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mos/.env
  healthcheck:
    test: ["CMD","nginx","-t"]

x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "./config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mos/.env

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  data-monitor-image:
    external: true
  nacos-log:
    external: true


services:
  # 网关
  frontend-nginx-gateway:
    <<: *nginx-defaults
    #image: harbor.qdbdtd.com/middleware/nginx:${NGINX_VERSION}
    image: harbor.qdbdtd.com/middleware/nginx:1.24.0
    command: /bin/sh -c "DOLLAR=$$ envsubst < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf && nginx -g 'daemon off;'"
    environment:
      - UPSTREAM_CMS=${UPSTREAM_CMS}
      - UPSTREAM_GIS_API=${UPSTREAM_GIS_API}
      - UPSTREAM_GIS_STATIC=${UPSTREAM_GIS_STATIC}
      - UPSTREAM_GIS_THREE=${UPSTREAM_GIS_THREE}
      - UPSTREAM_FINEREPORT=${UPSTREAM_FINEREPORT}
    volumes:
      - "./config/nginx.conf.d/:/etc/nginx/conf.d/localhost.etc/:ro"
      - "./config/nginx.conf:/etc/nginx/conf.d/default.conf.template:ro"
      - "data-monitor-image:/usr/share/nginx/html/topo/images:ro"
      - "./static:/usr/share/nginx/html/static:ro"
      - "/home/<USER>/file_sync/archive:/usr/share/nginx/archive:ro"
    ports:
      - "${PORT_FRONTEND}:80"

  # nacos
  nacos:
    <<: *backend-defaults
    image: harbor.qdbdtd.com/middleware/nacos-pg:v2.1.0
    container_name: nacos-standalone-pg
    environment:
      - PREFER_HOST_MODE=hostname
      - MODE=standalone
      - SPRING_DATASOURCE_PLATFORM=postgresql
      - PG_SERVICE_HOST=postgres
      - PG_SERVICE_PORT=${POSTGRES_PORT}
      - PG_SERVICE_PASSWORD=${POSTGRES_PASSWORD}
      - PG_SERVICE_USER=${POSTGRES_USERNAME}
      - PG_SERVICE_DB_NAME=nacos
    volumes:
      - "nacos-log:/home/<USER>/logs"
      #- ./init.d/custom.properties:/home/<USER>/init.d/custom.properties
    #ports:
    #  - "${PORT_NACOS}:8848"
    #  - "${PORT_GRPC_NACOS}:9848"
    #  - "9555:9555"

  # 前端 - 基座项目
  frontend-module-base:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-frontend:${FRONTEND_BASE_VERSION}

  # 前端 - 系统设置
  frontend-module-system_admin:
    <<: *nginx-reset
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-system-admin:${FRONTEND_SYSADMIN_VERSION}

  # 前端 - Bladex Saber
  frontend-saber:
    <<: *nginx-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/bladex_saber:${FRONTEND_BLADEX_VERSION}
    environment:
      - BACKEND_ENDPOINT=${BLADEX_BACKEND_ENDPOINT}
      #- BACKEND_DATA_MONITOR=${BACKEND_DATA_MONITOR}
    ports:
      - ${PORT_FRONTEND_SABER}:80

  # 前端 - 预警报警
  frontend-module-alarm_serve:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com/bjproduct/platform/front-services/alarm_serve:${FRONTEND_ALARM_VERSION}

  # 后端 - BladeX 用户权限
  backend-process-user-permission:
    <<: *backend-defaults
    container_name:  backend-platform
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/bladex/bladex_boot:${BACKEND_BLADEX_VERSION}
    environment:
      #- BLADEX_POSTGRES_DATABASE=${BLADEX_POSTGRES_DATABASE}
      #- BLADEX_MONGO_DATABASES=${BLADEX_MONGO_DATABASES}
      #- BLADEX_MINIO_BUCKET_NAME=${BLADEX_MINIO_BUCKET_NAME}
      #- AUTH_PATH=${AUTH_PATH}
      #- POINT_URL=${POINT_URL}
      #- AUTH_ENABLE=${AUTH_ENABLE}
      - AUTH_SERVICE_NAME=${AUTH_SERVICE_NAME}
      - AUTH_PROJECT_NAME=${AUTH_PROJECT_NAME}
      - AUTH_SERVICE_ID=${AUTH_SERVICE_ID}
      - AUTH_PROJECT_ID=${AUTH_PROJECT_ID}
    #ports:
    #  - "${PORT_BASE_BACKEND}:9401"

  # 授权后端
  backend-service-authorization:
    <<: *backend-defaults
    container_name: service-authorization
    privileged: true
    image: harbor.qdbdtd.com/bjproduct/platform/services/auth_client:${AUTH_CLIENT_VERSION}
    environment:
      - PUBLIC_SERVICE_PATH=${PUBLIC_SERVICE_PATH}
      - PUBLIC_SERVICE_PORT=${PUBLIC_SERVICE_PORT}
    volumes:
      - "./project-data/licenceRepository:/home/<USER>/licenceRepository"
      - /sbin/dmidecode:/sbin/dmidecode
      - /dev/mem:/dev/mem
      #- /etc/sysconfig/network-scripts:/tmp/info
      - /etc/NetworkManager/system-connections:/tmp/info
      - ./init.sh:/home/<USER>/init.sh
    ports:
      - "${PORT_AUTH_BACKEND}:22260"
      #- "14148:22260"

  # 消息推送服务
  backend-push-msg:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/push-msg:${BACKEND_PUSH_MSG}
    environment:
      # 无效化发送短信, SMS_URL 中加了异常字符串 111
      - SMS_URL=${SMS_URL:http://*************:8080/smg/webService/smsOper111?wsdl}
      - SMS_USERNAME=${SMS_USERNAME:zdyjpt}
      - SMS_PASSWORD=${SMS_PASSWORD:zdyjpt123}
      - PUSH_COMPANY=${PUSH_COMPANY}
      - HISTORY_SAVE_MODE=${HISTORY_SAVE_MODE:influxdb}
      - ALARM_TIME_INTERVAL=${ALARM_TIME_INTERVAL:1}
      - ALARM_LINKAGE_PHONES=${ALARM_LINKAGE_PHONES:10054799573}
      - MSSQL_URL=${ALARM_LINKAGE_DB_URL:***********************************************************************************************}
      # 无效化联动, 用户名 sa 后面加了 xxxx
      - MSSQL_USERNAME=${ALARM_LINKAGE_DB_USER:saxxxx}
      - MSSQL_PASSWORD=${ALARM_LINKAGE_DB_PASSWORD:KjfdU#183&}
    #ports:
    #  - "${PORT_PUSH_MSG}:9001"
