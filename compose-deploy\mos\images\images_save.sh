#!/bin/bash
SAVE_PATH=/root/images_save
count=`docker images | awk 'NR>=2{print}' | wc -l`
IMAGE_NAMES=(`docker images | awk 'NR>=2{print $1}'`)
IMAGE_TAGS=(`docker images | awk 'NR>=2{print $2}'`)

[ -d $SAVE_PATH ] || mkdir $SAVE_PATH 

for((i=0;i<$count;i++));
do
SOURCE="${IMAGE_NAMES[$i]}:${IMAGE_TAGS[$i]}"
TARGET=$SAVE_PATH/"`echo ${IMAGE_NAMES[$i]} | awk -F/ '{print $NF}'`_${IMAGE_TAGS[$i]}".tar
[ -f $TARGET ] || docker save $SOURCE -o $TARGET
done

