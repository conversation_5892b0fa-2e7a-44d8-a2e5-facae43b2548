/*
 Navicat Premium Data Transfer

 Source Server         : 204产品化2.0数据库
 Source Server Type    : PostgreSQL
 Source Server Version : 100011
 Source Host           : ***************:5433
 Source Catalog        : mes
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 100011
 File Encoding         : 65001

 Date: 01/09/2022 09:53:00
*/


-- ----------------------------
-- Table structure for gis_mine_dig_tube
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_mine_dig_tube";
CREATE TABLE "public"."gis_mine_dig_tube" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "dig_group_id" int4,
  "shape" int2,
  "material_type" int2,
  "tune_type" int2,
  "surface_id" int4,
  "state" int2 NOT NULL,
  "method" varchar(255) COLLATE "pg_catalog"."default",
  "shield" varchar(255) COLLATE "pg_catalog"."default",
  "high" varchar(255) COLLATE "pg_catalog"."default",
  "area" numeric,
  "design_length" numeric,
  "begin_time" timestamp(6),
  "end_time" timestamp(6),
  "order_id" int4,
  "already_length" numeric
)
;
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."name" IS '巷道名称';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."dig_group_id" IS '关联掘进队伍id';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."shape" IS '断面形状：1：矩形，2：半圆拱形，3：梯形，4：半梯形，5：圆弧拱形，6：三心拱形';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."material_type" IS '岩性类型：1.全煤，2.全岩，3.半煤，4.穿层';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."tune_type" IS '巷道类型：1.开拓，2.准备，3.回采';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."surface_id" IS '关联工作面表id';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."state" IS '巷道状态：1.准备，2.现掘，3.结束，4.巷修';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."method" IS '掘进方式：1.综掘，2.炮掘，3.掘修';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."shield" IS '支护方式：1. 锚杆，2.锚杆锚网，3.锚网所，4.U型棚，5.锚杆锚网喷浆，6.锚网索喷浆，7.锚网索+工钢棚，8.锚网索+U钢棚，9..锚网索梁+U钢+喷注，10.砌碹';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."high" IS '高度';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."area" IS '净断面积';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."design_length" IS '设计长度';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."begin_time" IS '开始时间';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."order_id" IS '排序';
COMMENT ON COLUMN "public"."gis_mine_dig_tube"."already_length" IS '已掘进米数基数';
COMMENT ON TABLE "public"."gis_mine_dig_tube" IS '掘进巷道信息表';
