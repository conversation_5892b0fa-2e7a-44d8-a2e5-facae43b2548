#!/bin/bash

# =============================================================================
# Hasura
# =============================================================================

## 检查 Hasura 是否上线
#check_hasura_up () {
#    cd $BASE_SERVICE_PATH
#    echo "正在等待graphql-engine启动"
#    TIMEOUT=0
#    while (( $TIMEOUT < 120 ))
#    do
#        docker run --network=${DOCKER_NETWORK} --rm -a stdout ${DOCKER_BUSY_BOX_IMAGE} sh -c "wget --quiet --tries=1 --spider http://graphql-engine:8080/healthz || exit 1"
#        if [ $? -eq 0 ]; then echo "" && break; fi
#        printf "."
#        TIMEOUT=$(( TIMEOUT+5 ))
#        sleep 5
#    done
#    if (( $TIMEOUT >= 120 )); then
#        echo_error "Migration 超时" && \
#        exit 1;
#    fi
#   cd $DEPLOY_PATH
#}

# =============================================================================
# start
# =============================================================================

start_base_service () {
  echo_yellow "启动基础服务"
  docker_compose_up $BASE_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_video_service () {
  echo_yellow "启动视频服务"
  docker_compose_up $VIDEO_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_mos_service () {
  echo_yellow "启动 MOS 服务"
  docker_compose_up $MOS_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_iot_service () {
  echo_yellow "启动 IOT 服务"
  docker_compose_up $IOT_SERVICE_PATH
  # if [ $DEPLOY_IOT_SERVICE = true ]; then
  #     check_hasura_up;
  # fi
  cd $DEPLOY_PATH
}

start_mes_service () {
  echo_yellow "启动 MES 服务"
  docker_compose_up $MES_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_gis_service () {
  echo_yellow "启动 GIS 服务"
  docker_compose_up $GIS_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_app_service () {
  echo_yellow "启动 app 服务"
  docker_compose_up $APP_SERVICE_PATH
  cd $DEPLOY_PATH
}

start_workflow_service () {
  echo_yellow "启动 业务审批流 服务"
  docker_compose_up $WORK_FLOW_PATH
  cd $DEPLOY_PATH
}

# =============================================================================
# stop
# =============================================================================

stop_base_service () {
  echo_yellow "停止基础服务"
  docker_compose_stop $BASE_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_video_service () {
  echo_yellow "停止视频服务"
  docker_compose_stop $VIDEO_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_mos_service () {
  echo_yellow "停止 MOS 服务"
  docker_compose_stop $MOS_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_iot_service () {
  echo_yellow "停止 IOT 服务"
  docker_compose_stop $IOT_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_mes_service () {
  echo_yellow "停止 MES 服务"
  docker_compose_stop $MES_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_gis_service () {
  echo_yellow "停止 GIS 服务"
  docker_compose_stop $GIS_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_app_service () {
  echo_yellow "停止 app 服务"
  docker_compose_stop $APP_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_workflow_service () {
  echo_yellow "停止 业务审批流 服务"
  docker_compose_stop $WORK_FLOW_PATH
  cd $DEPLOY_PATH
}

# =============================================================================
# restart
# =============================================================================

restart_base_service () {
  echo_yellow "启动基础服务"
  docker_compose_restart $BASE_SERVICE_PATH
  cd $DEPLOY_PATH
}


restart_video_service () {
  echo_yellow "启动视频服务"
  docker_compose_restart $VIDEO_SERVICE_PATH
  cd $DEPLOY_PATH
}

restart_mos_service () {
  echo_yellow "启动 MOS 服务"
  docker_compose_restart $MOS_SERVICE_PATH
  cd $DEPLOY_PATH
}

restart_iot_service () {
  echo_yellow "启动 IOT 服务"
  docker_compose_restart $IOT_SERVICE_PATH
  # if [ $DEPLOY_IOT_SERVICE = true ]; then 
  #     check_hasura_up; 
  # fi
  cd $DEPLOY_PATH
}

restart_mes_service () {
  echo_yellow "启动 MES 服务"
  docker_compose_restart $MES_SERVICE_PATH
  cd $DEPLOY_PATH
}

restart_gis_service () {
  echo_yellow "启动 GIS 服务"
  docker_compose_restart $GIS_SERVICE_PATH
  cd $DEPLOY_PATH
}

restart_app_service () {
  echo_yellow "启动 app 服务"
  docker_compose_restart $APP_SERVICE_PATH
  cd $DEPLOY_PATH
}

restart_workflow_service () {
  echo_yellow "启动 业务审批流 服务"
  docker_compose_restart $WORK_FLOW_PATH
  cd $DEPLOY_PATH
}

# =============================================================================
# down
# =============================================================================

down_base_service () {
  echo_yellow "停止基础服务"
  docker_compose_down $BASE_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_video_service () {
  echo_yellow "停止视频服务"
  docker_compose_down $VIDEO_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_mos_service () {
  echo_yellow "停止 MOS 服务"
  docker_compose_down $MOS_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_iot_service () {
  echo_yellow "停止 IOT 服务"
  docker_compose_down $IOT_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_mes_service () {
  echo_yellow "停止 MES 服务"
  docker_compose_down $MES_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_gis_service () {
  echo_yellow "停止 GIS 服务"
  docker_compose_down $GIS_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_app_service () {
  echo_yellow "停止 app 服务"
  docker_compose_down $APP_SERVICE_PATH
  cd $DEPLOY_PATH
}

down_workflow_service () {
  echo_yellow "停止 业务审批流 服务"
  docker_compose_down $WORK_FLOW_PATH
  cd $DEPLOY_PATH
}


start_ssh_service () {
  echo_yellow "启动 SSH 服务"
  docker_compose_up $SSH_SERVICE_PATH
  cd $DEPLOY_PATH
}

stop_ssh_service () {
  echo_yellow "停止 SSH 服务"
  docker_compose_down $SSH_SERVICE_PATH
  cd $DEPLOY_PATH
}

