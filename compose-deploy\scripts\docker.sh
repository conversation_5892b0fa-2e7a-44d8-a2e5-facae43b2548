#!/bin/bash

create_docker_network () {
  net_name=$1
  net_flag=$(docker network ls | grep -w "$net_name" | wc -l)
  if [ $net_flag -eq 0 ]; then
    docker network create $net_name
  fi
}

create_docker_volume () {
  vol_name=$1
  vol_flag=`docker volume ls -f name=$vol_name | grep -w $vol_name\$ | wc -l`
  if [ $vol_flag -eq 0 ]; then
    docker volume create --name=$vol_name
  fi
}

remove_docker_network () {
  net_name=$1
  net_flag=`docker network ls | grep -w $net_name | wc -l`
  if [ $net_flag -ne 0 ]; then
    docker network rm $net_name
  fi
}

remove_docker_volume () {
  vol_name=$1
  vol_flag=`docker volume ls -f name=$vol_name | grep -w $vol_name\$ | wc -l`
  if [ $vol_flag -ne 0 ]; then
    docker volume rm $vol_name
  fi
}

backup_docker_volume () {
  vol_name=$1
  echo_yellow "备份 $vol_name"
  vol_flag=`docker volume ls -f name=$vol_name | grep -w $vol_name\$ | wc -l`
  if [ $vol_flag -eq 1 ]; then
    backup_file_name=$vol_name.$(date "+%H.%M.%S").tgz
    docker run --rm \
    --mount source=$vol_name,target=/backup_source,readonly \
    -v $BACKUP_PATH:/backup_dist \
    ${DOCKER_BUSY_BOX_IMAGE} tar zcvf /backup_dist/$backup_file_name /backup_source
  else
    echo_error $vol_name "备份失败"
  fi
}

# CAREFUL: THIS IS NOT A REAL RESORE
restore_docker_volume () {
  vol_name=$1
  restore_from=$2
  docker run -i --rm --name restore_helper \
  --mount source=$vol_name,target=/restore_target \
  -v $restore_from:/restore_source/backup.tgz \
  ${DOCKER_BUSY_BOX_IMAGE} tar zxvf /restore_source/backup.tgz --directory /restore_target/ --strip 1
}

pull_docker_image () {
  docker compose pull && return 0 || return 1
}

docker_compose_pull_with_retry () {
  for i in {1..3}
  do
    pull_docker_image
    if [ $? -eq 0 ]; then
      break
    else
      if [ "$i" = 3 ]; then
        echo_error 拉取镜像失败 && return 1
      fi
    fi
  done
}

docker_compose_up () {
  if [ -z "$1" ]; then
    echo_error "docker compose 缺少路径参数" && return 1
  fi
  cd $1 && \
  # docker_compose_pull_with_retry && \
  docker compose --compatibility up -d
}

docker_compose_restart () {
  if [ -z "$1" ]; then
    echo_error "docker compose 缺少路径参数" && return 1
  fi
  cd $1 && \
  # docker_compose_pull_with_retry && \
  docker compose --compatibility restart
}

docker_compose_stop () {
  if [ -z "$1" ]; then
    echo_error "docker compose 缺少路径参数" && return 1
  fi
  cd $1 && docker compose --compatibility stop
}

docker_compose_down () {
  if [ -z "$1" ]; then
    echo_error "docker compose 缺少路径参数" && return 1
  fi
  cd $1 && docker compose --compatibility down
}

init_docker () {

  echo "vm.swappiness=0">>/etc/sysctl.conf
  sysctl -p

  # Harbor Login
  echo_yellow "正在登录 Harbor"
  $DEPLOY_PATH/harbor-login-deploy.sh.x & \

  # Create Docker Network
  echo_yellow "正在创建 Docker 网络"
  for network_name in ${DOCKER_NETWORKS[@]}; do
    create_docker_network $network_name
  done

  # Create Docker Named Volumes
  echo_yellow "正在创建 Docker 命名卷"
  for volume_name in ${DOCKER_VOLUMES[@]}; do
    create_docker_volume $volume_name
  done
}

prune_docker () {
  echo_yellow "正在删除 Docker 命名卷"
  for volume_name in ${DOCKER_VOLUMES[@]}; do
    remove_docker_volume $volume_name
  done

  echo_yellow "正在删除 Docker 网络"
  for network_name in ${DOCKER_NETWORKS[@]}; do
    remove_docker_network $network_name
  done
}
