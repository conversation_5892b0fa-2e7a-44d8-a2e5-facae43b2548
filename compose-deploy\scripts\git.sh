#!/bin/bash

git_update () {
    tag=$1
    if [ -z "$tag" ]; then
        echo_error "git checkout 缺少版本号" && return 1
    fi
    echo "[GIT]: 正在更新部署材料..."
    git fetch
    found_tag=`git tag -l $tag | wc -l`
    if [ $found_tag -eq 0 ]; then
        echo_error "无法找到版本，请联系管理员" && return 1
    fi
    git_stash=0
    git diff-index --quiet HEAD --
    if [ $? -ne 0 ]; then
        echo_yellow "[GIT]: 检测到本地文件修改"
        git stash
        git_stash=1
        echo_ok "git stash 成功"
    fi
    git checkout $tag
    if [ $? -eq 0 ]; then
        echo_ok "git checkout 成功"
        if [ $git_stash -eq 1 ]; then
            git stash pop
            if [ $? -eq 0 ]; then
                echo_ok "git stash pop 成功"
                return 0
            else
                echo_error "git stash pop 失败，请联系管理员" && return 1
            fi
        fi
    else
        git stash pop
        echo_error "git checkout 失败，请联系管理员" && return 1
    fi
}

