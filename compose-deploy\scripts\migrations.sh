start_hasura_console () {
  docker run --rm -it --name hasura-console \
    -p "9999-10000:9999-10000" \
    --entrypoint="" -v $BASE_SERVICE_PATH/hasura:/app --workdir=/app \
    -e HASURA_GRAPHQL_CONSOLE_ASSETS_DIR=/srv/console-assets \
    harbor.qdbdtd.com/middleware/hasura/graphql-engine:v1.3.2.cli-migrations-v2 \
    hasura-cli console \
    --address 0.0.0.0 --console-port 9999 --api-port 10000 --skip-update-check \
    --endpoint $1
}
