#! /bin/bash

psqlGetSchema(){
  local psql=$1
  if [[ $psql =~ search_path%3d(.*)$ ]]; then
    echo "${BASH_REMATCH[1]}"
  fi
}

psqlCheckConn(){
  local psql="$1"
  $psql -c "select 1" 1>/dev/null 2>&1
  return $?
}

tssqlCheckConn(){
  local tssql="$1"
  $tssql -s "show users;" 1>/dev/null 2>&1
  return $?
}

tssqlImport(){
  local tssql="$1"
  $tssql -s "source /home/<USER>/init-tsdb.sql;" 1>/dev/null 2>&1
  return $?
}

tssqlCheckInit(){
  local curVer=$(docker exec -i tdengine taos -h tdengine -uroot -pbdtdtd@123 -s "select today() from log.log;" 2>&1)
  if [[ "$curVer" == *"(Query OK)"* ]]; then
    echo "DEBUG: 时序初始数据库导入成功" > /dev/tty
  elif [[ "$curVer" == *"error"* ]]; then
    echo "ERROR: 脚本异常退出,错误原因: $curVer" > /dev/tty
    exit 1
  else
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/ //g')
    echo "$curVer"
  fi
}

psqlCheckSchema(){
  local psql="$1"
  local schema=$(psqlGetSchema "$1")
  local sqlRes
  sqlRes=$($psql -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = '$schema';")
  if [[ "$sqlRes" == *"(0 rows)"* ]]; then
    echo "ERROR: 数据库中不存在schema=$schema!!"
    exit 1
  fi
}


psqlGetCurVer(){
  local psql=$1
  local schema=$(psqlGetSchema "$1")
  local checkExist=$($psql -c "select 1  from information_schema.tables where table_schema='$schema' and table_name='databasechangelog'" 2>&1)
  if [[ "$checkExist" == *"(0 rows)"* ]]; then
    return
  fi
  local curVer=$($psql -c "select tag from ${schema}.databasechangelog order by dateexecuted desc limit 1" 2>&1)
  if [[ "$curVer" == *"(0 rows)"* ]]; then
    echo "DEBUG: 未找到databasechangelog表中的记录" > /dev/tty
  elif [[ "$curVer" == *"error"* ]]; then
    echo "ERROR: 脚本异常退出,错误原因: $curVer" > /dev/tty
    exit 1
  else
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/ //g')
    echo "$curVer"
  fi
}


psqlCreateCvsTable(){
  local psql=$1
  local schema=$(psqlGetSchema "$1")
    $psql << EOF
CREATE TABLE IF NOT EXISTS $schema."databasechangelog" (
  "id" varchar(255) NOT NULL,
  "author" varchar(255) NOT NULL,
  "filename" varchar(255),
  "dateexecuted" timestamp(6) NOT NULL,
  "orderexecuted" int4 NOT NULL,
  "exectype" varchar(10),
  "md5sum" varchar(35),
  "description" varchar(255),
  "comments" varchar(255),
  "tag" varchar(255),
  "liquibase" varchar(20),
  "contexts" varchar(255),
  "labels" varchar(255),
  "deployment_id" varchar(10)
);
CREATE TABLE IF NOT EXISTS $schema."databasechangeloglock" (
  "id" int4 NOT NULL,
  "locked" bool NOT NULL,
  "lockgranted" timestamp(6),
  "lockedby" varchar(255),
  CONSTRAINT "databasechangeloglock_pkey" PRIMARY KEY ("id")
);
EOF
}




psqlMarkVersion(){
  local psql=$1 version=$2
  local schema=$(psqlGetSchema "$1")
  $psql -c "insert into $schema.databasechangelog(id,author,dateexecuted,orderexecuted,tag) values('$version','auto_deploy','$(date '+%Y-%m-%d %H:%M:%S')',1,'$version');"
}


psqlExecSqlFile(){
  local psql=$1 sqlFile=$2
  echo "INFO: 正在执行SQL文件:$sqlFile"
  local sqlErr=$($psql < $sqlFile 2>&1 1>/dev/null)   #scan stderr only
  if [[ $sqlErr == *ERROR:* ]]; then
    echo "ERROR: 脚本异常退出,错误原因 : $sqlErr"
    exit 1
  fi
}

psqlExecSql(){
  local psql=$1 sql=$2
  $psql -c "$sql" 2>&1
}

