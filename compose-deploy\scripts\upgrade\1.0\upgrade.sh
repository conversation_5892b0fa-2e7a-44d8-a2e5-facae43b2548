#!/bin/bash
# 注意：请在完全知晓以下脚本行为的情况下执行

# Vars
MIGRATION_TEMP_PATH=/tmp/bdtdcenter/migration
DOCKER_BUSY_BOX_IMAGE=harbor.qdbdtd.com/middleware/busybox:1.32

# Utils

create_volume () {
    vol_name=$1
    vol_flag=`docker volume ls -f name=$vol_name | grep -w $vol_name\$ | wc -l`
    if [ $vol_flag -eq 0 ]; then
        docker volume create --name=$vol_name
    fi
}

backup_volume () {
    vol_name=$1
    vol_path=$2
    echo $1 $2
    echo "备份 $vol_name"
    backup_file_name=$vol_name.tar
    docker run --rm \
    -v $vol_path:/backup_source:ro \
    -v $MIGRATION_TEMP_PATH:/backup_dist \
    ${DOCKER_BUSY_BOX_IMAGE} \
    tar cf /backup_dist/$backup_file_name /backup_source
}

restore_volume () {
    vol_name=$1
    restore_from=$2
    docker run -i --rm --name restore_helper \
    --mount source=$vol_name,target=/restore_target \
    -v $restore_from:/restore_source/backup.tar \
    ${DOCKER_BUSY_BOX_IMAGE} tar xf /restore_source/backup.tar --directory /restore_target/ --strip 1
}

cbr () {
  vol_name=$1
  vol_path=$2
  backup_file_path=$MIGRATION_TEMP_PATH/$1.tar
  create_volume $vol_name
  backup_volume $vol_name $vol_path
  restore_volume $vol_name $backup_file_path
}

# Main

mkdir -p $MIGRATION_TEMP_PATH


# Base
create_volume 'base-rabbitmq'
cbr 'base-red-node' '/data/red-node-data'
cbr 'base-redis' '/data/mos/redis'
cbr 'base-postgres' '/data/mos/postgresql'
cbr 'base-influxdb' '/data/mos/influxdb'

# MOS
cbr 'data-monitor-image' '/data/mos/data-monitor/images'
cbr 'user-auth-db' '/data/mos/mysql'
cbr 'data-monitor-log' '/data/mos/data-monitor/logs/logs'
cbr 'data-storage-log' '/data/mos/data-storage/logs'
cbr 'data-warn-log' '/data/mos/data-warn/logs'
cbr 'converter-monitor-log' '/data/mos/converter-monitor/logs'
cbr 'gis-biz-log' '/data/mos/gis-biz/logs'
cbr 'mes-biz-log' '/data/mos/mes-biz/logs'

# Video
cbr 'video-redis' '/data/video-server/video-redis'

#Tool
cbr 'tool-portainer-data' '/data/tool/portainer/data'
cbr 'tool-grafana-data' '/data/tool/grafana/data'
cbr 'tool-prometheus-data' '/data/tool/prometheus/data'
