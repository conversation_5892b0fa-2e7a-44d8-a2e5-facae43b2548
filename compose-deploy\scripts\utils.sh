#!/bin/bash

# Path
# DEPLOY_PATH read from ../auto_deploy.sh
BASE_SERVICE_PATH=$DEPLOY_PATH/base
MOS_SERVICE_PATH=$DEPLOY_PATH/mos
IOT_SERVICE_PATH=$DEPLOY_PATH/iot
WORK_FLOW_PATH=$DEPLOY_PATH/workflow
MES_SERVICE_PATH=$DEPLOY_PATH/mes
GIS_SERVICE_PATH=$DEPLOY_PATH/gis
SSH_SERVICE_PATH=$DEPLOY_PATH/ssh
APP_SERVICE_PATH=$DEPLOY_PATH/app
VIDEO_SERVICE_PATH=$DEPLOY_PATH/video

#BACKUP_DB_PATH=$BACKUP_BASE_PATH/db

# Minimum requirements...
MIN_DOCKER_VERSION='20.10.14'
MIN_COMPOSE_VERSION='1.27.4'
MIN_RAM=6144 # MB

# Docker networks
DOCKER_NETWORKS=("$DOCKER_NETWORK")

# Docker images (helper)
DOCKER_BUSY_BOX_IMAGE=harbor.qdbdtd.com/middleware/busybox:1.32

# Docker named volumes
DOCKER_VOLUMES=("base-red-node" "base-redis" "base-postgres" "base-influxdb" "base-rabbitmq"  "base-mysql"  "base-tdengine-data" \
"base-tdengine-log" "base-business-postgres" "bladex-minio" "data-monitor-image" "data-monitor-log" "data-storage-log" \
"data-warn-log" "converter-monitor-log" "gis-biz-log" "mes-biz-log" "video-redis" "base-workflow"  "base-ftp" "nacos-log")

# Docker volumes to backup
DOCKER_BACKUP_VOLUMES=("data-monitor-image" "base-red-node" "base-redis" "video-redis" "base-tdengine-data" \
"base-postgres" "bladex-minio" "base-business-postgres" "base-ftp")

# Colors 
COLOR_NONE='\033[0m'
COLOR_RED='\033[0;31m'
COLOR_GREEN='\033[0;32m'
COLOR_BLUE='\033[1;34m'
COLOR_YELLOW='\033[1;33m'

# Utils
echo_error () {
  echo -e $COLOR_RED"[ERROR]: "$1$COLOR_NONE
}

echo_ok () {
  echo -e $COLOR_GREEN"[OK]: "$1$COLOR_NONE
}

echo_yellow () {
  echo -e $COLOR_YELLOW$1$COLOR_NONE
}

# Compare dot-separated strings - function below is inspired by https://stackoverflow.com/a/37939589/808368
function ver () { echo "$@" | awk -F. '{ printf("%d%03d%03d", $1,$2,$3); }'; }

index_of(){
  local value="$1"
  shift
  local arr=("$@")
  for i in "${!arr[@]}"; do
   if [[ "${arr[$i]}" = "${value}" ]]; then
     echo "$i"
     return 0
   fi
  done
  echo -1
}

compare_version(){
  local a=$1 b=$2
  if [[ "$a" == "$b" ]]; then
  echo 0
  return
  fi
  local arr=("$@")
  IFS=$'\n'
  sorted=($(sort -V <<<"${arr[*]}"))
  unset IFS
  for i in "${!sorted[@]}"; do
   if [[ "${sorted[$i]}" = "$a" ]]; then
     local ai="$i"
   elif  [[ "${sorted[$i]}" = "$b" ]]; then
     local bi="$i"
   fi
  done
  if [[ ai -gt bi ]]; then
  echo 1
  else
   echo -1
  fi
}

check_system () {
  echo_yellow "正在检查系统"
  DOCKER_VERSION=$(docker version --format '{{.Server.Version}}')
  COMPOSE_VERSION=$(docker compose --no-ansi --version | sed 's/docker compose version \(.\{1,\}\),.*/\1/')
  RAM_AVAILABLE_IN_DOCKER=$(docker run --rm $DOCKER_BUSY_BOX_IMAGE free -m 2>/dev/null | awk '/Mem/ {print $2}');

  if [[ "$(ver $DOCKER_VERSION)" -lt "$(ver $MIN_DOCKER_VERSION)" ]]; then
  echo_error "Expected minimum Docker version to be $MIN_DOCKER_VERSION but found $DOCKER_VERSION"
  echo_yellow "是否继续？（输入 yes 继续）"
  read; if [[ $REPLY != yes ]]; then exit 1; fi
  fi

  if [[ "$(ver $COMPOSE_VERSION)" -lt "$(ver $MIN_COMPOSE_VERSION)" ]]; then
  echo_error "Expected minimum docker compose version to be $MIN_COMPOSE_VERSION but found $COMPOSE_VERSION"
  echo_yellow "是否继续？（输入 yes 继续）"
  read; if [[ $REPLY != yes ]]; then exit 1; fi
  fi

  if [[ "$RAM_AVAILABLE_IN_DOCKER" -lt "$MIN_RAM" ]]; then
  echo_error "Expected minimum RAM available to Docker to be $MIN_RAM MB but found $RAM_AVAILABLE_IN_DOCKER MB"
  echo_yellow "是否继续？（输入 yes 继续）"
  read; if [[ $REPLY != yes ]]; then exit 1; fi
  fi
}

check_mine_code () {
  echo_yellow "正在检查矿 ID"
  echo "矿 ID:" $MINECODE
  if [ $DEPLOY_MOS_SERVICE = false ]; then return 0; fi
  [[ $MINECODE =~ ^[0-9]{6}$ ]] && return 0 || return 1
}

check_cms_upstream () {
  echo_yellow "正在检查 CMS Upstream"
  echo "CMS Upstream:" $UPSTREAM_CMS
  curl -s $UPSTREAM_CMS > /dev/null && return 0 || return 1
}

check_auth_service () {
  echo_yellow "正在检查 授权服务名称 AUTH_SERVICE_NAME, AUTH_PROJECT_NAME"
  if [ -z "$AUTH_SERVICE_NAME" ] || [ -z "$AUTH_PROJECT_NAME" ]; then return 1; fi
  return 0
}

check_config_env () {
  check_mine_code
  if [ $? -ne 0 ]; then
    echo_error "矿 ID 输入错误，应为 6 位数字，请与管理员沟通在 CMS 系统中创建新矿编码"
    exit 1
  fi

  # # check_cms_upstream
  # if [ $? -ne 0 ]; then
  #   echo_error "CMS Upstream 连接失败，请检查是否配置是否正确. $UPSTREAM_CMS"
  #   exit 1
  # fi

  # 授权检查
  check_auth_service
  if [ $? -ne 0 ]; then
    echo_error "缺少授权配置信息，请联系管理员在CMS中配置. AUTH_SERVICE_NAME, AUTH_PROJECT_NAME"
    exit 1
  fi
}
