# Services 目录

本目录包含了综合管控平台的所有微服务组件，按照功能模块进行组织。

## 目录结构

```
services/
├── base/           # 基础服务（数据库、消息队列、缓存等）
├── mos/            # MOS基础服务（用户权限管理）
├── iot/            # IOT平台服务（数据接入、预警报警、Topo）
├── gis/            # GIS业务服务
├── mes/            # MES业务服务
├── video/          # 视频服务
├── workflow/       # 业务审批流服务
├── app/            # 移动应用服务
├── ssh/            # SSH服务
└── tool/           # 监控工具服务（Grafana、Prometheus等）
```

## 服务说明

### base - 基础服务

包含系统运行所需的基础组件：
- PostgreSQL 数据库
- Redis 缓存
- RabbitMQ 消息队列
- InfluxDB 时序数据库
- MinIO 对象存储
- TDengine 时序数据库

### mos - MOS基础服务

提供用户权限管理和基础业务功能：
- 用户认证授权
- 权限管理
- 基础数据管理
- Nacos 配置中心

### iot - IOT平台服务

物联网数据接入和处理：
- 数据接入系统
- 预警报警消息推送
- Topo 拓扑图管理
- Hasura GraphQL API

### gis - GIS业务服务

地理信息系统相关功能：
- GIS 前端服务
- GIS 后端业务逻辑

### mes - MES业务服务

制造执行系统：
- MES 前端界面
- MES 后端业务逻辑

### video - 视频服务

视频监控和流媒体服务：
- 视频流处理
- SRS 流媒体服务器

### workflow - 业务审批流

工作流引擎和审批流程：
- 工作流引擎
- 审批流程管理

### app - 移动应用服务

移动端应用支持服务

### ssh - SSH服务

远程访问和管理服务

### tool - 监控工具

系统监控和运维工具：
- Grafana 监控面板
- Prometheus 指标收集
- Portainer 容器管理

## 部署说明

每个服务目录都包含：
- `docker-compose.yml` - Docker Compose 配置文件
- `config/` - 配置文件目录（如果需要）
- `README.md` - 服务说明文档（如果有）

使用 `auto_deploy.sh` 脚本进行统一部署和管理。
