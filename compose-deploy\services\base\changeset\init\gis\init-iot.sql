-- ----------------------------------------------------------
-- 数据接入4个项目用到的表导出的数据结构
-- ----------------------------------------------------------

CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_raster;
CREATE EXTENSION IF NOT EXISTS LTREE;

CREATE OR REPLACE FUNCTION ltree_from_str(varchar) RETURNS ltree AS
$$
SELECT ltree_in($1::cstring);
$$ LANGUAGE SQL IMMUTABLE;

CREATE OR REPLACE FUNCTION public.updated_timestamp()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
begin
    new.updated_at = current_timestamp;
return new;
end
$function$
;

CREATE OR REPLACE FUNCTION public.update_geolocation_area()
 R<PERSON>URNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  tables varchar[] := ARRAY['safety_supervision', 'location_identifiers', 'broadcast', 'video_channel', 'device', 'dispatch'];
  t varchar;
  BEGIN
    IF NEW.geometry_polygon is NULL THEN
      FOREACH t IN ARRAY tables
      LOOP
        EXECUTE format('UPDATE %I set geolocation_area_id = NULL where geolocation_area_id = %L', t, NEW.id);
      END LOOP;
    ELSE
      FOREACH t IN ARRAY tables
      LOOP
        EXECUTE format('UPDATE %1$I as s set geolocation_area_id = d.d_id from (select %1$I.id as s_id, geolocation_area.id as d_id from %1$I CROSS JOIN geolocation_area where st_contains(geolocation_area.geometry_polygon, %1$I.geometry_point) and geolocation_area.id = %2$L) as d where s.id=d.s_id', t, NEW.id);
      END LOOP;
    END IF;
    RETURN NEW;
  END;
$function$
;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sex_enum') THEN
    CREATE TYPE "public"."sex_enum" AS ENUM ( '男', '女' );
    ALTER TYPE "public"."sex_enum" OWNER TO "postgres";
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'video_device_type_enum') THEN
    CREATE TYPE "public"."video_device_type_enum" AS ENUM ('hikvision', 'dahua', 'hxtx');
    ALTER TYPE "public"."video_device_type_enum" OWNER TO "postgres";
  END IF;
END$$;

CREATE OR REPLACE FUNCTION "public"."updated_timestamp"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
begin
    new.updated_at = current_timestamp;
    return new;
end
$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

CREATE OR REPLACE FUNCTION "public"."set_current_timestamp_updated_at"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
DECLARE
  _new record;
BEGIN
  _new := NEW;
  _new."updated_at" = NOW();
  RETURN _new;
END;
$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

-- ----------------------------
-- Table structure for geolocation_area
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area" CASCADE;
CREATE TABLE "public"."geolocation_area" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "color" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_polygon" "public"."geometry",
  "mining_area_id" int4,
  "order" int4,
  "topoId" text COLLATE "pg_catalog"."default",
  "topoName" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."geolocation_area"."id" IS '自增ID';
COMMENT ON COLUMN "public"."geolocation_area"."name" IS '区域名称';
COMMENT ON COLUMN "public"."geolocation_area"."type" IS '区域类型';
COMMENT ON COLUMN "public"."geolocation_area"."color" IS '区域颜色';
COMMENT ON COLUMN "public"."geolocation_area"."geometry_polygon" IS '区域坐标';
COMMENT ON COLUMN "public"."geolocation_area"."mining_area_id" IS '采区ID';
COMMENT ON COLUMN "public"."geolocation_area"."order" IS '权重';
COMMENT ON TABLE "public"."geolocation_area" IS '区域定义表';

-- ----------------------------
-- Records of geolocation_area
-- ----------------------------

-- ----------------------------
-- Table structure for geolocation_area_device_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area_device_map" CASCADE;
CREATE TABLE "public"."geolocation_area_device_map" (
  "id" SERIAL NOT NULL,
  "geolocation_area_id" int4 NOT NULL,
  "device_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "point_id" text COLLATE "pg_catalog"."default",
  "control_point_id" text COLLATE "pg_catalog"."default",
  "control_value" int4
)
;
COMMENT ON COLUMN "public"."geolocation_area_device_map"."device_name" IS '设备的显示名称';
COMMENT ON COLUMN "public"."geolocation_area_device_map"."point_id" IS '设备监测值的测点ID';
COMMENT ON COLUMN "public"."geolocation_area_device_map"."control_point_id" IS '设备联动的控制点ID(通常为断电)';
COMMENT ON COLUMN "public"."geolocation_area_device_map"."control_value" IS '设备联动时给控制点发送的控制值(通常为断电)';
COMMENT ON TABLE "public"."geolocation_area_device_map" IS '区域内可控的(电力)设备配置表';

-- ----------------------------
-- Records of geolocation_area_device_map
-- ----------------------------

-- ----------------------------
-- Table structure for geolocation_area_escape_route_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area_escape_route_map" CASCADE;
CREATE TABLE "public"."geolocation_area_escape_route_map" (
  "id" SERIAL NOT NULL,
  "geolocation_area_id" int4 NOT NULL,
  "escape_route_id" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."geolocation_area_escape_route_map"."id" IS '自增ID';
COMMENT ON COLUMN "public"."geolocation_area_escape_route_map"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."geolocation_area_escape_route_map"."escape_route_id" IS '避灾路线ID';
COMMENT ON TABLE "public"."geolocation_area_escape_route_map" IS '区域避灾路线映射表';


-- ----------------------------
-- Table structure for geolocation_area_safety_supervision_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area_safety_supervision_map" CASCADE;
CREATE TABLE "public"."geolocation_area_safety_supervision_map" (
  "id" SERIAL NOT NULL,
  "geolocation_area_id" int4 NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "duration" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_map"."id" IS '自增ID';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_map"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_map"."point_id" IS '测点编号';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_map"."duration" IS '时长';
COMMENT ON TABLE "public"."geolocation_area_safety_supervision_map" IS '区域重点监测映射表（安全监控系统）';

-- ----------------------------
-- Records of geolocation_area_safety_supervision_map
-- ----------------------------

-- ----------------------------
-- Table structure for geolocation_area_safety_supervision_type_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area_safety_supervision_type_map" CASCADE;
CREATE TABLE "public"."geolocation_area_safety_supervision_type_map" (
  "id" SERIAL NOT NULL,
  "geolocation_area_id" int4 NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "duration" int4 NOT NULL,
  "upper_threshold" float8,
  "lower_threshold" float8
)
;
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."id" IS '自增ID';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."type" IS '传感器类型';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."duration" IS '时长';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."upper_threshold" IS '阈值上限';
COMMENT ON COLUMN "public"."geolocation_area_safety_supervision_type_map"."lower_threshold" IS '阈值下限';
COMMENT ON TABLE "public"."geolocation_area_safety_supervision_type_map" IS '区域类型聚合统计映射表（安全监控系统）';

-- ----------------------------
-- Records of geolocation_area_safety_supervision_type_map
-- ----------------------------

-- ----------------------------
-- Table structure for geolocation_area_ventilation_route_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."geolocation_area_ventilation_route_map" CASCADE;
CREATE TABLE "public"."geolocation_area_ventilation_route_map" (
  "id" SERIAL NOT NULL,
  "geolocation_area_id" int4 NOT NULL,
  "ventilation_route_id" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."geolocation_area_ventilation_route_map"."id" IS '自增ID';
COMMENT ON COLUMN "public"."geolocation_area_ventilation_route_map"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."geolocation_area_ventilation_route_map"."ventilation_route_id" IS '通风路线ID';
COMMENT ON TABLE "public"."geolocation_area_ventilation_route_map" IS '区域通风路线映射表';

-- ----------------------------
-- Records of geolocation_area_ventilation_route_map
-- ----------------------------

-- ----------------------------
-- Indexes structure for table geolocation_area
-- ----------------------------
CREATE INDEX "idx_geolocation_area_geometry_polygon" ON "public"."geolocation_area" USING gist (
  "geometry_polygon" "public"."gist_geometry_ops_2d"
);

-- ----------------------------
-- Triggers structure for table geolocation_area
-- ----------------------------
CREATE TRIGGER "update_geolocation_area_polygon" AFTER INSERT OR UPDATE OF "geometry_polygon" ON "public"."geolocation_area"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_geolocation_area"();

-- ----------------------------
-- Uniques structure for table geolocation_area
-- ----------------------------
ALTER TABLE "public"."geolocation_area" ADD CONSTRAINT "geolocation_area_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table geolocation_area
-- ----------------------------
ALTER TABLE "public"."geolocation_area" ADD CONSTRAINT "geolocation_area_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table geolocation_area_device_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_device_map" ADD CONSTRAINT "geolocation_area_device_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table geolocation_area_escape_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_escape_route_map" ADD CONSTRAINT "area_escape_route" UNIQUE ("geolocation_area_id", "escape_route_id");

-- ----------------------------
-- Primary Key structure for table geolocation_area_escape_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_escape_route_map" ADD CONSTRAINT "geolocation_area_escape_route_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table geolocation_area_safety_supervision_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_safety_supervision_map" ADD CONSTRAINT "geolocation_area_safety_supervision_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table geolocation_area_safety_supervision_type_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_safety_supervision_type_map" ADD CONSTRAINT "area_id_type" UNIQUE ("geolocation_area_id", "type");

-- ----------------------------
-- Primary Key structure for table geolocation_area_safety_supervision_type_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_safety_supervision_type_map" ADD CONSTRAINT "geolocation_area_safety_supervision_type_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table geolocation_area_ventilation_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_ventilation_route_map" ADD CONSTRAINT "area_ventilation_route" UNIQUE ("geolocation_area_id", "ventilation_route_id");

-- ----------------------------
-- Primary Key structure for table geolocation_area_ventilation_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_ventilation_route_map" ADD CONSTRAINT "geolocation_area_ventilation_route_map_pkey" PRIMARY KEY ("id");

CREATE OR REPLACE FUNCTION "public"."update_point"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  IF NEW.geometry_point is NULL THEN
    EXECUTE format('UPDATE %1$I set geolocation_area_id = NULL where %1$I.id = %2$L', TG_TABLE_NAME, NEW.id);
  ELSE
    EXECUTE format('UPDATE %1$I set geolocation_area_id = (SELECT geolocation_area.id from %1$I CROSS JOIN geolocation_area where %1$I.id = %2$L and ST_Contains(geolocation_area.geometry_polygon, %1$I.geometry_point) limit 1) where %1$I.id = %2$L', TG_TABLE_NAME, NEW.id);
  END IF;
  RETURN NEW;
END;
$BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

CREATE OR REPLACE FUNCTION "public"."update_geolocation_area"()
RETURNS "pg_catalog"."trigger" AS $BODY$
      DECLARE
          tables varchar[] := ARRAY['safety_supervision', 'location_identifiers', 'broadcast', 'video_channel', 'device', 'dispatch'];
          t varchar;
      BEGIN
          IF NEW.geometry_polygon is NULL THEN
              FOREACH t IN ARRAY tables
        LOOP
                  EXECUTE format('UPDATE %I set geolocation_area_id = NULL where geolocation_area_id = %L', t, NEW.id);
              END LOOP;
          ELSE
        FOREACH t IN ARRAY tables
        LOOP
                  EXECUTE format('UPDATE %1$I as s set geolocation_area_id = d.d_id from (select %1$I.id as s_id, geolocation_area.id as d_id from %1$I CROSS JOIN geolocation_area where st_contains(geolocation_area.geometry_polygon, %1$I.geometry_point) and geolocation_area.id = %2$L) as d where s.id=d.s_id', t, NEW.id);
              END LOOP;
          END IF;
              RETURN NEW;
          END;
      $BODY$
LANGUAGE plpgsql VOLATILE
COST 100;

-- ----------------------------
-- Table structure for alarm_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."alarm_detail" CASCADE;
CREATE TABLE "public"."alarm_detail" (
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_type" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "reserve_first" varchar(255) COLLATE "pg_catalog"."default",
  "reserve_second" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."alarm_detail"."point_id" IS '点id';
COMMENT ON COLUMN "public"."alarm_detail"."system_id" IS '系统名称';
COMMENT ON COLUMN "public"."alarm_detail"."alarm_type" IS '报警类型';
COMMENT ON COLUMN "public"."alarm_detail"."timestamp" IS '事件发生时间';
COMMENT ON COLUMN "public"."alarm_detail"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."alarm_detail"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."alarm_detail"."reserve_first" IS '预留字段1';
COMMENT ON COLUMN "public"."alarm_detail"."reserve_second" IS '预留字段2';

-- ----------------------------
-- Records of alarm_detail
-- ----------------------------

-- ----------------------------
-- Table structure for alarm_directive
-- ----------------------------
DROP TABLE IF EXISTS "public"."alarm_directive" CASCADE;
CREATE TABLE "public"."alarm_directive" (
  "id" SERIAL NOT NULL,
  "point_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_level" text COLLATE "pg_catalog"."default",
  "min_range" float8,
  "max_range" float8,
  "state" int4 NOT NULL,
  "timestamp" time(6) NOT NULL DEFAULT now()
)
;
COMMENT ON TABLE "public"."alarm_directive" IS '模拟报警设置指令';

-- ----------------------------
-- Records of alarm_directive
-- ----------------------------

-- ----------------------------
-- Table structure for alarm_handle
-- ----------------------------
DROP TABLE IF EXISTS "public"."alarm_handle" CASCADE;
CREATE TABLE "public"."alarm_handle" (
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_reason" varchar(255) COLLATE "pg_catalog"."default",
  "alarm_position" varchar(255) COLLATE "pg_catalog"."default",
  "action" varchar(255) COLLATE "pg_catalog"."default",
  "begin_time" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."alarm_handle"."point_id" IS '点id';
COMMENT ON COLUMN "public"."alarm_handle"."alarm_reason" IS '报警原因';
COMMENT ON COLUMN "public"."alarm_handle"."alarm_position" IS '报警位置';
COMMENT ON COLUMN "public"."alarm_handle"."action" IS '处置措施';
COMMENT ON COLUMN "public"."alarm_handle"."begin_time" IS '开始时间';
COMMENT ON COLUMN "public"."alarm_handle"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."alarm_handle"."created_at" IS '创建时间';

-- ----------------------------
-- Records of alarm_handle
-- ----------------------------

-- ----------------------------
-- Table structure for alarm_history
-- ----------------------------
DROP TABLE IF EXISTS "public"."alarm_history" CASCADE;
CREATE TABLE "public"."alarm_history" (
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "begin_time" timestamptz(6) NOT NULL,
  "end_time" timestamptz(6) NOT NULL,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "reserve_first" varchar(255) COLLATE "pg_catalog"."default",
  "reserve_second" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."alarm_history"."point_id" IS '点id';
COMMENT ON COLUMN "public"."alarm_history"."system_id" IS '系统名称';
COMMENT ON COLUMN "public"."alarm_history"."alarm_type" IS '报警类型';
COMMENT ON COLUMN "public"."alarm_history"."begin_time" IS '开始时间';
COMMENT ON COLUMN "public"."alarm_history"."end_time" IS '结束时间';
COMMENT ON COLUMN "public"."alarm_history"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."alarm_history"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."alarm_history"."reserve_first" IS '预留字段1';
COMMENT ON COLUMN "public"."alarm_history"."reserve_second" IS '预留字段2';

-- ----------------------------
-- Records of alarm_history
-- ----------------------------

-- ----------------------------
-- Table structure for alarm_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."alarm_realtime" CASCADE;
CREATE TABLE "public"."alarm_realtime" (
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "system_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_type" varchar(255) COLLATE "pg_catalog"."default",
  "begin_time" timestamptz(6),
  "updated_at" timestamptz(6) DEFAULT now(),
  "created_at" timestamptz(6) DEFAULT now(),
  "reserve_first" varchar(255) COLLATE "pg_catalog"."default",
  "reserve_second" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."alarm_realtime"."point_id" IS '点id';
COMMENT ON COLUMN "public"."alarm_realtime"."system_id" IS '系统名称';
COMMENT ON COLUMN "public"."alarm_realtime"."alarm_type" IS '报警类型';
COMMENT ON COLUMN "public"."alarm_realtime"."begin_time" IS '开始时间';
COMMENT ON COLUMN "public"."alarm_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."alarm_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."alarm_realtime"."reserve_first" IS '预留字段1';
COMMENT ON COLUMN "public"."alarm_realtime"."reserve_second" IS '预留字段2';
COMMENT ON COLUMN "public"."alarm_realtime"."timestamp" IS '不同实时值的begin_time';

-- ----------------------------
-- Triggers structure for table alarm_detail
-- ----------------------------
CREATE TRIGGER "upd_timestamp_alarm_detail" BEFORE UPDATE ON "public"."alarm_detail"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table alarm_detail
-- ----------------------------
ALTER TABLE "public"."alarm_detail" ADD CONSTRAINT "alarm_detail_pkey" PRIMARY KEY ("point_id", "timestamp");

-- ----------------------------
-- Primary Key structure for table alarm_directive
-- ----------------------------
ALTER TABLE "public"."alarm_directive" ADD CONSTRAINT "alarm_directive_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table alarm_handle
-- ----------------------------
CREATE TRIGGER "upd_timestamp_alarm_handle" BEFORE UPDATE ON "public"."alarm_handle"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table alarm_handle
-- ----------------------------
ALTER TABLE "public"."alarm_handle" ADD CONSTRAINT "alarm_handle_pkey" PRIMARY KEY ("point_id", "begin_time");

-- ----------------------------
-- Triggers structure for table alarm_history
-- ----------------------------
CREATE TRIGGER "upd_timestamp_alarm_history" BEFORE UPDATE ON "public"."alarm_history"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table alarm_history
-- ----------------------------
ALTER TABLE "public"."alarm_history" ADD CONSTRAINT "alarm_history_pkey" PRIMARY KEY ("point_id", "begin_time");

-- ----------------------------
-- Triggers structure for table alarm_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_alarm_realtime" BEFORE UPDATE ON "public"."alarm_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table alarm_realtime
-- ----------------------------
ALTER TABLE "public"."alarm_realtime" ADD CONSTRAINT "alarm_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Table structure for all_driver_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."all_driver_definition" CASCADE;
CREATE TABLE "public"."all_driver_definition" (
  "phone" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "driver_name" varchar COLLATE "pg_catalog"."default",
  "driver_code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at " timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "id_del" varchar(1) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "data_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."all_driver_definition"."phone" IS '手机号';
COMMENT ON COLUMN "public"."all_driver_definition"."driver_name" IS '司机名字';
COMMENT ON COLUMN "public"."all_driver_definition"."driver_code" IS '司机编码';
COMMENT ON COLUMN "public"."all_driver_definition"."created_at " IS '创建时间';
COMMENT ON COLUMN "public"."all_driver_definition"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."all_driver_definition"."id_del" IS '是否删除';
COMMENT ON COLUMN "public"."all_driver_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."all_driver_definition"."data_time" IS '时间';

-- ----------------------------
-- Records of all_driver_definition
-- ----------------------------

-- ----------------------------
-- Triggers structure for table all_driver_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_all_driver_definition" BEFORE UPDATE ON "public"."all_driver_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table all_driver_definition
-- ----------------------------
ALTER TABLE "public"."all_driver_definition" ADD CONSTRAINT "all_driver_definition_id" PRIMARY KEY ("phone");

-- ----------------------------
-- Table structure for anchor_stress_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."anchor_stress_definition" CASCADE;
CREATE TABLE "public"."anchor_stress_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_status" int4,
  "sensor_type" varchar COLLATE "pg_catalog"."default",
  "point_monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "description" varchar COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "unit" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "station_type" varchar(255) COLLATE "pg_catalog"."default",
  "station_number" varchar COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."anchor_stress_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."anchor_stress_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."anchor_stress_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."anchor_stress_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."anchor_stress_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."anchor_stress_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."anchor_stress_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."anchor_stress_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."anchor_stress_definition"."sensor_type" IS '传感器名称';
COMMENT ON COLUMN "public"."anchor_stress_definition"."point_monitor_type" IS '通道监测类型';
COMMENT ON COLUMN "public"."anchor_stress_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."anchor_stress_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."anchor_stress_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."anchor_stress_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."anchor_stress_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."anchor_stress_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."anchor_stress_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."anchor_stress_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."anchor_stress_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."anchor_stress_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."anchor_stress_definition"."station_type" IS '测站类型';
COMMENT ON COLUMN "public"."anchor_stress_definition"."station_number" IS '测站编号';
COMMENT ON COLUMN "public"."anchor_stress_definition"."channel_no" IS '数据使用通道';
COMMENT ON COLUMN "public"."anchor_stress_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."anchor_stress_definition" IS '矿压锚索杆应力';

-- ----------------------------
-- Primary Key structure for table anchor_stress_definition
-- ----------------------------
ALTER TABLE "public"."anchor_stress_definition" ADD CONSTRAINT "anchor_stress_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Table structure for attribute
-- ----------------------------
DROP TABLE IF EXISTS "public"."attribute" CASCADE;
CREATE TABLE "public"."attribute" (
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "id" SERIAL NOT NULL,
  "attr_type_id" int4,
  "unit" int4,
  "monitor_type_int" int4,
  "oid" varchar(225) COLLATE "pg_catalog"."default",
  "flag" int2 NOT NULL DEFAULT 0,
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."attribute"."name" IS '属性名';
COMMENT ON COLUMN "public"."attribute"."id" IS '自增，PK';
COMMENT ON COLUMN "public"."attribute"."attr_type_id" IS '外键，不可为null';
COMMENT ON COLUMN "public"."attribute"."unit" IS '和mine_unit的id对应';
COMMENT ON COLUMN "public"."attribute"."monitor_type_int" IS '监控数据类型  1表示开关量0表示数字量';

-- ----------------------------
-- Records of attribute
-- ----------------------------

-- ----------------------------
-- Table structure for auxiliary_transport_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."auxiliary_transport_definition" CASCADE;
CREATE TABLE "public"."auxiliary_transport_definition" (
  "car_number_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "sip_number" varchar(100) COLLATE "pg_catalog"."default",
  "location_number" varchar(100) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."auxiliary_transport_definition"."sip_number" IS 'sip号';
COMMENT ON COLUMN "public"."auxiliary_transport_definition"."location_number" IS '定位卡号';
COMMENT ON COLUMN "public"."auxiliary_transport_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."auxiliary_transport_definition"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."auxiliary_transport_definition" IS '辅助运输基础信息表';

-- ----------------------------
-- Table structure for auxiliary_transport_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."auxiliary_transport_realtime" CASCADE;
CREATE TABLE "public"."auxiliary_transport_realtime" (
  "car_number_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "location_number" varchar(100) COLLATE "pg_catalog"."default",
  "x" varchar(255) COLLATE "pg_catalog"."default",
  "y" varchar(255) COLLATE "pg_catalog"."default",
  "driver_id" varchar(100) COLLATE "pg_catalog"."default",
  "driver_name" varchar(100) COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."location_number" IS '定位卡号';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."x" IS 'X坐标';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."y" IS 'Y坐标';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."driver_id" IS '驾驶员ID';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."driver_name" IS '驾驶员姓名';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."timestamp" IS '上传数据时间yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."auxiliary_transport_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."auxiliary_transport_realtime" IS '辅助运输实时数据表';

-- ----------------------------
-- Primary Key structure for table attribute
-- ----------------------------
ALTER TABLE "public"."attribute" ADD CONSTRAINT "attribute_copy1_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table auxiliary_transport_definition
-- ----------------------------
ALTER TABLE "public"."auxiliary_transport_definition" ADD CONSTRAINT "auxiliary_transport_definition_pkey" PRIMARY KEY ("car_number_name");

-- ----------------------------
-- Primary Key structure for table auxiliary_transport_realtime
-- ----------------------------
ALTER TABLE "public"."auxiliary_transport_realtime" ADD CONSTRAINT "auxiliary_transport_realtime_pkey" PRIMARY KEY ("car_number_name");

-- ----------------------------
-- Table structure for belt_weigher_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."belt_weigher_definition" CASCADE;
CREATE TABLE "public"."belt_weigher_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "translate" varchar COLLATE "pg_catalog"."default",
  "unit" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "lower_range_value" numeric,
  "upper_range_value" numeric,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT now(),
  "point_type" int4
)
;

-- ----------------------------
-- Records of belt_weigher_definition
-- ----------------------------

-- ----------------------------
-- Table structure for belt_weigher_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."belt_weigher_realtime" CASCADE;
CREATE TABLE "public"."belt_weigher_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default",
  "state" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;

-- ----------------------------
-- Records of belt_weigher_realtime
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table belt_weigher_definition
-- ----------------------------
ALTER TABLE "public"."belt_weigher_definition" ADD CONSTRAINT "belt_weigher_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table belt_weigher_realtime
-- ----------------------------
ALTER TABLE "public"."belt_weigher_realtime" ADD CONSTRAINT "belt_weigher_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Foreign Keys structure for table belt_weigher_realtime
-- ----------------------------
ALTER TABLE "public"."belt_weigher_realtime" ADD CONSTRAINT "belt_weigher_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."belt_weigher_definition" ("point_id") ON DELETE RESTRICT ON UPDATE RESTRICT;

-- ----------------------------
-- Table structure for camera_area
-- ----------------------------
DROP TABLE IF EXISTS "public"."camera_area" CASCADE;
CREATE TABLE "public"."camera_area" (
  "id" SERIAL NOT NULL,
  "name" varchar(50) COLLATE "pg_catalog"."default",
  "content" varchar(100) COLLATE "pg_catalog"."default",
  "location_flag" varchar(1) COLLATE "pg_catalog"."default",
  "map_id" varchar(225) COLLATE "pg_catalog"."default",
  "num" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."camera_area"."id" IS '自增id';
COMMENT ON COLUMN "public"."camera_area"."name" IS '区域名称';
COMMENT ON COLUMN "public"."camera_area"."content" IS '区域说明';
COMMENT ON COLUMN "public"."camera_area"."location_flag" IS '井上下类型  0 井下 1 井上 ';
COMMENT ON COLUMN "public"."camera_area"."map_id" IS 'map关联标识';
COMMENT ON COLUMN "public"."camera_area"."num" IS '区域编码';
COMMENT ON TABLE "public"."camera_area" IS '摄像头区域表';

-- ----------------------------
-- Records of camera_area
-- ----------------------------
-- INSERT INTO "public"."camera_area" VALUES (6, '井下皮带', NULL, '0', '1', NULL);

-- ----------------------------
-- Table structure for camera_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."camera_group" CASCADE;
CREATE TABLE "public"."camera_group" (
  "id" SERIAL NOT NULL,
  "name" varchar(50) COLLATE "pg_catalog"."default",
  "content" varchar(255) COLLATE "pg_catalog"."default",
  "parent_id" int4,
  "create_time" timestamp(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."camera_group"."id" IS '自增id';
COMMENT ON COLUMN "public"."camera_group"."name" IS '分组名称';
COMMENT ON COLUMN "public"."camera_group"."content" IS '分组说明';
COMMENT ON COLUMN "public"."camera_group"."parent_id" IS '父编码';
COMMENT ON COLUMN "public"."camera_group"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."camera_group" IS '摄像头分组表';

-- ----------------------------
-- Records of camera_group
-- ----------------------------

-- ----------------------------
-- Table structure for camera_group_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."camera_group_mapping" CASCADE;
CREATE TABLE "public"."camera_group_mapping" (
  "id" SERIAL NOT NULL,
  "group_id" int4,
  "camera_id" int4,
  "create_time" timestamp(6) DEFAULT now(),
  "root_id" int4
)
;
COMMENT ON COLUMN "public"."camera_group_mapping"."id" IS '自增id';
COMMENT ON COLUMN "public"."camera_group_mapping"."group_id" IS '分组编码';
COMMENT ON COLUMN "public"."camera_group_mapping"."camera_id" IS '摄像头编码';
COMMENT ON COLUMN "public"."camera_group_mapping"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."camera_group_mapping"."root_id" IS '根编号';
COMMENT ON TABLE "public"."camera_group_mapping" IS '摄像头分组映射表';

-- ----------------------------
-- Records of camera_group_mapping
-- ----------------------------

-- ----------------------------
-- Table structure for camera_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."camera_info" CASCADE;
CREATE TABLE "public"."camera_info" (
  "id" SERIAL NOT NULL,
  "name" varchar(20) COLLATE "pg_catalog"."default",
  "area_id" int4,
  "ip" varchar(20) COLLATE "pg_catalog"."default",
  "port" varchar(10) COLLATE "pg_catalog"."default",
  "username" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "password" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "path" varchar(100) COLLATE "pg_catalog"."default",
  "online" bool,
  "factory" varchar(50) COLLATE "pg_catalog"."default",
  "channel_port" int4,
  "useif" bool DEFAULT true,
  "code_stream" int2,
  "location" varchar(100) COLLATE "pg_catalog"."default",
  "protocol_port" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "protocol_name" varchar(20) COLLATE "pg_catalog"."default",
  "ws_url" varchar(100) COLLATE "pg_catalog"."default",
  "mos_camera_id" int4 NOT NULL,
  "device_port" varchar(20) COLLATE "pg_catalog"."default",
  "device_ip" varchar(100) COLLATE "pg_catalog"."default",
  "play_url" varchar(255) COLLATE "pg_catalog"."default",
  "site" int4,
  "device_id" int4,
  "audio_enable" bool,
  "display_name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."camera_info"."id" IS '主键id';
COMMENT ON COLUMN "public"."camera_info"."name" IS '摄像头名称';
COMMENT ON COLUMN "public"."camera_info"."area_id" IS '摄像头区域id';
COMMENT ON COLUMN "public"."camera_info"."ip" IS '摄像头ip';
COMMENT ON COLUMN "public"."camera_info"."port" IS '端口号';
COMMENT ON COLUMN "public"."camera_info"."username" IS '用户名';
COMMENT ON COLUMN "public"."camera_info"."password" IS '密码';
COMMENT ON COLUMN "public"."camera_info"."path" IS '视频流路径';
COMMENT ON COLUMN "public"."camera_info"."online" IS '是否在线(0不在线,1在线)';
COMMENT ON COLUMN "public"."camera_info"."factory" IS '厂家';
COMMENT ON COLUMN "public"."camera_info"."channel_port" IS '通道号';
COMMENT ON COLUMN "public"."camera_info"."useif" IS '是否配置算法(0未配置,1已配置)';
COMMENT ON COLUMN "public"."camera_info"."code_stream" IS '主码流,字码流';
COMMENT ON COLUMN "public"."camera_info"."location" IS '区域';
COMMENT ON COLUMN "public"."camera_info"."mos_camera_id" IS '中台摄像头编码';
COMMENT ON COLUMN "public"."camera_info"."device_port" IS '设备端口';
COMMENT ON COLUMN "public"."camera_info"."device_ip" IS '设备ip';
COMMENT ON COLUMN "public"."camera_info"."site" IS '井上/井下（1 井上  2 井下）';
COMMENT ON COLUMN "public"."camera_info"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."camera_info"."audio_enable" IS '音频标识(true 可用 false 不可用)';
COMMENT ON COLUMN "public"."camera_info"."display_name" IS '摄像机别名';
COMMENT ON TABLE "public"."camera_info" IS '摄像头信息表';

-- ----------------------------
-- Records of camera_info
-- ----------------------------
-- INSERT INTO "public"."camera_info" VALUES (33, '模拟13号机架', NULL, '*************', '80', 'admin', 'bdtd123456', '/Streaming/Channels/101?transportmode=unicast', 'f', '海康', 1, 'f', 1, NULL, '554', 'rtsp', NULL, 275, NULL, NULL, '/live/13dd7d819f0add8f', NULL, 266, 'f', NULL);
-- INSERT INTO "public"."camera_info" VALUES (34, '模拟13号机架', NULL, '*************', '80', 'admin', 'bdtd123456', '/Streaming/Channels/101?transportmode=unicast', 'f', '海康', 1, 'f', 1, NULL, '554', 'rtsp', NULL, 276, NULL, NULL, '/live/13dd7d819f0add8f', NULL, 267, 'f', NULL);
-- INSERT INTO "public"."camera_info" VALUES (35, '模拟13号机架', 6, '*************', '80', 'admin', 'bdtd123456', '/Streaming/Channels/101?transportmode=unicast', 'f', '海康', 1, 'f', 1, '井下皮带', '554', 'rtsp', NULL, 277, NULL, NULL, '/live/13dd7d819f0add8f', 0, 268, 'f', '');

-- ----------------------------
-- Table structure for camera_pillar_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."camera_pillar_mapping" CASCADE;
CREATE TABLE "public"."camera_pillar_mapping" (
  "camera_id" int4 NOT NULL,
  "pillar_id" varchar(50) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."camera_pillar_mapping"."camera_id" IS '摄像机主键ID';
COMMENT ON COLUMN "public"."camera_pillar_mapping"."pillar_id" IS '液压支柱编号';
COMMENT ON COLUMN "public"."camera_pillar_mapping"."create_time" IS '添加时间';
COMMENT ON TABLE "public"."camera_pillar_mapping" IS '液压支柱与摄像头的关联关系';

-- ----------------------------
-- Records of camera_pillar_mapping
-- ----------------------------

-- ----------------------------
-- Table structure for car_area_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_area_definition" CASCADE;
CREATE TABLE "public"."car_area_definition" (
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default",
  "org_code" varchar COLLATE "pg_catalog"."default",
  "region" varchar COLLATE "pg_catalog"."default",
  "x" varchar COLLATE "pg_catalog"."default",
  "y" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "id_del" varchar(1) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "data_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."car_area_definition"."name" IS '区域名称';
COMMENT ON COLUMN "public"."car_area_definition"."type" IS '区域类型';
COMMENT ON COLUMN "public"."car_area_definition"."org_code" IS '组织机构编码';
COMMENT ON COLUMN "public"."car_area_definition"."region" IS '区域经纬度坐标集合';
COMMENT ON COLUMN "public"."car_area_definition"."x" IS '经度';
COMMENT ON COLUMN "public"."car_area_definition"."y" IS '纬度';
COMMENT ON COLUMN "public"."car_area_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."car_area_definition"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."car_area_definition"."id_del" IS '是否删除';
COMMENT ON COLUMN "public"."car_area_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."car_area_definition"."data_time" IS '时间';

-- ----------------------------
-- Records of car_area_definition
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table camera_pillar_mapping
-- ----------------------------
ALTER TABLE "public"."camera_pillar_mapping" ADD CONSTRAINT "camera_pillar_mapping_pkey" PRIMARY KEY ("camera_id");

-- ----------------------------
-- Triggers structure for table car_area_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_car_area_definition" BEFORE UPDATE ON "public"."car_area_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table car_area_definition
-- ----------------------------
ALTER TABLE "public"."car_area_definition" ADD CONSTRAINT "vehicle_tbcar_area_info_definition_pkey" PRIMARY KEY ("name");


-- ----------------------------
-- Table structure for car_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_definition" CASCADE;
CREATE TABLE "public"."car_definition" (
  "edge_code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "car_category" varchar COLLATE "pg_catalog"."default",
  "car_code" varchar COLLATE "pg_catalog"."default",
  "car_name" varchar COLLATE "pg_catalog"."default",
  "car_type" varchar COLLATE "pg_catalog"."default",
  "car_sn" varchar COLLATE "pg_catalog"."default",
  "car_specification" varchar COLLATE "pg_catalog"."default",
  "car_commissioning_date" varchar COLLATE "pg_catalog"."default",
  "car_manufacturing_date" varchar COLLATE "pg_catalog"."default",
  "unit_code" varchar COLLATE "pg_catalog"."default",
  "unit_name" varchar COLLATE "pg_catalog"."default",
  "org_code" varchar COLLATE "pg_catalog"."default",
  "orge_name" varchar COLLATE "pg_catalog"."default",
  "tcapacity" varchar COLLATE "pg_catalog"."default",
  "speedlimit" varchar COLLATE "pg_catalog"."default",
  "ecapacity" varchar COLLATE "pg_catalog"."default",
  "coaling" varchar COLLATE "pg_catalog"."default",
  "muck" varchar COLLATE "pg_catalog"."default",
  "eruck" varchar COLLATE "pg_catalog"."default",
  "druck" varchar COLLATE "pg_catalog"."default",
  "iradius" varchar COLLATE "pg_catalog"."default",
  "wradius" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "id_del" varchar(1) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "data_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."car_definition"."edge_code" IS '设备ID';
COMMENT ON COLUMN "public"."car_definition"."car_category" IS '车辆类别 ';
COMMENT ON COLUMN "public"."car_definition"."car_code" IS '车辆编码 ';
COMMENT ON COLUMN "public"."car_definition"."car_name" IS '车辆名称';
COMMENT ON COLUMN "public"."car_definition"."car_type" IS '车辆类型';
COMMENT ON COLUMN "public"."car_definition"."car_sn" IS '车架号或出厂编号';
COMMENT ON COLUMN "public"."car_definition"."car_specification" IS '车辆规格型号';
COMMENT ON COLUMN "public"."car_definition"."car_commissioning_date" IS '车辆投运日期';
COMMENT ON COLUMN "public"."car_definition"."car_manufacturing_date" IS '车辆制造日期';
COMMENT ON COLUMN "public"."car_definition"."unit_code" IS '使用单位、车队编码';
COMMENT ON COLUMN "public"."car_definition"."unit_name" IS '使用单位、车队名称';
COMMENT ON COLUMN "public"."car_definition"."org_code" IS '组织机构编码';
COMMENT ON COLUMN "public"."car_definition"."orge_name" IS '组织机构名称';
COMMENT ON COLUMN "public"."car_definition"."tcapacity" IS '油箱容量';
COMMENT ON COLUMN "public"."car_definition"."speedlimit" IS '限速';
COMMENT ON COLUMN "public"."car_definition"."ecapacity" IS '设备容量';
COMMENT ON COLUMN "public"."car_definition"."coaling" IS '装煤能力';
COMMENT ON COLUMN "public"."car_definition"."muck" IS '装岩能力';
COMMENT ON COLUMN "public"."car_definition"."eruck" IS '装车时长';
COMMENT ON COLUMN "public"."car_definition"."druck" IS '卸车时长';
COMMENT ON COLUMN "public"."car_definition"."iradius" IS '待装半径';
COMMENT ON COLUMN "public"."car_definition"."wradius" IS '待载半径';
COMMENT ON COLUMN "public"."car_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."car_definition"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."car_definition"."id_del" IS '是否删除';
COMMENT ON COLUMN "public"."car_definition"."system_id" IS '系统id';
COMMENT ON TABLE "public"."car_definition" IS '新版本特变电卡掉系统定位表';

-- ----------------------------
-- Records of car_definition
-- ----------------------------

-- ----------------------------
-- Table structure for car_position_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_position_realtime" CASCADE;
CREATE TABLE "public"."car_position_realtime" (
  "e" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "l" int4,
  "lat" float8,
  "lon" float8,
  "alt" float8,
  "s" float8,
  "c" float8,
  "t" timestamp(6),
  "h" float8,
  "n" int4,
  "d" int4,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "id_del" varchar(1) COLLATE "pg_catalog"."default",
  "system_id" varchar(50) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6),
  "car_category" varchar(50) COLLATE "pg_catalog"."default",
  "car_type" varchar(50) COLLATE "pg_catalog"."default",
  "car_specification" varchar(50) COLLATE "pg_catalog"."default",
  "driver_code" varchar(50) COLLATE "pg_catalog"."default",
  "driver_name" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."car_position_realtime"."e" IS '物ID/终端ID/设备物ID/终端ID/设备物ID/终端ID/设备ID';
COMMENT ON COLUMN "public"."car_position_realtime"."l" IS 'TTL 小于0表示离线';
COMMENT ON COLUMN "public"."car_position_realtime"."lat" IS '纬度，如果离线表示最后一次记录';
COMMENT ON COLUMN "public"."car_position_realtime"."lon" IS '经度';
COMMENT ON COLUMN "public"."car_position_realtime"."alt" IS '海拔';
COMMENT ON COLUMN "public"."car_position_realtime"."s" IS '速度 公里每小时';
COMMENT ON COLUMN "public"."car_position_realtime"."c" IS '正北角角度，顺时针';
COMMENT ON COLUMN "public"."car_position_realtime"."t" IS '数据采集时间 RFC3339';
COMMENT ON COLUMN "public"."car_position_realtime"."h" IS '水平精度因子';
COMMENT ON COLUMN "public"."car_position_realtime"."n" IS '定位使用卫星数';
COMMENT ON COLUMN "public"."car_position_realtime"."d" IS '设备到云端的延迟估计 毫秒';
COMMENT ON COLUMN "public"."car_position_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."car_position_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."car_position_realtime"."id_del" IS '是否删除';
COMMENT ON COLUMN "public"."car_position_realtime"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."car_position_realtime"."car_category" IS '车辆类别';
COMMENT ON COLUMN "public"."car_position_realtime"."car_type" IS '车辆类型';
COMMENT ON COLUMN "public"."car_position_realtime"."car_specification" IS '车辆规格型号';
COMMENT ON COLUMN "public"."car_position_realtime"."driver_code" IS '司机编码';
COMMENT ON COLUMN "public"."car_position_realtime"."driver_name" IS '司机名字';

-- ----------------------------
-- Records of car_position_realtime
-- ----------------------------

-- ----------------------------
-- Triggers structure for table car_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_car_definition" BEFORE UPDATE ON "public"."car_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table car_definition
-- ----------------------------
ALTER TABLE "public"."car_definition" ADD CONSTRAINT "vehicle_tbcar_device_info_definition_pkey" PRIMARY KEY ("edge_code");

-- ----------------------------
-- Triggers structure for table car_position_realtime
-- ----------------------------
CREATE TRIGGER "upd_car_position_realtime" BEFORE UPDATE ON "public"."car_position_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table car_position_realtime
-- ----------------------------
ALTER TABLE "public"."car_position_realtime" ADD CONSTRAINT "car_positinon_realtime_pkey" PRIMARY KEY ("e");

-- ----------------------------
-- Table structure for data_automation_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_automation_definition" CASCADE;
CREATE TABLE "public"."data_automation_definition" (
  "group_code" varchar COLLATE "pg_catalog"."default",
  "company_code" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "data_type" varchar COLLATE "pg_catalog"."default",
  "point_type" int4,
  "type" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."data_automation_definition"."point_id" IS '唯一索引';
COMMENT ON COLUMN "public"."data_automation_definition"."name" IS '名称';
COMMENT ON COLUMN "public"."data_automation_definition"."data_type" IS '值类型';
COMMENT ON COLUMN "public"."data_automation_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."data_automation_definition"."point_type" IS '0,模拟量 1，开关量';
COMMENT ON COLUMN "public"."data_automation_definition"."company_code" IS '矿名称';
COMMENT ON COLUMN "public"."data_automation_definition"."group_code" IS '集团名称';
COMMENT ON COLUMN "public"."data_automation_definition"."type" IS '类型';
COMMENT ON COLUMN "public"."data_automation_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_automation_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."data_automation_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."data_automation_definition" IS '自动化定义表';

-- ----------------------------
-- Table structure for data_automation_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_automation_realtime" CASCADE;
CREATE TABLE "public"."data_automation_realtime" (
  "state" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6) NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."data_automation_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_automation_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."data_automation_realtime" IS '自动化实时表';

-- ----------------------------
-- Table structure for data_business
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_business" CASCADE;
CREATE TABLE "public"."data_business" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "type_id" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "business_object" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of data_business
-- ----------------------------

-- ----------------------------
-- Table structure for data_child_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_child_system" CASCADE;
CREATE TABLE "public"."data_child_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "topo_config" text COLLATE "pg_catalog"."default",
  "image_path" varchar COLLATE "pg_catalog"."default",
  "sort_num" int4,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;

-- ----------------------------
-- Records of data_child_system
-- ----------------------------

-- ----------------------------
-- Table structure for data_definition_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_definition_conf" CASCADE;
CREATE TABLE "public"."data_definition_conf" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "other_name" varchar COLLATE "pg_catalog"."default",
  "reserve" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "company_code" varchar COLLATE "pg_catalog"."default",
  "group_code" varchar COLLATE "pg_catalog"."default",
  "expression" varchar COLLATE "pg_catalog"."default",
  "expression_params" json NULL,
  "translate" text COLLATE "pg_catalog"."default",
  "unit" varchar COLLATE "pg_catalog"."default",
  "point_type" int4,
  "warn" text COLLATE "pg_catalog"."default",
  "short_name" varchar COLLATE "pg_catalog"."default",
  "unit_id" varchar(32) COLLATE "pg_catalog"."default",
  "unit_display_symbol" varchar(32) COLLATE "pg_catalog"."default",
  "unit_short_name" varchar(16) COLLATE "pg_catalog"."default",
  "node_label" varchar(16) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_definition_conf"."point_id" IS '点id';
COMMENT ON COLUMN "public"."data_definition_conf"."other_name" IS '别名';
COMMENT ON COLUMN "public"."data_definition_conf"."reserve" IS '备用字段';
COMMENT ON COLUMN "public"."data_definition_conf"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_definition_conf"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."data_definition_conf"."company_code" IS '矿名称';
COMMENT ON COLUMN "public"."data_definition_conf"."group_code" IS '集团名称';
COMMENT ON COLUMN "public"."data_definition_conf"."expression" IS '表达式';
COMMENT ON COLUMN "public"."data_definition_conf"."expression_params" IS '表达式参数列表';
COMMENT ON COLUMN "public"."data_definition_conf"."translate" IS '翻译';
COMMENT ON COLUMN "public"."data_definition_conf"."unit" IS '单位';
COMMENT ON COLUMN "public"."data_definition_conf"."short_name" IS '简称';
COMMENT ON COLUMN "public"."data_definition_conf"."unit_id" IS '单位ID';
COMMENT ON COLUMN "public"."data_definition_conf"."unit_display_symbol" IS '单位显示符号';
COMMENT ON COLUMN "public"."data_definition_conf"."unit_short_name" IS '单位简称';
COMMENT ON COLUMN "public"."data_definition_conf"."node_label" IS '路径标签';

-- ----------------------------
-- Table structure for data_derive_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_derive_definition" CASCADE;
CREATE TABLE "public"."data_derive_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "data_type" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "point_type" int4,
  "company_code" varchar COLLATE "pg_catalog"."default",
  "group_code" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "unit" varchar COLLATE "pg_catalog"."default",
  "parent_system_id" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON TABLE "public"."data_derive_definition" IS '派生点实时表';

-- ----------------------------
-- Table structure for data_derive_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_derive_realtime" CASCADE;
CREATE TABLE "public"."data_derive_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "company_code" varchar COLLATE "pg_catalog"."default",
  "group_code" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6),
  "state" varchar COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for data_derive_tree
-- ----------------------------
-- 派生点关系树
DROP TABLE IF EXISTS "public"."data_derive_tree";
CREATE TABLE "public"."data_derive_tree" (
  id serial4 NOT NULL, -- 自增ID
  "path" public.ltree NULL, -- 点位路径
  source_point_id varchar(255) NULL, -- 点位ID
  source_node_label varchar(32) NULL, -- 点位标签
  derive_point_id varchar(255) NULL, -- 点位ID
  derive_node_label varchar(32) NULL -- 点位标签
)
;
COMMENT ON TABLE public.data_derive_tree IS '派生点关系树';
COMMENT ON COLUMN public.data_derive_tree.id IS '自增ID';
COMMENT ON COLUMN public.data_derive_tree.source_point_id IS '点位ID';
COMMENT ON COLUMN public.data_derive_tree.source_node_label IS '点位标签';
COMMENT ON COLUMN public.data_derive_tree."path" IS '点位路径';
COMMENT ON COLUMN public.data_derive_tree.derive_point_id IS '点位ID';
COMMENT ON COLUMN public.data_derive_tree.derive_node_label IS '点位标签';

-- ----------------------------
-- Table structure for data_device
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_device" CASCADE;
CREATE TABLE "public"."data_device" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "type_id" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "geometry_point" "public"."geometry",
  "device_id" varchar COLLATE "pg_catalog"."default",
  "system" varchar(255) COLLATE "pg_catalog"."default",
  "geolocation_area_id" int4,
  "tunnel_guid" text COLLATE "pg_catalog"."default"
)
;
-- ----------------------------
-- Triggers structure for table data_device
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_device" BEFORE UPDATE ON "public"."data_device"
    FOR EACH ROW
    EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_device
-- ----------------------------
ALTER TABLE "public"."data_device" ADD CONSTRAINT "data_device_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Records of data_device
-- ----------------------------

-- ----------------------------
-- Table structure for data_device_model_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_device_model_conf" CASCADE;
CREATE TABLE "public"."data_device_model_conf" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "attribute_id" varchar COLLATE "pg_catalog"."default",
  "device_id" varchar COLLATE "pg_catalog"."default",
  "business_id" varchar COLLATE "pg_catalog"."default",
  "company_code" varchar COLLATE "pg_catalog"."default",
  "group_code" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "system_id" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_device_model_conf"."point_id" IS '点id';
COMMENT ON COLUMN "public"."data_device_model_conf"."attribute_id" IS '属性id';
COMMENT ON COLUMN "public"."data_device_model_conf"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."data_device_model_conf"."business_id" IS '组id';
COMMENT ON COLUMN "public"."data_device_model_conf"."company_code" IS '矿名称';
COMMENT ON COLUMN "public"."data_device_model_conf"."group_code" IS '组名称';
COMMENT ON COLUMN "public"."data_device_model_conf"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_device_model_conf"."updated_at" IS '更新时间';

-- ----------------------------
-- Table structure for data_device_gis
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_device_gis" CASCADE;
CREATE TABLE "public"."data_device_gis" (
    "id" Serial,
    "device_id" text COLLATE "pg_catalog"."default" NOT NULL,
    "topo_code" text COLLATE "pg_catalog"."default",
    CONSTRAINT "data_device_gis_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "data_device_gis_device_id_fkey" FOREIGN KEY ("device_id") REFERENCES "public"."data_device" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "data_device_gis_device_id_key" UNIQUE ("device_id")
);

COMMENT ON TABLE "public"."data_device_gis" IS '自动化设备在GIS绑点附加信息';

-- ----------------------------
-- Table structure for data_dict
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_dict" CASCADE;
CREATE TABLE "public"."data_dict" (
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "k" varchar(255) COLLATE "pg_catalog"."default",
  "v" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of data_dict
-- ----------------------------

-- ----------------------------
-- Table structure for data_source_monitor
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_source_monitor" CASCADE;
CREATE TABLE "public"."data_source_monitor" (
  "routing_key" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "source_name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  "msg_size" int4 NOT NULL,
  "flag" int4 NOT NULL DEFAULT 0,
  "data_time" timestamp(6) NOT NULL,
  "title" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar(255) COLLATE "pg_catalog"."default",
  "ip" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_source_monitor"."routing_key" IS '主键，routingkey截取 除去汉字';
COMMENT ON COLUMN "public"."data_source_monitor"."source_name" IS '数据源名称';
COMMENT ON COLUMN "public"."data_source_monitor"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_source_monitor"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."data_source_monitor"."msg_size" IS '消息数量';
COMMENT ON COLUMN "public"."data_source_monitor"."flag" IS '0-默认，1-前端查询用，手动更改';
COMMENT ON COLUMN "public"."data_source_monitor"."data_time" IS '数据时间';
COMMENT ON COLUMN "public"."data_source_monitor"."title" IS '系统展示名称';
COMMENT ON COLUMN "public"."data_source_monitor"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."data_source_monitor"."ip" IS 'ip';
COMMENT ON TABLE "public"."data_source_monitor" IS '数据源监控表';

-- ----------------------------
-- Table structure for data_topo
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_topo" CASCADE;
CREATE TABLE "public"."data_topo" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "code" varchar COLLATE "pg_catalog"."default",
  "value" text COLLATE "pg_catalog"."default",
  "version" int4,
  "create_time" timestamptz(6) NOT NULL DEFAULT now(),
  "flag" int4,
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6),
  "ref_url" varchar(1024) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_topo"."ref_url" IS '参照组态地址, 比如力控Web发布';

-- ----------------------------
-- Table structure for data_topo_draft
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_topo_draft" CASCADE;
CREATE TABLE "public"."data_topo_draft" (
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" text COLLATE "pg_catalog"."default" NOT NULL,
  "version" int4,
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "change" int4 NOT NULL,
  "ref_url" varchar(1024) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_topo_draft"."change" IS '0:未修改(和正式表一直),1:已经修改';
COMMENT ON COLUMN "public"."data_topo_draft"."ref_url" IS '参照组态地址, 比如力控Web发布';

-- ----------------------------
-- Table structure for data_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_type" CASCADE;
CREATE TABLE "public"."data_type" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "discription" varchar COLLATE "pg_catalog"."default",
  "object" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "order_number" int4,
  "display_symbol" varchar(32) COLLATE "pg_catalog"."default",
  "short_name" varchar(16) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."data_type"."name" IS '名称';
COMMENT ON COLUMN "public"."data_type"."type" IS 'unit单位,warn告警类型';
COMMENT ON COLUMN "public"."data_type"."display_symbol" IS '显示符号';
COMMENT ON COLUMN "public"."data_type"."short_name" IS '短名称';

-- ----------------------------
-- Triggers structure for table data_automation_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_automation_definition" BEFORE UPDATE ON "public"."data_automation_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_automation_definition
-- ----------------------------
ALTER TABLE "public"."data_automation_definition" ADD CONSTRAINT "data_automation_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table data_automation_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_automation_realtime" BEFORE UPDATE ON "public"."data_automation_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_automation_realtime
-- ----------------------------
ALTER TABLE "public"."data_automation_realtime" ADD CONSTRAINT "data_automation_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table data_business
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_business" BEFORE UPDATE ON "public"."data_business"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_business
-- ----------------------------
ALTER TABLE "public"."data_business" ADD CONSTRAINT "data_business_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table data_child_system
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_child_system" BEFORE UPDATE ON "public"."data_child_system"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_child_system
-- ----------------------------
ALTER TABLE "public"."data_child_system" ADD CONSTRAINT "data_child_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table data_definition_conf
-- ----------------------------
ALTER TABLE "public"."data_definition_conf" ADD CONSTRAINT "data_definition_conf_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table data_derive_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_derive_definition" BEFORE UPDATE ON "public"."data_derive_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_derive_definition
-- ----------------------------
ALTER TABLE "public"."data_derive_definition" ADD CONSTRAINT "data_derive_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Indexes structure for table data_derive_realtime
-- ----------------------------
CREATE INDEX "uniq_index" ON "public"."data_derive_realtime" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table data_derive_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_derive_realtime" BEFORE UPDATE ON "public"."data_derive_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_derive_realtime
-- ----------------------------
ALTER TABLE "public"."data_derive_realtime" ADD CONSTRAINT "data_derive_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Indexes structure for table data_derive_tree
-- ----------------------------
CREATE INDEX data_derive_tree_path_idx ON "public"."data_derive_tree" USING gist (path);

-- ----------------------------
-- Primary Key structure for table data_derive_tree
-- ----------------------------
ALTER TABLE "public"."data_derive_tree" ADD CONSTRAINT "data_derive_tree_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table data_device_model_conf
-- ----------------------------
ALTER TABLE "public"."data_device_model_conf" ADD CONSTRAINT "data_device_model_conf_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table data_source_monitor
-- ----------------------------
ALTER TABLE "public"."data_source_monitor" ADD CONSTRAINT "data_source_monitor_pkey" PRIMARY KEY ("routing_key");

-- ----------------------------
-- Primary Key structure for table data_topo
-- ----------------------------
ALTER TABLE "public"."data_topo" ADD CONSTRAINT "data_topo_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table data_topo_draft
-- ----------------------------
ALTER TABLE "public"."data_topo_draft" ADD CONSTRAINT "data_topo_draft_pkey" PRIMARY KEY ("code");

-- ----------------------------
-- Triggers structure for table data_type
-- ----------------------------
CREATE TRIGGER "upd_timestamp_data_type" BEFORE UPDATE ON "public"."data_type"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table data_type
-- ----------------------------
ALTER TABLE "public"."data_type" ADD CONSTRAINT "data_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table data_automation_realtime
-- ----------------------------
ALTER TABLE "public"."data_automation_realtime" ADD CONSTRAINT "data_automation_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."data_automation_definition" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table data_derive_realtime
-- ----------------------------
ALTER TABLE "public"."data_derive_realtime" ADD CONSTRAINT "data_derive_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."data_derive_definition" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for data_status
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_status";
CREATE TABLE "public"."data_status" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "note" varchar COLLATE "pg_catalog"."default",
  "point_key" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;

-- ----------------------------
-- Records of data_status
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table data_status
-- ----------------------------
ALTER TABLE "public"."data_status" ADD CONSTRAINT "data_status_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Table structure for device
-- ----------------------------
DROP TABLE IF EXISTS "public"."device" CASCADE;
CREATE TABLE "public"."device" (
  "id" SERIAL NOT NULL,
  "device_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_point" "public"."geometry",
  "geolocation_area_id" int4,
  "system" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6),
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."device"."id" IS '自增ID';
COMMENT ON COLUMN "public"."device"."device_id" IS '设备ID';
COMMENT ON COLUMN "public"."device"."name" IS '设备名称';
COMMENT ON COLUMN "public"."device"."geometry_point" IS '坐标点';
COMMENT ON COLUMN "public"."device"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."device"."system" IS '系统名称';
COMMENT ON COLUMN "public"."device"."tunnel_guid" IS '巷道GUID';
COMMENT ON COLUMN "public"."device"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."device"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."device"."system_id" IS '系统ID';
COMMENT ON TABLE "public"."device" IS '设备信息表';

-- ----------------------------
-- Table structure for device_control
-- ----------------------------
DROP TABLE IF EXISTS "public"."device_control" CASCADE;
CREATE TABLE "public"."device_control" (
  "id" SERIAL NOT NULL,
  "node_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "value" text COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "status" int4 DEFAULT 0,
  "user_id" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."device_control"."status" IS '1表示已成功， 2表示已失败';

-- ----------------------------
-- Records of device_control
-- ----------------------------

-- ----------------------------
-- Table structure for device_control_white_list
-- ----------------------------
DROP TABLE IF EXISTS "public"."device_control_white_list" CASCADE;
CREATE TABLE "public"."device_control_white_list" (
  "point_id" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."device_control_white_list" IS '控制点白名单';

-- ----------------------------
-- Records of device_control_white_list
-- ----------------------------

-- ----------------------------
-- Table structure for device_model
-- ----------------------------
DROP TABLE IF EXISTS "public"."device_model" CASCADE;
CREATE TABLE "public"."device_model" (
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "id" SERIAL NOT NULL,
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."device_model"."name" IS '设备模型--属性对应表';
COMMENT ON COLUMN "public"."device_model"."id" IS '自增，PK';

-- ----------------------------
-- Records of device_model
-- ----------------------------

-- ----------------------------
-- Table structure for device_point
-- ----------------------------
DROP TABLE IF EXISTS "public"."device_point" CASCADE;
CREATE TABLE "public"."device_point" (
  "id" BIGSERIAL NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "device_id" varchar COLLATE "pg_catalog"."default",
  "raw_value" varchar COLLATE "pg_catalog"."default",
  "display_value" varchar COLLATE "pg_catalog"."default",
  "point_type" int4,
  "alarm" bool NOT NULL,
  "alarm_level" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "state" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'Good'::text,
  "browser_name" text COLLATE "pg_catalog"."default",
  "unit" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."device_point"."id" IS '自增ID';
COMMENT ON COLUMN "public"."device_point"."point_id" IS '设备点ID';
COMMENT ON COLUMN "public"."device_point"."device_id" IS '设备ID';
COMMENT ON COLUMN "public"."device_point"."display_value" IS '显示值';
COMMENT ON COLUMN "public"."device_point"."point_type" IS '设备点类型';
COMMENT ON COLUMN "public"."device_point"."alarm" IS '是否报警';
COMMENT ON COLUMN "public"."device_point"."alarm_level" IS '报警级别';
COMMENT ON COLUMN "public"."device_point"."browser_name" IS '浏览名称';
COMMENT ON COLUMN "public"."device_point"."unit" IS '单位';
COMMENT ON COLUMN "public"."device_point"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."device_point"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."device_point" IS '设备信息点表';

-- ----------------------------
-- Records of device_point
-- ----------------------------

-- ----------------------------
-- Indexes structure for table device
-- ----------------------------
CREATE INDEX "idx_device_geometry_point" ON "public"."device" USING gist (
  "geometry_point" "public"."gist_geometry_ops_2d"
);

-- ----------------------------
-- Triggers structure for table device
-- ----------------------------
CREATE TRIGGER "update_device_geolocation_area_id" AFTER INSERT OR UPDATE OF "geometry_point" ON "public"."device"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_point"();

-- ----------------------------
-- Uniques structure for table device
-- ----------------------------
ALTER TABLE "public"."device" ADD CONSTRAINT "device_device_id_key" UNIQUE ("device_id");
ALTER TABLE "public"."device" ADD CONSTRAINT "device_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table device
-- ----------------------------
ALTER TABLE "public"."device" ADD CONSTRAINT "device_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_control
-- ----------------------------
ALTER TABLE "public"."device_control" ADD CONSTRAINT "device_control_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table device_control_white_list
-- ----------------------------
ALTER TABLE "public"."device_control_white_list" ADD CONSTRAINT "device_control_white_list_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Indexes structure for table device_point
-- ----------------------------
CREATE UNIQUE INDEX "ix_device_point_point_id" ON "public"."device_point" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table device_point
-- ----------------------------
CREATE TRIGGER "upd_timestamp_device_point" BEFORE UPDATE ON "public"."device_point"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table device_point
-- ----------------------------
ALTER TABLE "public"."device_point" ADD CONSTRAINT "device_point_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table device
-- ----------------------------
ALTER TABLE "public"."device" ADD CONSTRAINT "device_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for ding_approval
-- ----------------------------
DROP TABLE IF EXISTS "public"."ding_approval" CASCADE;
CREATE TABLE "public"."ding_approval" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "mine_code" int4,
  "ip_address" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "approver" varchar COLLATE "pg_catalog"."default",
  "state" varchar COLLATE "pg_catalog"."default",
  "begin_time" timestamp(6) DEFAULT now(),
  "end_time" timestamp(6) DEFAULT now(),
  "sql" text COLLATE "pg_catalog"."default",
  "node_red" text COLLATE "pg_catalog"."default",
  "note" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT now(),
  "system" json,
  "telephone" varchar COLLATE "pg_catalog"."default",
  "process_instance_id" varchar COLLATE "pg_catalog"."default",
  "dataroute_state" varchar COLLATE "pg_catalog"."default",
  "approval_type" int4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ding_approval"."name" IS '提交申请人姓名';

-- ----------------------------
-- Table structure for ding_approval_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."ding_approval_system" CASCADE;
CREATE TABLE "public"."ding_approval_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "mine_code" int4 NOT NULL,
  "route_service" int4 NOT NULL DEFAULT 1,
  "warn_service" int4 NOT NULL DEFAULT 0,
  "converter_service" int4 NOT NULL DEFAULT 0,
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  "approval_state" varchar COLLATE "pg_catalog"."default",
  "dataroute_state" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "mine_system_id" int4,
  "mine_system_name" varchar COLLATE "pg_catalog"."default",
  "business_type" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for dispatch
-- ----------------------------
DROP TABLE IF EXISTS "public"."dispatch" CASCADE;
CREATE TABLE "public"."dispatch" (
  "id" SERIAL NOT NULL,
  "point_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "mac" text COLLATE "pg_catalog"."default",
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "location" text COLLATE "pg_catalog"."default",
  "is_initiative_call" bool NOT NULL,
  "state" text COLLATE "pg_catalog"."default",
  "ip" text COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6),
  "geometry_point" "public"."geometry",
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "initiative_call_id" text COLLATE "pg_catalog"."default",
  "updated_at" timestamp(6) DEFAULT now(),
  "description" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."dispatch"."point_id" IS '分机号';
COMMENT ON COLUMN "public"."dispatch"."mac" IS '物理号';
COMMENT ON COLUMN "public"."dispatch"."is_initiative_call" IS '设置是否主叫';
COMMENT ON COLUMN "public"."dispatch"."ip" IS '（预留）以后可能介入ip电话';
COMMENT ON COLUMN "public"."dispatch"."initiative_call_id" IS '被叫分机id';
COMMENT ON COLUMN "public"."dispatch"."description" IS '描述';
COMMENT ON TABLE "public"."dispatch" IS '调度通讯';

-- ----------------------------
-- Records of dispatch
-- ----------------------------

-- ----------------------------
-- Table structure for dispatch_directive
-- ----------------------------
DROP TABLE IF EXISTS "public"."dispatch_directive" CASCADE;
CREATE TABLE "public"."dispatch_directive" (
  "id" SERIAL NOT NULL,
  "point_id" text COLLATE "pg_catalog"."default" NOT NULL,
  "state" text COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamp(6) NOT NULL DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "passive_mac" text COLLATE "pg_catalog"."default",
  "initiative_id" text COLLATE "pg_catalog"."default",
  "initiative_mac" text COLLATE "pg_catalog"."default",
  "duration" varchar(255) COLLATE "pg_catalog"."default",
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "system_id" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "tel" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "is_up" int2 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."dispatch_directive"."point_id" IS '被叫方id';
COMMENT ON COLUMN "public"."dispatch_directive"."state" IS '字符串（电话连接中、通话开始） 0 //1：空闲，2：忙，3：离线。';
COMMENT ON COLUMN "public"."dispatch_directive"."passive_mac" IS '被叫mac';
COMMENT ON COLUMN "public"."dispatch_directive"."initiative_id" IS '主叫id';
COMMENT ON COLUMN "public"."dispatch_directive"."initiative_mac" IS '主叫mac';
COMMENT ON COLUMN "public"."dispatch_directive"."duration" IS '通话时长';
COMMENT ON COLUMN "public"."dispatch_directive"."name" IS '名称';
COMMENT ON COLUMN "public"."dispatch_directive"."is_up" IS '0井下 1井上';
COMMENT ON TABLE "public"."dispatch_directive" IS '调度通讯指令下发';

-- ----------------------------
-- Records of dispatch_directive
-- ----------------------------

-- ----------------------------
-- Table structure for dispatch_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."dispatch_group" CASCADE;
CREATE TABLE "public"."dispatch_group" (
  "id" SERIAL NOT NULL,
  "name" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."dispatch_group" IS '调度通讯分组';

-- ----------------------------
-- Records of dispatch_group
-- ----------------------------

-- ----------------------------
-- Table structure for dispatch_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."dispatch_map" CASCADE;
CREATE TABLE "public"."dispatch_map" (
  "id" SERIAL NOT NULL,
  "dispatch_group_id" int4 NOT NULL,
  "point_id" text COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON TABLE "public"."dispatch_map" IS '调度通讯分组关系';

-- ----------------------------
-- Records of dispatch_map
-- ----------------------------

-- ----------------------------
-- Table structure for escape_route
-- ----------------------------
DROP TABLE IF EXISTS "public"."escape_route" CASCADE;
CREATE TABLE "public"."escape_route" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "color" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_line" "public"."geometry",
  "width" int4 NOT NULL,
  "tunnel_guid" json
)
;
COMMENT ON COLUMN "public"."escape_route"."id" IS '自增ID';
COMMENT ON COLUMN "public"."escape_route"."name" IS '路线名称';
COMMENT ON COLUMN "public"."escape_route"."type" IS '避灾类型';
COMMENT ON COLUMN "public"."escape_route"."color" IS '路线颜色';
COMMENT ON COLUMN "public"."escape_route"."geometry_line" IS '路线坐标';
COMMENT ON COLUMN "public"."escape_route"."width" IS '路线宽度';
COMMENT ON COLUMN "public"."escape_route"."tunnel_guid" IS '巷道guid';
COMMENT ON TABLE "public"."escape_route" IS '避灾路线管理';

-- ----------------------------
-- Records of escape_route
-- ----------------------------

-- ----------------------------
-- Table structure for mining_area
-- ----------------------------
DROP TABLE IF EXISTS "public"."mining_area" CASCADE;
CREATE TABLE "public"."mining_area" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "elevation" int4,
  "description" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."mining_area"."id" IS '自增ID';
COMMENT ON COLUMN "public"."mining_area"."name" IS '采区名称';
COMMENT ON COLUMN "public"."mining_area"."elevation" IS '标高';
COMMENT ON COLUMN "public"."mining_area"."description" IS '描述信息';
COMMENT ON TABLE "public"."mining_area" IS '采区信息表';

-- ----------------------------
-- Records of mining_area
-- ----------------------------

-- ----------------------------
-- Table structure for safety_supervision
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_supervision" CASCADE;
CREATE TABLE "public"."safety_supervision" (
  "id" BIGSERIAL NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "unit" varchar COLLATE "pg_catalog"."default",
  "label" varchar COLLATE "pg_catalog"."default",
  "point_type" int4 NOT NULL,
  "upper_range_value" float8,
  "lower_range_value" float8,
  "upper_alarm_value" float8,
  "lower_alarm_value" float8,
  "upper_warning_value" float8,
  "lower_warning_value" float8,
  "upper_cut_value" float8,
  "lower_cut_value" float8,
  "upper_reset_value" float8,
  "lower_reset_value" float8,
  "geometry_point" "public"."geometry",
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "use_type" int4 NOT NULL DEFAULT 1,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "view_type" int4 DEFAULT 3,
  "system_id" varchar COLLATE "pg_catalog"."default",
  "translate" varchar COLLATE "pg_catalog"."default",
  "data_type" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."safety_supervision"."id" IS '自增ID';
COMMENT ON COLUMN "public"."safety_supervision"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."safety_supervision"."parent_id" IS '传感器上级编号';
COMMENT ON COLUMN "public"."safety_supervision"."name" IS '传感器名称';
COMMENT ON COLUMN "public"."safety_supervision"."location" IS '安装位置';
COMMENT ON COLUMN "public"."safety_supervision"."type" IS '传感器类型';
COMMENT ON COLUMN "public"."safety_supervision"."unit" IS '模拟量单位';
COMMENT ON COLUMN "public"."safety_supervision"."label" IS '传感器描述';
COMMENT ON COLUMN "public"."safety_supervision"."point_type" IS '数据类型';
COMMENT ON COLUMN "public"."safety_supervision"."upper_range_value" IS '量程上限';
COMMENT ON COLUMN "public"."safety_supervision"."lower_range_value" IS '量程下限';
COMMENT ON COLUMN "public"."safety_supervision"."upper_alarm_value" IS '报警上限';
COMMENT ON COLUMN "public"."safety_supervision"."lower_alarm_value" IS '报警下限';
COMMENT ON COLUMN "public"."safety_supervision"."upper_warning_value" IS '预警上限';
COMMENT ON COLUMN "public"."safety_supervision"."lower_warning_value" IS '预警下限';
COMMENT ON COLUMN "public"."safety_supervision"."upper_cut_value" IS '断电上限';
COMMENT ON COLUMN "public"."safety_supervision"."lower_cut_value" IS '断电下限';
COMMENT ON COLUMN "public"."safety_supervision"."upper_reset_value" IS '复电上限';
COMMENT ON COLUMN "public"."safety_supervision"."lower_reset_value" IS '复电下限';
COMMENT ON COLUMN "public"."safety_supervision"."geometry_point" IS '坐标点';
COMMENT ON COLUMN "public"."safety_supervision"."mining_area_id" IS '采区ID';
COMMENT ON COLUMN "public"."safety_supervision"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."safety_supervision"."tunnel_guid" IS '巷道guid';
COMMENT ON COLUMN "public"."safety_supervision"."use_type" IS '使用类型 1 使用 2删除';
COMMENT ON COLUMN "public"."safety_supervision"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."safety_supervision"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."safety_supervision"."view_type" IS '查看类型 3 ';
COMMENT ON TABLE "public"."safety_supervision" IS '安全监控设备表';

-- ----------------------------
-- Records of safety_supervision
-- ----------------------------

-- ----------------------------
-- Table structure for safety_supervision_alarm_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_supervision_alarm_statistics" CASCADE;
CREATE TABLE "public"."safety_supervision_alarm_statistics" (
  "id" SERIAL NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "begin_time" timestamptz(6) NOT NULL,
  "end_time" timestamptz(6),
  "duration" int8,
  "alarm_level" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."id" IS '自增ID';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."parent_id" IS '传感器上级编号';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."name" IS '传感器名称';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."location" IS '安装位置';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."type" IS '传感器类型';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."begin_time" IS '报警开始时间';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."end_time" IS '报警结束时间';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."duration" IS '报警持续时长';
COMMENT ON COLUMN "public"."safety_supervision_alarm_statistics"."alarm_level" IS '报警级别';
COMMENT ON TABLE "public"."safety_supervision_alarm_statistics" IS '安全监控报警统计表';

-- ----------------------------
-- Records of safety_supervision_alarm_statistics
-- ----------------------------

-- ----------------------------
-- Table structure for safety_supervision_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_supervision_realtime" CASCADE;
CREATE TABLE "public"."safety_supervision_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "alarm" bool NOT NULL,
  "alarm_level" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "specific_status" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."safety_supervision_realtime"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."value" IS '实时值';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."state" IS '状态描述';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."alarm" IS '是否报警';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."alarm_level" IS '报警级别';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."safety_supervision_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."safety_supervision_realtime" IS '安全监控实时表';

-- ----------------------------
-- Records of safety_supervision_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for safety_supervision_statement
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_supervision_statement" CASCADE;
CREATE TABLE "public"."safety_supervision_statement" (
  "id" SERIAL NOT NULL,
  "file" jsonb NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."safety_supervision_statement"."file" IS '报表文件信息';
COMMENT ON COLUMN "public"."safety_supervision_statement"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."safety_supervision_statement"."updated_at" IS '修改时间';
COMMENT ON TABLE "public"."safety_supervision_statement" IS '安全监控报表';

-- ----------------------------
-- Records of safety_supervision_statement
-- ----------------------------

-- ----------------------------
-- Table structure for ventilation_route
-- ----------------------------
DROP TABLE IF EXISTS "public"."ventilation_route" CASCADE;
CREATE TABLE "public"."ventilation_route" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "color" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "width" int4 NOT NULL,
  "geometry_line" "public"."geometry",
  "tunnel_guid" json
)
;
COMMENT ON COLUMN "public"."ventilation_route"."id" IS '自增ID';
COMMENT ON COLUMN "public"."ventilation_route"."name" IS '路线名称';
COMMENT ON COLUMN "public"."ventilation_route"."type" IS '避灾类型';
COMMENT ON COLUMN "public"."ventilation_route"."color" IS '路线颜色';
COMMENT ON COLUMN "public"."ventilation_route"."width" IS '路线宽度';
COMMENT ON COLUMN "public"."ventilation_route"."tunnel_guid" IS '巷道guid';
COMMENT ON TABLE "public"."ventilation_route" IS '通风路线管理';

-- ----------------------------
-- Records of ventilation_route
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table ding_approval
-- ----------------------------
ALTER TABLE "public"."ding_approval" ADD CONSTRAINT "ding_approval_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table ding_approval_system
-- ----------------------------
ALTER TABLE "public"."ding_approval_system" ADD CONSTRAINT "ding_approval_system_system_name_key" UNIQUE ("system_name");

-- ----------------------------
-- Primary Key structure for table ding_approval_system
-- ----------------------------
ALTER TABLE "public"."ding_approval_system" ADD CONSTRAINT "ding_approval_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table dispatch
-- ----------------------------
CREATE TRIGGER "set_public_dispatch_updated_at" BEFORE UPDATE ON "public"."dispatch"
FOR EACH ROW
EXECUTE PROCEDURE "public"."set_current_timestamp_updated_at"();
COMMENT ON TRIGGER "set_public_dispatch_updated_at" ON "public"."dispatch" IS 'trigger to set value of column "updated_at" to current timestamp on row update';
CREATE TRIGGER "update_dispatch_geolocation_area_id" AFTER INSERT OR UPDATE OF "geometry_point" ON "public"."dispatch"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_point"();

-- ----------------------------
-- Uniques structure for table dispatch
-- ----------------------------
ALTER TABLE "public"."dispatch" ADD CONSTRAINT "dispatch_point_id_key" UNIQUE ("point_id");

-- ----------------------------
-- Primary Key structure for table dispatch
-- ----------------------------
ALTER TABLE "public"."dispatch" ADD CONSTRAINT "dispatch_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table dispatch_directive
-- ----------------------------
ALTER TABLE "public"."dispatch_directive" ADD CONSTRAINT "dispatch_realtime_point_id_key" UNIQUE ("point_id");

-- ----------------------------
-- Primary Key structure for table dispatch_directive
-- ----------------------------
ALTER TABLE "public"."dispatch_directive" ADD CONSTRAINT "dispatch_directive_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table dispatch_group
-- ----------------------------
ALTER TABLE "public"."dispatch_group" ADD CONSTRAINT "dispatch_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table dispatch_map
-- ----------------------------
ALTER TABLE "public"."dispatch_map" ADD CONSTRAINT "dispatch_map_dispatch_group_id_point_id_key" UNIQUE ("dispatch_group_id", "point_id");

-- ----------------------------
-- Primary Key structure for table dispatch_map
-- ----------------------------
ALTER TABLE "public"."dispatch_map" ADD CONSTRAINT "dispatch_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table escape_route
-- ----------------------------
CREATE INDEX "idx_escape_route_geometry_line" ON "public"."escape_route" USING gist (
  "geometry_line" "public"."gist_geometry_ops_2d"
);

-- ----------------------------
-- Uniques structure for table escape_route
-- ----------------------------
ALTER TABLE "public"."escape_route" ADD CONSTRAINT "escape_route_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table escape_route
-- ----------------------------
ALTER TABLE "public"."escape_route" ADD CONSTRAINT "escape_route_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table mining_area
-- ----------------------------
ALTER TABLE "public"."mining_area" ADD CONSTRAINT "mining_area_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table mining_area
-- ----------------------------
ALTER TABLE "public"."mining_area" ADD CONSTRAINT "mining_area_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table safety_supervision
-- ----------------------------
CREATE INDEX "idx_safety_supervision_geometry_point" ON "public"."safety_supervision" USING gist (
  "geometry_point" "public"."gist_geometry_ops_2d"
);
CREATE UNIQUE INDEX "ix_safety_supervision_point_id" ON "public"."safety_supervision" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table safety_supervision
-- ----------------------------
CREATE TRIGGER "upd_timestamp_safety_supervision" BEFORE UPDATE ON "public"."safety_supervision"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();
CREATE TRIGGER "update_safety_supervision_geolocation_area_id" AFTER INSERT OR UPDATE OF "geometry_point" ON "public"."safety_supervision"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_point"();

-- ----------------------------
-- Primary Key structure for table safety_supervision
-- ----------------------------
ALTER TABLE "public"."safety_supervision" ADD CONSTRAINT "safety_supervision_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table safety_supervision_alarm_statistics
-- ----------------------------
CREATE INDEX "ix_safety_supervision_alarm_statistics_begin_time" ON "public"."safety_supervision_alarm_statistics" USING btree (
  "begin_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table safety_supervision_alarm_statistics
-- ----------------------------
ALTER TABLE "public"."safety_supervision_alarm_statistics" ADD CONSTRAINT "safety_supervision_alarm_statistics_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table safety_supervision_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_safety_supervision_realtime" BEFORE UPDATE ON "public"."safety_supervision_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table safety_supervision_realtime
-- ----------------------------
ALTER TABLE "public"."safety_supervision_realtime" ADD CONSTRAINT "safety_supervision_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table safety_supervision_statement
-- ----------------------------
ALTER TABLE "public"."safety_supervision_statement" ADD CONSTRAINT "safety_supervision_statement_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table ventilation_route
-- ----------------------------
ALTER TABLE "public"."ventilation_route" ADD CONSTRAINT "ventilation_route_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table ventilation_route
-- ----------------------------
ALTER TABLE "public"."ventilation_route" ADD CONSTRAINT "ventilation_route_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table dispatch
-- ----------------------------
ALTER TABLE "public"."dispatch" ADD CONSTRAINT "dispatch_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "public"."dispatch" ADD CONSTRAINT "dispatch_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table dispatch_map
-- ----------------------------
ALTER TABLE "public"."dispatch_map" ADD CONSTRAINT "dispatch_map_dispatch_group_id_fkey" FOREIGN KEY ("dispatch_group_id") REFERENCES "public"."dispatch_group" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."dispatch_map" ADD CONSTRAINT "dispatch_map_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."dispatch" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area
-- ----------------------------
ALTER TABLE "public"."geolocation_area" ADD CONSTRAINT "geolocation_area_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area_device_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_device_map" ADD CONSTRAINT "geolocation_area_device_map_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area_escape_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_escape_route_map" ADD CONSTRAINT "geolocation_area_escape_route_map_escape_route_id_fkey" FOREIGN KEY ("escape_route_id") REFERENCES "public"."escape_route" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."geolocation_area_escape_route_map" ADD CONSTRAINT "geolocation_area_escape_route_map_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area_safety_supervision_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_safety_supervision_map" ADD CONSTRAINT "geolocation_area_safety_supervision_ma_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."geolocation_area_safety_supervision_map" ADD CONSTRAINT "geolocation_area_safety_supervision_map_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."safety_supervision" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area_safety_supervision_type_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_safety_supervision_type_map" ADD CONSTRAINT "geolocation_area_safety_supervision_ty_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table geolocation_area_ventilation_route_map
-- ----------------------------
ALTER TABLE "public"."geolocation_area_ventilation_route_map" ADD CONSTRAINT "geolocation_area_ventilation_route_ma_ventilation_route_id_fkey" FOREIGN KEY ("ventilation_route_id") REFERENCES "public"."ventilation_route" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."geolocation_area_ventilation_route_map" ADD CONSTRAINT "geolocation_area_ventilation_route_map_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table safety_supervision
-- ----------------------------
ALTER TABLE "public"."safety_supervision" ADD CONSTRAINT "safety_supervision_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "public"."safety_supervision" ADD CONSTRAINT "safety_supervision_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table safety_supervision_realtime
-- ----------------------------
ALTER TABLE "public"."safety_supervision_realtime" ADD CONSTRAINT "safety_supervision_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."safety_supervision" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee" CASCADE;
CREATE TABLE "public"."employee" (
  "id" SERIAL NOT NULL,
  "serial" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "job" varchar COLLATE "pg_catalog"."default",
  "work_kind" varchar COLLATE "pg_catalog"."default",
  "work_time" timestamptz(6),
  "sex" "public"."sex_enum",
  "birthday" timestamptz(6),
  "education" varchar COLLATE "pg_catalog"."default",
  "major" varchar COLLATE "pg_catalog"."default",
  "telephone" varchar COLLATE "pg_catalog"."default",
  "identity_card" varchar COLLATE "pg_catalog"."default",
  "nation" varchar COLLATE "pg_catalog"."default",
  "native_place" varchar COLLATE "pg_catalog"."default",
  "marital_status" varchar COLLATE "pg_catalog"."default",
  "polity" varchar COLLATE "pg_catalog"."default",
  "address" varchar COLLATE "pg_catalog"."default",
  "height" float8,
  "weight" float8,
  "system_title" text COLLATE "pg_catalog"."default",
  "system_title_en" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6),
  CONSTRAINT "employee_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "employee_serial_key" UNIQUE ("serial")
);

CREATE UNIQUE INDEX "ix_employee_card_id" ON "public"."employee" USING btree (
  "card_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE TRIGGER "upd_timestamp_employee" BEFORE UPDATE ON "public"."employee"
    FOR EACH ROW
    EXECUTE PROCEDURE "public"."updated_timestamp"();

COMMENT ON COLUMN "public"."employee"."id" IS '自增ID';
COMMENT ON COLUMN "public"."employee"."serial" IS '人员编号';
COMMENT ON COLUMN "public"."employee"."name" IS '人员姓名';
COMMENT ON COLUMN "public"."employee"."card_id" IS '识别卡编号';
COMMENT ON COLUMN "public"."employee"."department" IS '部门';
COMMENT ON COLUMN "public"."employee"."job" IS '职务';
COMMENT ON COLUMN "public"."employee"."work_kind" IS '工种';
COMMENT ON COLUMN "public"."employee"."work_time" IS '参加工作时间';
COMMENT ON COLUMN "public"."employee"."sex" IS '性别';
COMMENT ON COLUMN "public"."employee"."birthday" IS '出生日期';
COMMENT ON COLUMN "public"."employee"."education" IS '教育程度';
COMMENT ON COLUMN "public"."employee"."major" IS '专业';
COMMENT ON COLUMN "public"."employee"."telephone" IS '联系方式';
COMMENT ON COLUMN "public"."employee"."identity_card" IS '身份证号';
COMMENT ON COLUMN "public"."employee"."nation" IS '民族';
COMMENT ON COLUMN "public"."employee"."native_place" IS '籍贯';
COMMENT ON COLUMN "public"."employee"."marital_status" IS '婚姻状况';
COMMENT ON COLUMN "public"."employee"."polity" IS '政治面貌';
COMMENT ON COLUMN "public"."employee"."address" IS '地址';
COMMENT ON COLUMN "public"."employee"."height" IS '身高';
COMMENT ON COLUMN "public"."employee"."weight" IS '体重';
COMMENT ON COLUMN "public"."employee"."system_title" IS '系统标题';
COMMENT ON COLUMN "public"."employee"."system_title_en" IS '系统英文标题';
COMMENT ON TABLE "public"."employee" IS '员工信息表';

-- ----------------------------
-- Table structure for class_set
-- ----------------------------
DROP TABLE IF EXISTS "public"."class_set" CASCADE;
CREATE TABLE "public"."class_set" (
  "id" Serial NOT NULL ,
  "class_id" int4 NOT NULL,
  "type" text COLLATE "pg_catalog"."default" NOT NULL,
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "begin_time" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "end_time" varchar COLLATE "pg_catalog"."default" NOT NULL,
  CONSTRAINT "class_set_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "class_set_class_id_key" UNIQUE ("class_id"),
  CONSTRAINT "class_set_name_key" UNIQUE ("name")
);

COMMENT ON COLUMN "public"."class_set"."id" IS '自增ID';
COMMENT ON COLUMN "public"."class_set"."class_id" IS '班次ID';
COMMENT ON COLUMN "public"."class_set"."type" IS '班次类型';
COMMENT ON COLUMN "public"."class_set"."name" IS '班次名称';
COMMENT ON COLUMN "public"."class_set"."begin_time" IS '班次开始时间';
COMMENT ON COLUMN "public"."class_set"."end_time" IS '班次结束时间';
COMMENT ON TABLE "public"."class_set" IS '班次信息表';


-- ----------------------------
-- Table structure for location_identifiers
-- ----------------------------
DROP TABLE IF EXISTS "public"."location_identifiers" CASCADE;
CREATE TABLE "public"."location_identifiers" (
  "id" Serial NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_point" "public"."geometry",
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "parent_id" text COLLATE "pg_catalog"."default",
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "use_type" int4 NOT NULL DEFAULT 1,
  "view_type" int4 DEFAULT 3,
  "system_id" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "data_type" varchar COLLATE "pg_catalog"."default",
  "point_type" int4,
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6),
  CONSTRAINT "location_identifiers_pkey" PRIMARY KEY ("id"),
  CONSTRAINT "location_identifiers_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT "location_identifiers_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

CREATE INDEX "idx_location_identifiers_geometry_point" ON "public"."location_identifiers" USING gist (
  "geometry_point" "public"."gist_geometry_ops_2d"
);

CREATE UNIQUE INDEX "ix_location_identifiers_point_id" ON "public"."location_identifiers" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

CREATE TRIGGER "upd_timestamp_location_identifiers" BEFORE UPDATE ON "public"."location_identifiers"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

CREATE TRIGGER "update_location_identifiers_geolocation_area_id" AFTER INSERT OR UPDATE OF "geometry_point" ON "public"."location_identifiers"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_point"();

COMMENT ON COLUMN "public"."location_identifiers"."id" IS '自增ID';
COMMENT ON COLUMN "public"."location_identifiers"."point_id" IS '识别器编号';
COMMENT ON COLUMN "public"."location_identifiers"."name" IS '识别器名称';
COMMENT ON COLUMN "public"."location_identifiers"."location" IS '识别器安装位置';
COMMENT ON COLUMN "public"."location_identifiers"."geometry_point" IS '坐标点';
COMMENT ON COLUMN "public"."location_identifiers"."mining_area_id" IS '采区ID';
COMMENT ON COLUMN "public"."location_identifiers"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."location_identifiers"."parent_id" IS '上级编号';
COMMENT ON COLUMN "public"."location_identifiers"."tunnel_guid" IS '巷道guid';
COMMENT ON COLUMN "public"."location_identifiers"."use_type" IS '使用类型';
COMMENT ON TABLE "public"."location_identifiers" IS '人员定位识别器表';

-- ----------------------------
-- Table structure for position
-- ----------------------------
DROP TABLE IF EXISTS "public"."position" CASCADE;
CREATE TABLE "public"."position" (
 "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
 "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
 "prev_point_id" varchar COLLATE "pg_catalog"."default",
 "prev_timestamp" timestamptz(6),
 "downhole_time" timestamptz(6) NOT NULL,
 "timestamp" timestamptz(6) NOT NULL,
 "class_id" int4 NOT NULL,
 "geometry_point" "public"."geometry",
 "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
 "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
 CONSTRAINT "position_pkey" PRIMARY KEY ("card_id"),
 CONSTRAINT "position_card_id_fkey" FOREIGN KEY ("card_id") REFERENCES "public"."employee" ("card_id") ON DELETE CASCADE ON UPDATE CASCADE,
 CONSTRAINT "position_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."class_set" ("class_id") ON DELETE CASCADE ON UPDATE CASCADE,
 CONSTRAINT "position_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."location_identifiers" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE,
 CONSTRAINT "position_prev_point_id_fkey" FOREIGN KEY ("prev_point_id") REFERENCES "public"."location_identifiers" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE
);

COMMENT ON COLUMN "public"."position"."card_id" IS '人员识别卡ID';
COMMENT ON COLUMN "public"."position"."point_id" IS '定位基站ID';
COMMENT ON COLUMN "public"."position"."prev_point_id" IS '前一个基站ID';
COMMENT ON COLUMN "public"."position"."prev_timestamp" IS '离开前一个基站的时间';
COMMENT ON COLUMN "public"."position"."downhole_time" IS '下井时间';
COMMENT ON COLUMN "public"."position"."timestamp" IS '到达当前基站的时间';
COMMENT ON COLUMN "public"."position"."class_id" IS '班次ID';
COMMENT ON COLUMN "public"."position"."geometry_point" IS '坐标';
COMMENT ON COLUMN "public"."position"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."position"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."position" IS '实时位置表';

CREATE TRIGGER "upd_timestamp_position" BEFORE UPDATE ON "public"."position"
    FOR EACH ROW
    EXECUTE PROCEDURE "public"."updated_timestamp"();


-- ----------------------------
-- Table structure for schedule
-- ----------------------------
DROP TABLE IF EXISTS "public"."schedule" CASCADE;
CREATE TABLE "public"."schedule" (
     "id" Serial NOT NULL,
     "date" date NOT NULL,
     "class_id" int4 NOT NULL,
     "type" text COLLATE "pg_catalog"."default" NOT NULL,
     CONSTRAINT "schedule_pkey" PRIMARY KEY ("id"),
     CONSTRAINT "schedule_class_id_fkey" FOREIGN KEY ("class_id") REFERENCES "public"."class_set" ("id") ON DELETE CASCADE ON UPDATE RESTRICT,
     CONSTRAINT "schedule_date_class_id_type_key" UNIQUE ("date", "class_id", "type")
);

COMMENT ON TABLE "public"."schedule" IS '排班表';

-- ----------------------------
-- Table structure for schedule_x_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."schedule_x_employee" CASCADE;
CREATE TABLE "public"."schedule_x_employee" (
    "id" Serial NOT NULL,
    "employee_id" int4 NOT NULL,
    "schedule_id" int4 NOT NULL,
    "type" text COLLATE "pg_catalog"."default" NOT NULL,
    CONSTRAINT "schedule_x_employee_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "schedule_x_employee_employee_id_fkey" FOREIGN KEY ("employee_id") REFERENCES "public"."employee" ("id") ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT "schedule_x_employee_schedule_id_fkey" FOREIGN KEY ("schedule_id") REFERENCES "public"."schedule" ("id") ON DELETE CASCADE ON UPDATE RESTRICT,
    CONSTRAINT "schedule_x_employee_employee_id_schedule_id_type_key" UNIQUE ("employee_id", "schedule_id", "type")
);

COMMENT ON TABLE "public"."schedule_x_employee" IS '排班-人员-映射表';


-- ----------------------------
-- Table structure for employee_help
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee_help" CASCADE;
CREATE TABLE "public"."employee_help" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "downhole_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "begin_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "elapse_time" float8,
  "area_id" varchar COLLATE "pg_catalog"."default",
  "enter_area_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "enter_station_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "prev_point_id" varchar COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of employee_help
-- ----------------------------

-- ----------------------------
-- Table structure for employee_help_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee_help_statistics" CASCADE;
CREATE TABLE "public"."employee_help_statistics" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "downhole_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "begin_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "elapse_time" float8,
  "area_id" varchar COLLATE "pg_catalog"."default",
  "enter_area_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "enter_station_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "prev_point_id" varchar COLLATE "pg_catalog"."default",
  "end_time" timestamptz(6) DEFAULT NULL::timestamp without time zone
)
;

-- ----------------------------
-- Records of employee_help_statistics
-- ----------------------------

-- ----------------------------
-- Table structure for employee_timeout
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee_timeout" CASCADE;
CREATE TABLE "public"."employee_timeout" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "downhole_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "begin_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "elapse_time" float8,
  "area_id" varchar COLLATE "pg_catalog"."default",
  "enter_area_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "enter_station_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "prev_point_id" varchar COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of employee_timeout
-- ----------------------------

-- ----------------------------
-- Table structure for employee_timeout_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."employee_timeout_statistics" CASCADE;
CREATE TABLE "public"."employee_timeout_statistics" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "downhole_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "begin_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "elapse_time" float8,
  "area_id" varchar COLLATE "pg_catalog"."default",
  "enter_area_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "enter_station_time" timestamptz(6) DEFAULT NULL::timestamp without time zone,
  "prev_point_id" varchar COLLATE "pg_catalog"."default",
  "end_time" timestamptz(6) DEFAULT NULL::timestamp without time zone
);

-- ----------------------------
-- Table structure for enterprise
-- ----------------------------
DROP TABLE IF EXISTS "public"."enterprise" CASCADE;
CREATE TABLE "public"."enterprise" (
   "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
   "scope" varchar COLLATE "pg_catalog"."default",
   "mine_development" varchar COLLATE "pg_catalog"."default",
   "barmaster" varchar COLLATE "pg_catalog"."default",
   "telephone" varchar COLLATE "pg_catalog"."default",
   "maximum_amount" int4,
   "geolocation" varchar COLLATE "pg_catalog"."default",
   "superior_department" int4,
   "serial" varchar COLLATE "pg_catalog"."default" NOT NULL,
   "mineral_reserve" float8,
   "employee_amount" int4,
   "authorized_representative" varchar COLLATE "pg_catalog"."default",
   CONSTRAINT "enterprise_pkey" PRIMARY KEY ("serial")
);

COMMENT ON COLUMN "public"."enterprise"."name" IS '矿井名称';
COMMENT ON COLUMN "public"."enterprise"."scope" IS '矿井范围';
COMMENT ON COLUMN "public"."enterprise"."mine_development" IS '开拓方式';
COMMENT ON COLUMN "public"."enterprise"."barmaster" IS '矿长';
COMMENT ON COLUMN "public"."enterprise"."telephone" IS '联系电话';
COMMENT ON COLUMN "public"."enterprise"."maximum_amount" IS '核定下井人员数量';
COMMENT ON COLUMN "public"."enterprise"."geolocation" IS '地理位置';
COMMENT ON COLUMN "public"."enterprise"."superior_department" IS '上级部门';
COMMENT ON COLUMN "public"."enterprise"."serial" IS '矿井编号';
COMMENT ON COLUMN "public"."enterprise"."mineral_reserve" IS '资源储量';
COMMENT ON COLUMN "public"."enterprise"."employee_amount" IS '在职员工';
COMMENT ON COLUMN "public"."enterprise"."authorized_representative" IS '法人代表';
COMMENT ON TABLE "public"."enterprise" IS '企业基本信息表';



-- ----------------------------
-- Primary Key structure for table employee_help
-- ----------------------------
ALTER TABLE "public"."employee_help" ADD CONSTRAINT "employee_help_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Indexes structure for table employee_help_statistics
-- ----------------------------
CREATE UNIQUE INDEX "employee_help_statistics_card_id_begin_time_idx" ON "public"."employee_help_statistics" USING btree (
  "card_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "begin_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table employee_help_statistics
-- ----------------------------
ALTER TABLE "public"."employee_help_statistics" ADD CONSTRAINT "employee_help_statistics_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Primary Key structure for table employee_timeout
-- ----------------------------
ALTER TABLE "public"."employee_timeout" ADD CONSTRAINT "employee_timeout_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Indexes structure for table employee_timeout_statistics
-- ----------------------------
CREATE UNIQUE INDEX "employee_timeout_statistics_card_id_begin_time_idx" ON "public"."employee_timeout_statistics" USING btree (
  "card_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "begin_time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);

-- ----------------------------
-- Table structure for gas_drainage
-- ----------------------------
DROP TABLE IF EXISTS "public"."gas_drainage" CASCADE;
CREATE TABLE "public"."gas_drainage" (
  "id" SERIAL NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "unit" varchar COLLATE "pg_catalog"."default",
  "label" varchar COLLATE "pg_catalog"."default",
  "point_type" int4 NOT NULL,
  "upper_range_value" float8,
  "lower_range_value" float8,
  "upper_alarm_value" float8,
  "lower_alarm_value" float8,
  "upper_warning_value" float8,
  "lower_warning_value" float8,
  "upper_cut_value" float8,
  "lower_cut_value" float8,
  "upper_reset_value" float8,
  "lower_reset_value" float8,
  "geometry_point" "public"."geometry",
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."gas_drainage"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gas_drainage"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."gas_drainage"."parent_id" IS '传感器上级编号';
COMMENT ON COLUMN "public"."gas_drainage"."name" IS '传感器名称';
COMMENT ON COLUMN "public"."gas_drainage"."location" IS '安装位置';
COMMENT ON COLUMN "public"."gas_drainage"."type" IS '传感器类型';
COMMENT ON COLUMN "public"."gas_drainage"."unit" IS '模拟量单位';
COMMENT ON COLUMN "public"."gas_drainage"."label" IS '传感器描述';
COMMENT ON COLUMN "public"."gas_drainage"."point_type" IS '数据类型';
COMMENT ON COLUMN "public"."gas_drainage"."upper_range_value" IS '量程上限';
COMMENT ON COLUMN "public"."gas_drainage"."lower_range_value" IS '量程下限';
COMMENT ON COLUMN "public"."gas_drainage"."upper_alarm_value" IS '报警上限';
COMMENT ON COLUMN "public"."gas_drainage"."lower_alarm_value" IS '报警下限';
COMMENT ON COLUMN "public"."gas_drainage"."upper_warning_value" IS '预警上限';
COMMENT ON COLUMN "public"."gas_drainage"."lower_warning_value" IS '预警下限';
COMMENT ON COLUMN "public"."gas_drainage"."upper_cut_value" IS '断电上限';
COMMENT ON COLUMN "public"."gas_drainage"."lower_cut_value" IS '断电下限';
COMMENT ON COLUMN "public"."gas_drainage"."upper_reset_value" IS '复电上限';
COMMENT ON COLUMN "public"."gas_drainage"."lower_reset_value" IS '复电下限';
COMMENT ON COLUMN "public"."gas_drainage"."geometry_point" IS '坐标点';
COMMENT ON COLUMN "public"."gas_drainage"."mining_area_id" IS '采区ID';
COMMENT ON COLUMN "public"."gas_drainage"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."gas_drainage"."tunnel_guid" IS '巷道guid';
COMMENT ON COLUMN "public"."gas_drainage"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."gas_drainage"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."gas_drainage" IS '安全监控设备表';

-- ----------------------------
-- Records of gas_drainage
-- ----------------------------

-- ----------------------------
-- Table structure for gas_drainage_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."gas_drainage_realtime" CASCADE;
CREATE TABLE "public"."gas_drainage_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "alarm" bool NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "specific_status" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."gas_drainage_realtime"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."value" IS '实时值';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."state" IS '状态描述';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."alarm" IS '是否报警';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."gas_drainage_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."gas_drainage_realtime" IS '安全监控实时表';

-- ----------------------------
-- Records of gas_drainage_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for gas_patrol
-- ----------------------------
DROP TABLE IF EXISTS "public"."gas_patrol" CASCADE;
CREATE TABLE "public"."gas_patrol" (
  "id" SERIAL NOT NULL,
  "serial" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "value" numeric NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."gas_patrol"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gas_patrol"."serial" IS '巡更仪编号';
COMMENT ON COLUMN "public"."gas_patrol"."point_id" IS '测点ID';
COMMENT ON COLUMN "public"."gas_patrol"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."gas_patrol"."value" IS '数值';
COMMENT ON TABLE "public"."gas_patrol" IS '瓦斯巡更信息表';

-- ----------------------------
-- Records of gas_patrol
-- ----------------------------

-- ----------------------------
-- Triggers structure for table gas_drainage
-- ----------------------------
CREATE TRIGGER "upd_timestamp_gas_drainage" BEFORE UPDATE ON "public"."gas_drainage"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Uniques structure for table gas_drainage
-- ----------------------------
ALTER TABLE "public"."gas_drainage" ADD CONSTRAINT "gas_drainage_point_id_key" UNIQUE ("point_id");

-- ----------------------------
-- Primary Key structure for table gas_drainage
-- ----------------------------
ALTER TABLE "public"."gas_drainage" ADD CONSTRAINT "gas_drainage_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table gas_drainage_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_gas_drainage_realtime" BEFORE UPDATE ON "public"."gas_drainage_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table gas_drainage_realtime
-- ----------------------------
ALTER TABLE "public"."gas_drainage_realtime" ADD CONSTRAINT "gas_drainage_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table gas_patrol
-- ----------------------------
CREATE TRIGGER "upd_timestamp_gas_patrol" BEFORE UPDATE ON "public"."gas_patrol"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Uniques structure for table gas_patrol
-- ----------------------------
ALTER TABLE "public"."gas_patrol" ADD CONSTRAINT "gas_patrol_point_id_key" UNIQUE ("point_id");

-- ----------------------------
-- Primary Key structure for table gas_patrol
-- ----------------------------
ALTER TABLE "public"."gas_patrol" ADD CONSTRAINT "gas_patrol_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table gas_drainage
-- ----------------------------
ALTER TABLE "public"."gas_drainage" ADD CONSTRAINT "gas_drainage_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "public"."gas_drainage" ADD CONSTRAINT "gas_drainage_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table gas_drainage_realtime
-- ----------------------------
ALTER TABLE "public"."gas_drainage_realtime" ADD CONSTRAINT "gas_drainage_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."gas_drainage" ("point_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table gas_patrol
-- ----------------------------
ALTER TABLE "public"."gas_patrol" ADD CONSTRAINT "gas_patrol_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."safety_supervision" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for ground_sound_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."ground_sound_definition" CASCADE;
CREATE TABLE "public"."ground_sound_definition" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "mine_name" varchar(255) COLLATE "pg_catalog"."default",
  "relation_tunnel" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "passageway_amount" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_length" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_length_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_frequency" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_frequency_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_direction" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_direction_name" varchar(255) COLLATE "pg_catalog"."default",
  "install_mode" varchar(255) COLLATE "pg_catalog"."default",
  "sensitivity" varchar(255) COLLATE "pg_catalog"."default",
  "sensitivity_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_type" varchar(255) COLLATE "pg_catalog"."default",
  "frequency" varchar(255) COLLATE "pg_catalog"."default",
  "passageway_number" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
  "threshold" varchar(255) COLLATE "pg_catalog"."default",
  "describe" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."ground_sound_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."ground_sound_definition"."mine_name" IS '矿名称';
COMMENT ON COLUMN "public"."ground_sound_definition"."relation_tunnel" IS '关联巷道';
COMMENT ON COLUMN "public"."ground_sound_definition"."passageway_amount" IS '通道数';
COMMENT ON COLUMN "public"."ground_sound_definition"."sampling_length" IS '采样长度';
COMMENT ON COLUMN "public"."ground_sound_definition"."sampling_length_unit" IS '采样长度单位';
COMMENT ON COLUMN "public"."ground_sound_definition"."sampling_frequency" IS '采样频率';
COMMENT ON COLUMN "public"."ground_sound_definition"."sampling_frequency_unit" IS '采样频率单位';
COMMENT ON COLUMN "public"."ground_sound_definition"."sensor_direction" IS '传感器方向';
COMMENT ON COLUMN "public"."ground_sound_definition"."sensor_direction_name" IS '传感器方向名称';
COMMENT ON COLUMN "public"."ground_sound_definition"."install_mode" IS '安装方式';
COMMENT ON COLUMN "public"."ground_sound_definition"."sensitivity" IS '灵敏度';
COMMENT ON COLUMN "public"."ground_sound_definition"."sensitivity_unit" IS '灵敏度单位';
COMMENT ON COLUMN "public"."ground_sound_definition"."sensor_type" IS '传感器类型';
COMMENT ON COLUMN "public"."ground_sound_definition"."frequency" IS '频次';
COMMENT ON COLUMN "public"."ground_sound_definition"."passageway_number" IS '通道号';
COMMENT ON COLUMN "public"."ground_sound_definition"."threshold" IS '阀值';
COMMENT ON COLUMN "public"."ground_sound_definition"."describe" IS '异常描述';
COMMENT ON COLUMN "public"."ground_sound_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ground_sound_definition"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."ground_sound_definition" IS '地音系统基础信息表';

-- ----------------------------
-- Table structure for ground_sound_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."ground_sound_realtime" CASCADE;
CREATE TABLE "public"."ground_sound_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "relation_tunnel" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "value" float8,
  "passageway_number" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(10) COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."ground_sound_realtime"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."ground_sound_realtime"."relation_tunnel" IS '关联巷道';
COMMENT ON COLUMN "public"."ground_sound_realtime"."value" IS '传感器实时值';
COMMENT ON COLUMN "public"."ground_sound_realtime"."passageway_number" IS '通道号';
COMMENT ON COLUMN "public"."ground_sound_realtime"."status" IS '状态';
COMMENT ON COLUMN "public"."ground_sound_realtime"."timestamp" IS '上传数据时间yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."ground_sound_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."ground_sound_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."ground_sound_realtime" IS '地音系统实时数据信息表';

-- ----------------------------
-- Primary Key structure for table ground_sound_definition
-- ----------------------------
ALTER TABLE "public"."ground_sound_definition" ADD CONSTRAINT "ground_sound_definition_pkey" PRIMARY KEY ("mine_code", "relation_tunnel", "passageway_number");

-- ----------------------------
-- Primary Key structure for table ground_sound_realtime
-- ----------------------------
ALTER TABLE "public"."ground_sound_realtime" ADD CONSTRAINT "ground_sound_realtime_pkey" PRIMARY KEY ("mine_code", "relation_tunnel", "passageway_number");

-- ----------------------------
-- Table structure for item_alarm_status
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_alarm_status" CASCADE;
CREATE TABLE "public"."item_alarm_status" (
  "alarm_status_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_status_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_status_note" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_alarm_status"."alarm_status_code" IS '报警类型编码';
COMMENT ON COLUMN "public"."item_alarm_status"."alarm_status_name" IS '报警类型名称';
COMMENT ON COLUMN "public"."item_alarm_status"."alarm_status_note" IS '报警类型描述';
COMMENT ON TABLE "public"."item_alarm_status" IS '报警状态表';

-- ----------------------------
-- Records of item_alarm_status
-- ----------------------------
INSERT INTO "public"."item_alarm_status" VALUES ('0', '正常', '正常');
INSERT INTO "public"."item_alarm_status" VALUES ('5', '标校报警', '标校报警');
INSERT INTO "public"."item_alarm_status" VALUES ('16', '分站故障', '由于分站故障的原因，导致的数据不正常');
INSERT INTO "public"."item_alarm_status" VALUES ('1', '超限报警', '传感器的测量值超过了设定的报警值');
INSERT INTO "public"."item_alarm_status" VALUES ('128', '传感器故障', '由于传感器故障的原因，导致的数据不正常');
INSERT INTO "public"."item_alarm_status" VALUES ('2', '断电报警', '传感器的测量值超过了设定的断电值');
INSERT INTO "public"."item_alarm_status" VALUES ('32', '不巡检', '由于分站不参加巡检而导致的传感数据不正常');
INSERT INTO "public"."item_alarm_status" VALUES ('4', '标校', '在对传感器进行调校,其数据不是真实情况的反应');
INSERT INTO "public"."item_alarm_status" VALUES ('64', '暂停', '人为的设置，传感器处于暂停状态');
INSERT INTO "public"."item_alarm_status" VALUES ('8', '超量程', '被测对象已经超出了传感器的测量范围');

-- ----------------------------
-- Table structure for item_alarm_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_alarm_type" CASCADE;
CREATE TABLE "public"."item_alarm_type" (
  "alarm_type_code" varchar(5) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_type_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_alarm_type"."alarm_type_code" IS '报警类型编码';
COMMENT ON COLUMN "public"."item_alarm_type"."alarm_type_name" IS '报警类型名称';
COMMENT ON TABLE "public"."item_alarm_type" IS '报警类型表';

-- ----------------------------
-- Records of item_alarm_type
-- ----------------------------
INSERT INTO "public"."item_alarm_type" VALUES ('001', '超限报警');
INSERT INTO "public"."item_alarm_type" VALUES ('002', '断电报警');
INSERT INTO "public"."item_alarm_type" VALUES ('003', '馈电异常');
INSERT INTO "public"."item_alarm_type" VALUES ('004', '传感器断线');
INSERT INTO "public"."item_alarm_type" VALUES ('005', '基站断电');
INSERT INTO "public"."item_alarm_type" VALUES ('006', '基站不通');
INSERT INTO "public"."item_alarm_type" VALUES ('007', '标校');
INSERT INTO "public"."item_alarm_type" VALUES ('008', '超量程');
INSERT INTO "public"."item_alarm_type" VALUES ('009', '超上限预警');
INSERT INTO "public"."item_alarm_type" VALUES ('010', '超下限预警');

-- ----------------------------
-- Table structure for item_point_value_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_point_value_type" CASCADE;
CREATE TABLE "public"."item_point_value_type" (
  "point_value_type_code" varchar(2) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_point_value_type"."point_value_type_code" IS '测点分类编码,';
COMMENT ON COLUMN "public"."item_point_value_type"."point_value_type_name" IS '测点分类名称,';
COMMENT ON TABLE "public"."item_point_value_type" IS '测点分类表';

-- ----------------------------
-- Records of item_point_value_type
-- ----------------------------
INSERT INTO "public"."item_point_value_type" VALUES ('0', '模拟量');
INSERT INTO "public"."item_point_value_type" VALUES ('1', '开关量');
INSERT INTO "public"."item_point_value_type" VALUES ('2', '多态量');
INSERT INTO "public"."item_point_value_type" VALUES ('3', '累计量');
INSERT INTO "public"."item_point_value_type" VALUES ('4', '文本量');

-- ----------------------------
-- Table structure for item_power_status
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_power_status" CASCADE;
CREATE TABLE "public"."item_power_status" (
  "power_status_code" int4 NOT NULL,
  "power_status_name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."item_power_status"."power_status_code" IS '供电状态编码';
COMMENT ON COLUMN "public"."item_power_status"."power_status_name" IS '供电状态描述';
COMMENT ON TABLE "public"."item_power_status" IS '分站/基站供电状态';

-- ----------------------------
-- Records of item_power_status
-- ----------------------------
INSERT INTO "public"."item_power_status" VALUES (0, '直流供电');
INSERT INTO "public"."item_power_status" VALUES (1, '交流供电');
INSERT INTO "public"."item_power_status" VALUES (2, '电源故障');
INSERT INTO "public"."item_power_status" VALUES (9, '未知');

-- ----------------------------
-- Table structure for item_run_status
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_run_status" CASCADE;
CREATE TABLE "public"."item_run_status" (
  "run_status_code" int4 NOT NULL,
  "run_status_name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."item_run_status"."run_status_code" IS '运行状态编码';
COMMENT ON COLUMN "public"."item_run_status"."run_status_name" IS '运行状态描述';
COMMENT ON TABLE "public"."item_run_status" IS '分站/基站运行状态';

-- ----------------------------
-- Records of item_run_status
-- ----------------------------
INSERT INTO "public"."item_run_status" VALUES (0, '通讯正常');
INSERT INTO "public"."item_run_status" VALUES (1, '通讯故障');
INSERT INTO "public"."item_run_status" VALUES (2, '故障');
INSERT INTO "public"."item_run_status" VALUES (9, '未知');

-- ----------------------------
-- Table structure for item_safety_value
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_safety_value" CASCADE;
CREATE TABLE "public"."item_safety_value" (
  "value" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "value_description" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_safety_value"."value" IS '安监value 值';
COMMENT ON COLUMN "public"."item_safety_value"."value_description" IS '安监value 值';
COMMENT ON TABLE "public"."item_safety_value" IS '安监value值 描述对照表';

-- ----------------------------
-- Records of item_safety_value
-- ----------------------------

-- ----------------------------
-- Table structure for item_sensor_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_sensor_type" CASCADE;
CREATE TABLE "public"."item_sensor_type" (
  "sensor_type_code" varchar(6) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_code" varchar(2) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_note" varchar(50) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_sensor_type"."sensor_type_code" IS '传感器编码,';
COMMENT ON COLUMN "public"."item_sensor_type"."sensor_type_name" IS '传感器编码名称,';
COMMENT ON COLUMN "public"."item_sensor_type"."point_value_type_code" IS '测点分类表,';
COMMENT ON COLUMN "public"."item_sensor_type"."sensor_type_note" IS '传感器编码描述,';
COMMENT ON TABLE "public"."item_sensor_type" IS '传感器分类字典表';

-- ----------------------------
-- Records of item_sensor_type
-- ----------------------------
INSERT INTO "public"."item_sensor_type" VALUES ('0001', '环境瓦斯', '0', '环境瓦斯');
INSERT INTO "public"."item_sensor_type" VALUES ('0002', '风速', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0003', '环境温度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0004', '一氧化碳', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0005', '风压', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0006', '负压', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0007', '水池水位', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0008', '煤位', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0009', '硫化氢', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0010', '水温度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0011', '高低浓瓦斯', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0012', '氧气', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0013', '二氧化碳', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0014', '粉尘', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0015', '电压', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0016', '频率', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0017', '电流', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0018', '湿度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0019', '风量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0020', '顶板离层位移', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0021', '坝体位移', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0022', '管道瓦斯', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0023', '管道温度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0024', '水质', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0025', '管道压力', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0026', '轴承温度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0027', '噪音', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0028', '电机温度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0029', '水库水位', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0030', '浸润线', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0031', '降雨量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0032', '液压压力', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0033', '围岩应力', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0034', '钻孔应力', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0035', '锚杆应力', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0036', '混合瓦斯流量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0037', '纯瓦斯流量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0038', '管道一氧化碳', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0039', '氢气', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0040', '管道流量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0041', '二氧化氮', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0042', '二氧化硫', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0043', '激光甲烷', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0044', '氨气', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0045', '氮气', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0046', '乙烯', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0047', '乙烷', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0048', '压强', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0049', '液位', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0050', '物位', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0051', '开度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0052', '高度', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0053', '流量', '0', '');
INSERT INTO "public"."item_sensor_type" VALUES ('0054', '压力', '0', ' ');
INSERT INTO "public"."item_sensor_type" VALUES ('0055', '局扇', '1', ' ');
INSERT INTO "public"."item_sensor_type" VALUES ('0056', '甲烷', '0', ' ');
INSERT INTO "public"."item_sensor_type" VALUES ('1001', '局部通风机', '1', '0 表示停，1 表示开');
INSERT INTO "public"."item_sensor_type" VALUES ('1002', '风门', '1', '0 表示风门关闭，1 表示风门打开');
INSERT INTO "public"."item_sensor_type" VALUES ('1003', '风筒状态', '1', '0 表示风筒无风，1 表示风筒有风');
INSERT INTO "public"."item_sensor_type" VALUES ('1004', '设备开停', '1', '0 表示停止，1 表示开');
INSERT INTO "public"."item_sensor_type" VALUES ('1005', '开关', '1', '0 表示关，1 表示开');
INSERT INTO "public"."item_sensor_type" VALUES ('1006', '风向', '1', '0 逆风，1顺风');
INSERT INTO "public"."item_sensor_type" VALUES ('1007', '煤仓空满', '1', '0 表示空仓，1 表示满仓');
INSERT INTO "public"."item_sensor_type" VALUES ('1008', '烟雾', '1', '0 表示无烟雾，1 表示有烟雾');
INSERT INTO "public"."item_sensor_type" VALUES ('1009', '断电器', '1', '0 表示断电，1 表示复电');
INSERT INTO "public"."item_sensor_type" VALUES ('1010', '主通风机', '1', '0 表示停，1 表示开');
INSERT INTO "public"."item_sensor_type" VALUES ('1011', '馈电器', '1', '0 表示负荷侧无电压，1 表示负荷侧有电压');
INSERT INTO "public"."item_sensor_type" VALUES ('1012', '声光报警器', '1', '0 表示无报警，1 表示报警');
INSERT INTO "public"."item_sensor_type" VALUES ('1013', '计量开停控制器', '1', '0 表示停止，1 表示开启');
INSERT INTO "public"."item_sensor_type" VALUES ('1014', '控制量', '1', '0 表示断开，1 表示合并');
INSERT INTO "public"."item_sensor_type" VALUES ('1015', '馈电', '1', '0 表示关，1 表示开');
INSERT INTO "public"."item_sensor_type" VALUES ('3001', '产量', '3', '');
INSERT INTO "public"."item_sensor_type" VALUES ('3002', '瓦斯抽放量', '3', '');
INSERT INTO "public"."item_sensor_type" VALUES ('3003', '排水量', '3', '');
INSERT INTO "public"."item_sensor_type" VALUES ('3004', '钩数', '3', '');
INSERT INTO "public"."item_sensor_type" VALUES ('3005', '水流量', '3', '');
INSERT INTO "public"."item_sensor_type" VALUES ('4001', '分站', '1', '0 表示故障，1 表示正常');
INSERT INTO "public"."item_sensor_type" VALUES ('4002', '电源状态', '1', '0 表示无电，1 表示交流供电，2 表示直流供电');
INSERT INTO "public"."item_sensor_type" VALUES ('1016', '皮带', '1', '0停 1开');
INSERT INTO "public"."item_sensor_type" VALUES ('1017', '水泵', '1', '0停1开');
INSERT INTO "public"."item_sensor_type" VALUES ('1099', '其他（开）', '1', '0开1关');
INSERT INTO "public"."item_sensor_type" VALUES ('0099', '其他（模）', '0', ' ');

-- ----------------------------
-- Table structure for item_system_org
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_system_org" CASCADE;
CREATE TABLE "public"."item_system_org" (
  "system_org_code" varchar(2) COLLATE "pg_catalog"."default" NOT NULL,
  "system_org_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "system_org_alias" varchar(10) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_system_org"."system_org_code" IS '系统编码,';
COMMENT ON COLUMN "public"."item_system_org"."system_org_name" IS '系统名称,';
COMMENT ON COLUMN "public"."item_system_org"."system_org_alias" IS '系统简称,';
COMMENT ON TABLE "public"."item_system_org" IS '系统字典表';

-- ----------------------------
-- Records of item_system_org
-- ----------------------------
INSERT INTO "public"."item_system_org" VALUES ('01', '安全监控系统', 'aqjk');
INSERT INTO "public"."item_system_org" VALUES ('02', '瓦斯抽放系统', 'wscf');
INSERT INTO "public"."item_system_org" VALUES ('03', '井下作业人员管理系统', 'jxry');
INSERT INTO "public"."item_system_org" VALUES ('04', '工业视频监控系统', 'gysp');

-- ----------------------------
-- Table structure for item_unit_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."item_unit_info" CASCADE;
CREATE TABLE "public"."item_unit_info" (
  "unit_code" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "unit_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."item_unit_info"."unit_code" IS '测点单位编码,';
COMMENT ON COLUMN "public"."item_unit_info"."unit_name" IS '测点单位名称,';
COMMENT ON TABLE "public"."item_unit_info" IS '测点单位表';

-- ----------------------------
-- Records of item_unit_info
-- ----------------------------
INSERT INTO "public"."item_unit_info" VALUES ('%', '湿度');
INSERT INTO "public"."item_unit_info" VALUES ('℃', '温度');
INSERT INTO "public"."item_unit_info" VALUES ('A', '电流');
INSERT INTO "public"."item_unit_info" VALUES ('%CH4', '甲烷');
INSERT INTO "public"."item_unit_info" VALUES ('dB', '噪音');
INSERT INTO "public"."item_unit_info" VALUES ('Hz', '低频率');
INSERT INTO "public"."item_unit_info" VALUES ('kHz', '高频率');
INSERT INTO "public"."item_unit_info" VALUES ('kpa', '压力');
INSERT INTO "public"."item_unit_info" VALUES ('kV', '高电压');
INSERT INTO "public"."item_unit_info" VALUES ('kw', '功率');
INSERT INTO "public"."item_unit_info" VALUES ('kW·h', '电度');
INSERT INTO "public"."item_unit_info" VALUES ('m', '位移');
INSERT INTO "public"."item_unit_info" VALUES ('m3/min', '流量');
INSERT INTO "public"."item_unit_info" VALUES ('mg/m3', '粉尘');
INSERT INTO "public"."item_unit_info" VALUES ('mm', '降雨量');
INSERT INTO "public"."item_unit_info" VALUES ('mol/l', '水质');
INSERT INTO "public"."item_unit_info" VALUES ('m/s', '风速');
INSERT INTO "public"."item_unit_info" VALUES ('ppm', '浓度');
INSERT INTO "public"."item_unit_info" VALUES ('TN/m2', '顶板压力');
INSERT INTO "public"."item_unit_info" VALUES ('V', '低电压');

-- ----------------------------
-- Primary Key structure for table item_alarm_status
-- ----------------------------
ALTER TABLE "public"."item_alarm_status" ADD CONSTRAINT "item_alarm_status_pkey" PRIMARY KEY ("alarm_status_code");

-- ----------------------------
-- Primary Key structure for table item_alarm_type
-- ----------------------------
ALTER TABLE "public"."item_alarm_type" ADD CONSTRAINT "item_alarm_type_pkey" PRIMARY KEY ("alarm_type_code");

-- ----------------------------
-- Primary Key structure for table item_point_value_type
-- ----------------------------
ALTER TABLE "public"."item_point_value_type" ADD CONSTRAINT "item_point_value_type_pkey" PRIMARY KEY ("point_value_type_code");

-- ----------------------------
-- Primary Key structure for table item_power_status
-- ----------------------------
ALTER TABLE "public"."item_power_status" ADD CONSTRAINT "item_power_status_pkey" PRIMARY KEY ("power_status_code");

-- ----------------------------
-- Primary Key structure for table item_run_status
-- ----------------------------
ALTER TABLE "public"."item_run_status" ADD CONSTRAINT "item_run_status_pkey" PRIMARY KEY ("run_status_code");

-- ----------------------------
-- Primary Key structure for table item_safety_value
-- ----------------------------
ALTER TABLE "public"."item_safety_value" ADD CONSTRAINT "item_alarm_status_copy1_pkey" PRIMARY KEY ("value");

-- ----------------------------
-- Primary Key structure for table item_sensor_type
-- ----------------------------
ALTER TABLE "public"."item_sensor_type" ADD CONSTRAINT "item_sensor_type_pkey" PRIMARY KEY ("sensor_type_code");

-- ----------------------------
-- Primary Key structure for table item_system_org
-- ----------------------------
ALTER TABLE "public"."item_system_org" ADD CONSTRAINT "item_system_org_pkey" PRIMARY KEY ("system_org_code");

-- ----------------------------
-- Primary Key structure for table item_unit_info
-- ----------------------------
ALTER TABLE "public"."item_unit_info" ADD CONSTRAINT "item_unit_info_pkey" PRIMARY KEY ("unit_code");


-- ----------------------------
-- Table structure for location_identifiers_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."location_identifiers_realtime" CASCADE;
CREATE TABLE "public"."location_identifiers_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "alarm" bool NOT NULL,
  "alarm_reason" varchar COLLATE "pg_catalog"."default",
  "specific_status" varchar COLLATE "pg_catalog"."default",
  CONSTRAINT "location_identifiers_realtime_pkey" PRIMARY KEY ("point_id"),
  CONSTRAINT "location_identifiers_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."location_identifiers" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE
)
;
COMMENT ON COLUMN "public"."location_identifiers_realtime"."point_id" IS '识别器编号';
COMMENT ON COLUMN "public"."location_identifiers_realtime"."state" IS '设备运行状态';
COMMENT ON COLUMN "public"."location_identifiers_realtime"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."location_identifiers_realtime"."alarm" IS '是否报警';
COMMENT ON COLUMN "public"."location_identifiers_realtime"."alarm_reason" IS '报警原因';
COMMENT ON TABLE "public"."location_identifiers_realtime" IS '人员定位识别器实时表';

-- ----------------------------
-- Table structure for substation_route
-- ----------------------------
DROP TABLE IF EXISTS "public"."substation_route" CASCADE;
CREATE TABLE "public"."substation_route" (
     "id" Serial NOT NULL,
     "begin_point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
     "end_point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
     "color" varchar COLLATE "pg_catalog"."default" NOT NULL,
     "geometry_line" "public"."geometry",
     "width" int4 NOT NULL,
     "tunnel_guid" json,
     CONSTRAINT "substation_route_pkey" PRIMARY KEY ("id"),
     CONSTRAINT "substation_route_begin_point_id_fkey" FOREIGN KEY ("begin_point_id") REFERENCES "public"."location_identifiers" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE,
     CONSTRAINT "substation_route_end_point_id_fkey" FOREIGN KEY ("end_point_id") REFERENCES "public"."location_identifiers" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE INDEX "idx_substation_route_geometry_line" ON "public"."substation_route" USING gist (
  "geometry_line" "public"."gist_geometry_ops_2d"
);

COMMENT ON COLUMN "public"."substation_route"."id" IS '自增ID';
COMMENT ON COLUMN "public"."substation_route"."begin_point_id" IS '起始分战编号';
COMMENT ON COLUMN "public"."substation_route"."end_point_id" IS '结束分站编号';
COMMENT ON COLUMN "public"."substation_route"."color" IS '路线颜色';
COMMENT ON COLUMN "public"."substation_route"."geometry_line" IS '路线坐标';
COMMENT ON COLUMN "public"."substation_route"."width" IS '路线宽度';
COMMENT ON COLUMN "public"."substation_route"."tunnel_guid" IS '巷道guid';
COMMENT ON TABLE "public"."substation_route" IS '分站路线管理';


-- ----------------------------
-- Table structure for model_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_system" CASCADE;
CREATE TABLE "public"."model_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "conf_table" varchar COLLATE "pg_catalog"."default",
  "update_table" varchar COLLATE "pg_catalog"."default",
  "history_table" varchar COLLATE "pg_catalog"."default",
  "conf_table_struct" text COLLATE "pg_catalog"."default",
  "category" varchar COLLATE "pg_catalog"."default",
  "flag" varchar(255) COLLATE "pg_catalog"."default",
  "definition_routing_key" varchar(255) COLLATE "pg_catalog"."default",
  "update_routing_key" varchar(255) COLLATE "pg_catalog"."default",
  "mine_id" int4,
  "sid" int4,
  "route_service" int2 NULL DEFAULT 0,
  "warn_service" int2 NULL DEFAULT 0,
  "converter_service" int2 NULL DEFAULT 0,
  "early_warn_service" int2 NULL DEFAULT 0,
  "exchange" varchar COLLATE "pg_catalog"."default",
  "access_configurable" int2,
  "link_id" varchar(128) NULL
)
;
COMMENT ON COLUMN "public"."model_system"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."model_system"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."model_system"."definition_routing_key" IS '定义routkingkey';
COMMENT ON COLUMN "public"."model_system"."update_routing_key" IS '实时值routingkey';
COMMENT ON COLUMN "public"."model_system"."mine_id" IS '对应mine_name id';
COMMENT ON COLUMN "public"."model_system"."sid" IS '外键关联mine_system';
COMMENT ON COLUMN "public"."model_system"."early_warn_service" IS '是否开启灾害预警';
COMMENT ON COLUMN "public"."model_system"."access_configurable" IS '是否开启接入配置';
COMMENT ON COLUMN "public"."model_system"."link_id" IS '全链路监控排查ID';
COMMENT ON TABLE "public"."model_system" IS '子系统信息';

-- ----------------------------
-- Records of model_system
-- ----------------------------

INSERT INTO "public"."model_system" ("id", "name", "created_at", "updated_at", "conf_table", "update_table", "history_table", "conf_table_struct", "category", "flag", "definition_routing_key", "update_routing_key", "mine_id", "sid", "route_service", "warn_service", "converter_service", "exchange") VALUES ('999999', '派生点', '2019-09-01 10:19:25.439465+08', '2022-05-06 10:51:39.300799+08', 'data_derive_definition', 'data_derive_realtime', 'data_derive_realtime', '{"name": {"label": "名称","type": "String","isUpdate":"true"},
        "point_id": {"label": "点Id","type": "String","isUpdate":"false"},
        "system_id": {"label": "系统Id","type": "String","isUpdate":"false"},
        "data_type": {"label": "值类型","type": "String","isUpdate":"true"},
        "point_type": {"label": "点类型","type": "Int","isUpdate":"true"},
        "company_code": {"label": "矿名称","type": "String","isUpdate":"true"},
        "group_code": {"label": "集团名称","type": "String","isUpdate":"true"},
        "unit": {"label": "单位","type": "String","isUpdate":"true"},
        "type": {"label": "类型","type": "String","isUpdate":"true"},
        "parent_system_id": {"label": "父系统","type": "String","isUpdate":"true"}
}', NULL, '1', '', 'update.10040.100052.999999.派生点', 100052, 999, 0, 1, 0, 'ex_derive');

-- ----------------------------
-- Table structure for model_system_alarm
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_system_alarm" CASCADE;
CREATE TABLE "public"."model_system_alarm" (
  "id" SERIAL NOT NULL,
  "indictor_id" int4 NOT NULL,
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_value" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "content" varchar(255) COLLATE "pg_catalog"."default",
  "is_flag" int2 NOT NULL DEFAULT 0,
  "mine_name" varchar(255) COLLATE "pg_catalog"."default",
  "type_id" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."model_system_alarm"."id" IS '主键';
COMMENT ON COLUMN "public"."model_system_alarm"."indictor_id" IS '预警指标库id';
COMMENT ON COLUMN "public"."model_system_alarm"."point_id" IS '点位Id';
COMMENT ON COLUMN "public"."model_system_alarm"."alarm_value" IS '报警值';
COMMENT ON COLUMN "public"."model_system_alarm"."content" IS '报警内容';
COMMENT ON COLUMN "public"."model_system_alarm"."is_flag" IS '是否为标志位， 0否1是';
COMMENT ON COLUMN "public"."model_system_alarm"."mine_name" IS '矿名';
COMMENT ON COLUMN "public"."model_system_alarm"."type_id" IS '指标类型';
COMMENT ON COLUMN "public"."model_system_alarm"."condition" IS '条件';
COMMENT ON TABLE "public"."model_system_alarm" IS '子系统预警报警';

-- ----------------------------
-- Table structure for model_system_alive
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_system_alive" CASCADE;
CREATE TABLE "public"."model_system_alive" (
  "system_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "time_limit" int4 NULL,
  "mine_id" int4,
  "update_table" varchar(255) COLLATE "pg_catalog"."default",
  "date_name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."model_system_alive"."system_id" IS '子系统ID';
COMMENT ON COLUMN "public"."model_system_alive"."system_name" IS '子系统名称';
COMMENT ON COLUMN "public"."model_system_alive"."time_limit" IS '时间限制，单位是 秒';
COMMENT ON COLUMN "public"."model_system_alive"."mine_id" IS '对应mine_name id';
COMMENT ON COLUMN "public"."model_system_alive"."update_table" IS '实时更新表名';
COMMENT ON COLUMN "public"."model_system_alive"."date_name" IS '日期字段名';
COMMENT ON TABLE "public"."model_system_alive" IS '子系统存活监测';

-- ----------------------------
-- Records of model_system_alive
-- ----------------------------
--INSERT INTO "public"."model_system_alive" VALUES ('141001', '辅助运输系统', 1800, 100052, 'auxiliary_transport_realtime', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('142001', '高频微震系统', 1800, 100052, 'slight_shock_realtime', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('101001', '地面压风监控系统', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('106001', '通风一号', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('109001', '大巷皮带', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('106002', '通风二号', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('111001', '提升系统', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('112001', '井下中央变电所', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('112002', '井下盘曲变电所', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('112003', '110kV变电所', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('114001', '井下主排水系统', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('125001', '智能化综采', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('125002', '智能化综掘', 1800, 100052, 'data_automation_definition', 'updated_at');
--INSERT INTO "public"."model_system_alive" VALUES ('105001', '水文监测系统', 1800, 100052, 'water_observation_realtime', 'timestamp');
--INSERT INTO "public"."model_system_alive" VALUES ('116002', '人员定位系统', 1800, 100052, 'position_realtime', 'data_time');
--INSERT INTO "public"."model_system_alive" VALUES ('132001', '矿压锚索应力', 1800, 100052, 'mine_stress_realtime', 'timestamp');
--INSERT INTO "public"."model_system_alive" VALUES ('139001', '束管监测系统', 1800, 100052, 'tube_monitor_realtime', 'timestamp');
--INSERT INTO "public"."model_system_alive" VALUES ('140001', '地音监测系统', 1800, 100052, 'ground_sound_realtime', 'timestamp');
--INSERT INTO "public"."model_system_alive" VALUES ('103001', '安全监测系统', 1800, 100052, 'safety_realtime', 'data_time');

-- ----------------------------
-- Table structure for service_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."service_system";
CREATE TABLE "public"."service_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "conf_table" varchar COLLATE "pg_catalog"."default",
  "flag" varchar(255) COLLATE "pg_catalog"."default",
  "definition_routing_key" varchar(255) COLLATE "pg_catalog"."default",
  "route_service" int2,
  "warn_service" int2,
  "converter_service" int2,
  "exchange" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."service_system"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."service_system"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."service_system"."definition_routing_key" IS '定义routkingkey';

-- ----------------------------
-- Records of service_system
-- ----------------------------
INSERT INTO "public"."service_system" VALUES ('1', '系统监控', '2021-11-10 18:39:17+08', '2021-11-11 14:03:06.356843+08', 'model_system_alive', '1', 'model_system_alive_子系统存活监测', 1, NULL, 1, 'ex_alive');
-- INSERT INTO "public"."service_system" VALUES ('2', '预警报警', '2021-11-16 20:40:38+08', '2021-11-16 20:40:40+08', 'gis_warning_alarm_info', '1', 'gis_warning_alarm_info_子预警报警', 1, NULL, 1, 'gis_warning_alarm_info');

-- ----------------------------
-- Triggers structure for table service_system
-- ----------------------------
CREATE TRIGGER "upd_timestamp_service_system" BEFORE UPDATE ON "public"."service_system"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table service_system
-- ----------------------------
ALTER TABLE "public"."service_system" ADD CONSTRAINT "service_system_pkey" PRIMARY KEY ("id");


-- ----------------------------
-- Table structure for monitor_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."monitor_statistics" CASCADE;
CREATE TABLE "public"."monitor_statistics" (
  "routing_key" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "receive_count" int4,
  "storage_count" int4,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "definition_exception" int4,
  "constraint_exception" int4,
  "repeat_exception" int4
)
;

-- ----------------------------
-- Records of monitor_statistics
-- ----------------------------
--INSERT INTO "public"."monitor_statistics" VALUES ('update.10040.100052.140001.地音监测', 100, 10, '2022-04-25 10:24:09.166969+08', '2022-04-29 09:41:19.865664+08', 0, 0, 0);
--INSERT INTO "public"."monitor_statistics" VALUES ('update.10040.100052.132001.矿压锚索应力', 160, 0, '2022-04-25 10:24:09.196094+08', '2022-04-29 09:40:49.427354+08', 160, 0, 0);
--INSERT INTO "public"."monitor_statistics" VALUES ('update.10040.100052.142001.高频微震', 1, 1, '2022-04-25 10:24:09.199121+08', '2022-04-29 09:41:17.96036+08', 0, 0, 0);
--INSERT INTO "public"."monitor_statistics" VALUES ('update.10040.100052.105001.水文监测', 112, 89, '2022-04-25 10:24:09.205627+08', '2022-04-29 09:41:20.947698+08', 23, 0, 0);
--
-- ----------------------------
-- Table structure for mos_device_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."mos_device_info" CASCADE;
CREATE TABLE "public"."mos_device_info" (
  "id" int4 NOT NULL,
  "device_type" varchar(255) COLLATE "pg_catalog"."default",
  "device_name" varchar(255) COLLATE "pg_catalog"."default",
  "model" varchar(255) COLLATE "pg_catalog"."default",
  "username" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "ip" varchar(255) COLLATE "pg_catalog"."default",
  "port" int4,
  "device_ip" varchar(255) COLLATE "pg_catalog"."default",
  "device_port" int4
)
;
COMMENT ON COLUMN "public"."mos_device_info"."device_type" IS '设备类型';
COMMENT ON COLUMN "public"."mos_device_info"."device_name" IS '设备名称';
COMMENT ON COLUMN "public"."mos_device_info"."model" IS '型号';
COMMENT ON COLUMN "public"."mos_device_info"."username" IS '用户名';
COMMENT ON COLUMN "public"."mos_device_info"."password" IS '密码';
COMMENT ON COLUMN "public"."mos_device_info"."type" IS '厂家';
COMMENT ON COLUMN "public"."mos_device_info"."ip" IS '地址';
COMMENT ON COLUMN "public"."mos_device_info"."port" IS '端口号';
COMMENT ON COLUMN "public"."mos_device_info"."device_ip" IS '设备ip';
COMMENT ON COLUMN "public"."mos_device_info"."device_port" IS '设备端口号';

-- ----------------------------
-- Table structure for mq_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."mq_statistics" CASCADE;
CREATE TABLE "public"."mq_statistics" (
  "routing_key" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "mq_receive" int4,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;

---- ----------------------------
---- Records of mq_statistics
---- ----------------------------
--INSERT INTO "public"."mq_statistics" VALUES ('update.10040.100052.132001.矿压锚索应力', 160, '2022-04-25 10:24:09.188028+08', '2022-04-29 09:40:49.425854+08');
--INSERT INTO "public"."mq_statistics" VALUES ('update.10040.100052.142001.高频微震', 1, '2022-04-25 10:24:09.179565+08', '2022-04-29 09:41:17.957702+08');
--INSERT INTO "public"."mq_statistics" VALUES ('update.10040.100052.140001.地音监测', 100, '2022-04-25 10:24:09.161988+08', '2022-04-29 09:41:19.861466+08');
--INSERT INTO "public"."mq_statistics" VALUES ('update.10040.100052.105001.水文监测', 112, '2022-04-25 10:24:09.19926+08', '2022-04-29 09:41:20.93779+08');
--
-- ----------------------------
-- Triggers structure for table model_system
-- ----------------------------
CREATE TRIGGER "upd_timestamp_model_system" BEFORE UPDATE ON "public"."model_system"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table model_system
-- ----------------------------
ALTER TABLE "public"."model_system" ADD CONSTRAINT "model_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table model_system_alive
-- ----------------------------
ALTER TABLE "public"."model_system_alive" ADD CONSTRAINT "model_system_alive_pkey" PRIMARY KEY ("system_id");

-- ----------------------------
-- Triggers structure for table monitor_statistics
-- ----------------------------
CREATE TRIGGER "upd_timestamp_monitor_statistics" BEFORE UPDATE ON "public"."monitor_statistics"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table monitor_statistics
-- ----------------------------
ALTER TABLE "public"."monitor_statistics" ADD CONSTRAINT "monitor_statistics_pkey" PRIMARY KEY ("routing_key");

-- ----------------------------
-- Primary Key structure for table mos_device_info
-- ----------------------------
ALTER TABLE "public"."mos_device_info" ADD CONSTRAINT "mos_device_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table mq_statistics
-- ----------------------------
CREATE TRIGGER "upd_timestamp_mq_statistics" BEFORE UPDATE ON "public"."mq_statistics"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table mq_statistics
-- ----------------------------
ALTER TABLE "public"."mq_statistics" ADD CONSTRAINT "mq_statistics_pkey" PRIMARY KEY ("routing_key");

-- ----------------------------
-- Table structure for online_driver_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."online_driver_definition" CASCADE;
CREATE TABLE "public"."online_driver_definition" (
  "edge_code" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "driver_name" varchar COLLATE "pg_catalog"."default",
  "driver_code" varchar COLLATE "pg_catalog"."default",
  "created_at " timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "id_del" varchar(1) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "data_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."online_driver_definition"."edge_code" IS '设备ID';
COMMENT ON COLUMN "public"."online_driver_definition"."driver_name" IS '司机名字';
COMMENT ON COLUMN "public"."online_driver_definition"."driver_code" IS '司机编码';
COMMENT ON COLUMN "public"."online_driver_definition"."created_at " IS '创建时间';
COMMENT ON COLUMN "public"."online_driver_definition"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."online_driver_definition"."id_del" IS '是否删除';
COMMENT ON COLUMN "public"."online_driver_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."online_driver_definition"."data_time" IS '时间';

-- ----------------------------
-- Records of online_driver_definition
-- ----------------------------

-- ----------------------------
-- Table structure for position_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_employee" CASCADE;
CREATE TABLE "public"."position_employee" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "person_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "card_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(50) COLLATE "pg_catalog"."default",
  "job" varchar(50) COLLATE "pg_catalog"."default",
  "work_kind" varchar(50) COLLATE "pg_catalog"."default",
  "birthday" varchar(50) COLLATE "pg_catalog"."default",
  "education" varchar(50) COLLATE "pg_catalog"."default",
  "leader" int4 NOT NULL DEFAULT 0,
  "special_person" int4 NOT NULL DEFAULT 0,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."position_employee"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_employee"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."position_employee"."group_code" IS '集团编码,集团编码';
COMMENT ON COLUMN "public"."position_employee"."person_name" IS '人员姓名,人员姓名';
COMMENT ON COLUMN "public"."position_employee"."card_id" IS '识别卡编号,识别卡编号';
COMMENT ON COLUMN "public"."position_employee"."department" IS '部门,不超过 15 个汉字长度，对于矿长、副矿长、总工等设为“领导干部”，其他按实际填写';
COMMENT ON COLUMN "public"."position_employee"."job" IS '职务,职务';
COMMENT ON COLUMN "public"."position_employee"."work_kind" IS '工种,工种';
COMMENT ON COLUMN "public"."position_employee"."birthday" IS '出生日期,出生日期';
COMMENT ON COLUMN "public"."position_employee"."education" IS '教育程度,教育程度';
COMMENT ON COLUMN "public"."position_employee"."leader" IS '是否矿领导,1为是，0为否';
COMMENT ON COLUMN "public"."position_employee"."special_person" IS '是否特种人员,1为是，0为否';
COMMENT ON COLUMN "public"."position_employee"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_employee"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_employee"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_employee"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."position_employee" IS '人员基本信息表';

-- ----------------------------
-- Table structure for position_help_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_help_realtime" CASCADE;
CREATE TABLE "public"."position_help_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "card_id" varchar(17) COLLATE "pg_catalog"."default" NOT NULL,
  "person_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(10) COLLATE "pg_catalog"."default",
  "job" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "work_kind" varchar(50) COLLATE "pg_catalog"."default",
  "leader" int4 NOT NULL,
  "special_person" int4 NOT NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "alarm_during" int4,
  "down_time" timestamp(6) NOT NULL,
  "area_code" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "station_code" varchar(22) COLLATE "pg_catalog"."default" NOT NULL,
  "station_name" varchar(100) COLLATE "pg_catalog"."default",
  "enter_area_time" timestamp(6) NOT NULL,
  "enter_station_time" timestamp(6) NOT NULL,
  "class_set_code" varchar(2) COLLATE "pg_catalog"."default",
  "class_set_name" varchar(20) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int4 NOT NULL,
  "notice" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."position_help_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_help_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_help_realtime"."card_id" IS '人员卡编码';
COMMENT ON COLUMN "public"."position_help_realtime"."person_name" IS '姓名';
COMMENT ON COLUMN "public"."position_help_realtime"."department" IS '部门,不超过 15 个汉字长度，对于矿长、副矿长、总工等设为“领导干部”，其他按实际填写';
COMMENT ON COLUMN "public"."position_help_realtime"."job" IS '职务,职务';
COMMENT ON COLUMN "public"."position_help_realtime"."work_kind" IS '工种,工种';
COMMENT ON COLUMN "public"."position_help_realtime"."leader" IS '是否矿领导,1为是，0为否';
COMMENT ON COLUMN "public"."position_help_realtime"."special_person" IS '是否特种人员,1为是，0为否';
COMMENT ON COLUMN "public"."position_help_realtime"."begin_time" IS '求救开始时间';
COMMENT ON COLUMN "public"."position_help_realtime"."end_time" IS '求救结束时间';
COMMENT ON COLUMN "public"."position_help_realtime"."alarm_during" IS '报警时长';
COMMENT ON COLUMN "public"."position_help_realtime"."down_time" IS '入井时间';
COMMENT ON COLUMN "public"."position_help_realtime"."area_code" IS '当前所在区域编码';
COMMENT ON COLUMN "public"."position_help_realtime"."station_code" IS '当前所在基站编码';
COMMENT ON COLUMN "public"."position_help_realtime"."enter_area_time" IS '进入当前区域时刻';
COMMENT ON COLUMN "public"."position_help_realtime"."enter_station_time" IS '进入当前所处基站时刻';
COMMENT ON COLUMN "public"."position_help_realtime"."class_set_code" IS '班次编号';
COMMENT ON COLUMN "public"."position_help_realtime"."class_set_name" IS '班次名称';
COMMENT ON COLUMN "public"."position_help_realtime"."data_time" IS '插入时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_help_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_help_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_help_realtime"."collect_status" IS '状态 正常：100 ，90 断点续传 ，80:超时结束 ，70：比较结束';
COMMENT ON COLUMN "public"."position_help_realtime"."notice" IS '发送弹出报警标志 1：已发送 0：未发送 默认0';
COMMENT ON TABLE "public"."position_help_realtime" IS '人员救助实时表';

-- ----------------------------
-- Records of position_help_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for position_overman_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_overman_realtime" CASCADE;
CREATE TABLE "public"."position_overman_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "type" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
  "personnel_quota" int4 NOT NULL,
  "current_person_total" int4 NOT NULL,
  "area_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "alarm_during" int4,
  "class_set_code" varchar(2) COLLATE "pg_catalog"."default",
  "class_set_name" varchar(20) COLLATE "pg_catalog"."default",
  "area_person" text COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int4 NOT NULL,
  "notice" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."position_overman_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_overman_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_overman_realtime"."type" IS '超员类型,矿井超员、区域超员';
COMMENT ON COLUMN "public"."position_overman_realtime"."personnel_quota" IS '定员数,根据超员类别相应填写矿井、区域的定员数。必填项';
COMMENT ON COLUMN "public"."position_overman_realtime"."current_person_total" IS '当前总人数,根据类别填写当前总人数，如果类别是“矿井超员”，则填写全矿井当前总人数;如“重点区域超员”，则填写该重点区域总人数。必填项';
COMMENT ON COLUMN "public"."position_overman_realtime"."area_code" IS '区域编码';
COMMENT ON COLUMN "public"."position_overman_realtime"."area_name" IS '区域名称';
COMMENT ON COLUMN "public"."position_overman_realtime"."begin_time" IS '报警开始时间';
COMMENT ON COLUMN "public"."position_overman_realtime"."end_time" IS '报警结束时间';
COMMENT ON COLUMN "public"."position_overman_realtime"."alarm_during" IS '报警时长';
COMMENT ON COLUMN "public"."position_overman_realtime"."class_set_code" IS '班次编号';
COMMENT ON COLUMN "public"."position_overman_realtime"."class_set_name" IS '班次';
COMMENT ON COLUMN "public"."position_overman_realtime"."area_person" IS '当报警类型为进入限制区域时，记录当前限制区域中人员卡编码集合，之间用 “&”连接，其他报警类型为空。';
COMMENT ON COLUMN "public"."position_overman_realtime"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_overman_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_overman_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_overman_realtime"."collect_status" IS '状态 正常：100 ，90 断点续传 ，80:超时结束 ，70：比较结束';
COMMENT ON COLUMN "public"."position_overman_realtime"."notice" IS '发送弹出报警标志 1：已发送 0：未发送 默认0';
COMMENT ON TABLE "public"."position_overman_realtime" IS '人员超员表';

-- ----------------------------
-- Records of position_overman_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for position_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_realtime" CASCADE;
CREATE TABLE "public"."position_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "card_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "person_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "ry_well_code" int4 NOT NULL,
  "down_time" timestamp(6) NOT NULL,
  "up_time" timestamp(6),
  "area_code" varchar(255) COLLATE "pg_catalog"."default",
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "in_area_time" timestamp(6),
  "station_code" varchar(255) COLLATE "pg_catalog"."default",
  "station_name" varchar(100) COLLATE "pg_catalog"."default",
  "in_station_time" timestamp(6),
  "class_set_type" varchar(10) COLLATE "pg_catalog"."default",
  "station_distance" float8,
  "work_status" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(50) COLLATE "pg_catalog"."default",
  "job" varchar(50) COLLATE "pg_catalog"."default",
  "work_kind" varchar(50) COLLATE "pg_catalog"."default",
  "leader" int4 NOT NULL,
  "special_person" int4 NOT NULL,
  "track" text COLLATE "pg_catalog"."default",
  "class_set_code" varchar(2) COLLATE "pg_catalog"."default",
  "class_set_name" varchar(20) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT now(),
  "collect_status" int4 NOT NULL,
  "geometry_point" "public"."geometry",
  "x" varchar(255) COLLATE "pg_catalog"."default",
  "y" varchar(255) COLLATE "pg_catalog"."default",
  "z" varchar(255) COLLATE "pg_catalog"."default",
  "tunnel_code" varchar(255) NULL,
  "tunnel_name" varchar(255) NULL,
  "tunnel_distance" float8 NULL
)
;
COMMENT ON COLUMN "public"."position_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_realtime"."card_id" IS '人员卡编码,参见附录a.3人员卡编码，唯一，必填项。';
COMMENT ON COLUMN "public"."position_realtime"."person_name" IS '姓名,人员姓名，和人员信息对应，必填项。';
COMMENT ON COLUMN "public"."position_realtime"."ry_well_code" IS '出入井标志位,整数，携卡人在井口区域，标志位设为“0”;携卡人已入井，标志位设为“1”;携卡人出井，标志位设为“2” ，必填项。字典项，参见附录b.8人员出入井标志。';
COMMENT ON COLUMN "public"."position_realtime"."down_time" IS '入井时刻,日期时间格式yyyy-MM-dd HH:mm:sss。';
COMMENT ON COLUMN "public"."position_realtime"."up_time" IS '出井时刻,日期时间型，出入井标志位为“0”和“1”时，为空，为2时，填写该人员的出井时刻。日期时间格式yyyy-MM-dd HH:mm:sss。';
COMMENT ON COLUMN "public"."position_realtime"."area_code" IS '区域编码,关联井下区域';
COMMENT ON COLUMN "public"."position_realtime"."in_area_time" IS '进入当前区域时刻';
COMMENT ON COLUMN "public"."position_realtime"."station_code" IS '基站编码';
COMMENT ON COLUMN "public"."position_realtime"."in_station_time" IS '进入当前所处基站时刻';
COMMENT ON COLUMN "public"."position_realtime"."class_set_type" IS '劳动组织方式,人员班组模式，例如三八、四六制等';
COMMENT ON COLUMN "public"."position_realtime"."station_distance" IS '距离基站距离';
COMMENT ON COLUMN "public"."position_realtime"."work_status" IS '人员工作状态,正常|求救';
COMMENT ON COLUMN "public"."position_realtime"."department" IS '部门,不超过 15 个汉字长度，对于矿长、副矿长、总工等设为“领导干部”，其他按实际填写';
COMMENT ON COLUMN "public"."position_realtime"."job" IS '职务,职务';
COMMENT ON COLUMN "public"."position_realtime"."work_kind" IS '工种,工种';
COMMENT ON COLUMN "public"."position_realtime"."leader" IS '是否矿领导,1为是，0为否';
COMMENT ON COLUMN "public"."position_realtime"."special_person" IS '是否特种人员,1为是，0为否';
COMMENT ON COLUMN "public"."position_realtime"."track" IS '行进轨迹基站 时间集合';
COMMENT ON COLUMN "public"."position_realtime"."class_set_code" IS '班次编号';
COMMENT ON COLUMN "public"."position_realtime"."class_set_name" IS '班次名称';
COMMENT ON COLUMN "public"."position_realtime"."data_time" IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_realtime"."collect_status" IS '数据状态100：正常 90：自动结束';
COMMENT ON COLUMN "public"."position_realtime"."x" IS 'X坐标';
COMMENT ON COLUMN "public"."position_realtime"."y" IS 'Y坐标';
COMMENT ON COLUMN "public"."position_realtime"."z" IS 'Z坐标';
COMMENT ON COLUMN "public"."position_realtime"."tunnel_code" IS '巷道编号';
COMMENT ON COLUMN "public"."position_realtime"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."position_realtime"."tunnel_distance" IS '巷道距离';
COMMENT ON TABLE "public"."position_realtime" IS '人员定位实时表';

-- ----------------------------
-- Table structure for position_restricted_area_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_restricted_area_realtime" CASCADE;
CREATE TABLE "public"."position_restricted_area_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "card_id" varchar(17) COLLATE "pg_catalog"."default" NOT NULL,
  "person_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(10) COLLATE "pg_catalog"."default",
  "job" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "work_kind" varchar(50) COLLATE "pg_catalog"."default",
  "leader" int4 NOT NULL,
  "special_person" int4 NOT NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "alarm_during" int4,
  "down_time" timestamp(6),
  "area_code" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "station_code" varchar(22) COLLATE "pg_catalog"."default" NOT NULL,
  "station_name" varchar(100) COLLATE "pg_catalog"."default",
  "enter_area_time" timestamp(6),
  "enter_station_time" timestamp(6),
  "class_set_code" varchar(2) COLLATE "pg_catalog"."default",
  "class_set_name" varchar(20) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6),
  "insert_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int4 NOT NULL,
  "notice" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."card_id" IS '人员卡编码';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."person_name" IS '姓名';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."department" IS '部门,不超过 15 个汉字长度，对于矿长、副矿长、总工等设为“领导干部”，其他按实际填写';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."job" IS '职务,职务';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."work_kind" IS '工种,工种';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."leader" IS '是否矿领导,1为是，0为否';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."special_person" IS '是否特种人员,1为是，0为否';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."begin_time" IS '报警开始时间';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."end_time" IS '报警结束时间';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."alarm_during" IS '报警时长';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."down_time" IS '入井时间';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."area_code" IS '当前所在区域编码';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."station_code" IS '当前所在基站编码';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."enter_area_time" IS '进入当前区域时刻';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."enter_station_time" IS '进入当前所处基站时刻';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."class_set_code" IS '班次编号';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."class_set_name" IS '班次名称';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."collect_status" IS '状态 正常：100 ，90 断点续传 ，80:超时结束 ，70：比较结束';
COMMENT ON COLUMN "public"."position_restricted_area_realtime"."notice" IS '发送弹出报警标志 1：已发送 0：未发送 默认0';
COMMENT ON TABLE "public"."position_restricted_area_realtime" IS '人员进入限制区表';

-- ----------------------------
-- Records of position_restricted_area_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for position_scheduling_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_scheduling_definition" CASCADE;
CREATE TABLE "public"."position_scheduling_definition" (
  "shift_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "shift_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "work_shift" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "closing_time" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "shift_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."position_scheduling_definition"."shift_code" IS '班次编号';
COMMENT ON COLUMN "public"."position_scheduling_definition"."shift_name" IS '班次名称';
COMMENT ON COLUMN "public"."position_scheduling_definition"."work_shift" IS '上班时间 必填项，时间格式字符串，格式为HH:mm:ss';
COMMENT ON COLUMN "public"."position_scheduling_definition"."closing_time" IS '下班时间 必填项，时间格式字符串，格式为HH:mm:ss';
COMMENT ON COLUMN "public"."position_scheduling_definition"."shift_type" IS '班次类型 必填项，文字描述“二班制”、“三八制”、“四六制”三选一。';
COMMENT ON COLUMN "public"."position_scheduling_definition"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_scheduling_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_scheduling_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON TABLE "public"."position_scheduling_definition" IS '人员定位系统-排班信息实时表';

-- ----------------------------
-- Records of position_scheduling_definition
-- ----------------------------

-- ----------------------------
-- Table structure for position_station_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_station_definition" CASCADE;
CREATE TABLE "public"."position_station_definition" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "station_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "station_name" varchar(100) COLLATE "pg_catalog"."default",
  "location" varchar(100) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int2,
  "geometry_point" "public"."geometry",
  "deleted_at" timestamp(6) NULL
)
;
COMMENT ON COLUMN "public"."position_station_definition"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_station_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_station_definition"."station_code" IS '基站编码,关联分站表';
COMMENT ON COLUMN "public"."position_station_definition"."location" IS '位置注释';
COMMENT ON COLUMN "public"."position_station_definition"."data_time" IS '数据时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_station_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_station_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_station_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."position_station_definition" IS '基站表 每天上传一次，变化时全量';

-- ----------------------------
-- Table structure for position_timeout_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_timeout_realtime" CASCADE;
CREATE TABLE "public"."position_timeout_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(100) COLLATE "pg_catalog"."default",
  "card_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "person_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "department" varchar(15) COLLATE "pg_catalog"."default",
  "job" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "work_kind" varchar(10) COLLATE "pg_catalog"."default",
  "leader" int4 NOT NULL,
  "special_person" int4 NOT NULL,
  "area_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "down_time" timestamp(6) NOT NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "alarm_during" int4,
  "enter_area_time" timestamp(6) NOT NULL,
  "enter_station_time" timestamp(6) NOT NULL,
  "class_set_code" varchar(2) COLLATE "pg_catalog"."default",
  "class_set_name" varchar(20) COLLATE "pg_catalog"."default",
  "station_code" varchar(22) COLLATE "pg_catalog"."default" NOT NULL,
  "station_name" varchar(100) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int4 NOT NULL,
  "notice" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."position_timeout_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_timeout_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_timeout_realtime"."card_id" IS '识别卡id 17位';
COMMENT ON COLUMN "public"."position_timeout_realtime"."person_name" IS '人员姓名';
COMMENT ON COLUMN "public"."position_timeout_realtime"."department" IS '部门,不超过 15 个汉字长度，对于矿长、副矿长、总工等设为“领导干部”，其他按实际填写';
COMMENT ON COLUMN "public"."position_timeout_realtime"."job" IS '职务,职务';
COMMENT ON COLUMN "public"."position_timeout_realtime"."work_kind" IS '工种,工种';
COMMENT ON COLUMN "public"."position_timeout_realtime"."leader" IS '是否矿领导,1为是，0为否';
COMMENT ON COLUMN "public"."position_timeout_realtime"."special_person" IS '是否特种人员,1为是，0为否';
COMMENT ON COLUMN "public"."position_timeout_realtime"."area_code" IS '区域编码';
COMMENT ON COLUMN "public"."position_timeout_realtime"."down_time" IS '下井时间';
COMMENT ON COLUMN "public"."position_timeout_realtime"."begin_time" IS '报警开始时间';
COMMENT ON COLUMN "public"."position_timeout_realtime"."end_time" IS '报警结束时间';
COMMENT ON COLUMN "public"."position_timeout_realtime"."alarm_during" IS '报警时长';
COMMENT ON COLUMN "public"."position_timeout_realtime"."enter_area_time" IS '进入区域时刻';
COMMENT ON COLUMN "public"."position_timeout_realtime"."enter_station_time" IS '进入基站时刻';
COMMENT ON COLUMN "public"."position_timeout_realtime"."class_set_code" IS '班次编码';
COMMENT ON COLUMN "public"."position_timeout_realtime"."class_set_name" IS '班次名称';
COMMENT ON COLUMN "public"."position_timeout_realtime"."station_code" IS '基站编码';
COMMENT ON COLUMN "public"."position_timeout_realtime"."data_time" IS '数据时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_timeout_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_timeout_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_timeout_realtime"."collect_status" IS '状态 正常：100 ，90 断点续传 ，80:超时结束 ，70：比较结束';
COMMENT ON COLUMN "public"."position_timeout_realtime"."notice" IS '发送弹出报警标志 1：已发送 0：未发送 默认0';
COMMENT ON TABLE "public"."position_timeout_realtime" IS '人员超时表';

-- ----------------------------
-- Records of position_timeout_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for position_well_area
-- ----------------------------
DROP TABLE IF EXISTS "public"."position_well_area" CASCADE;
CREATE TABLE "public"."position_well_area" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "area_type" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
  "limit_person" int4 NOT NULL,
  "area_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "station_count" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."position_well_area"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."position_well_area"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."position_well_area"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."position_well_area"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."position_well_area"."area_type" IS '区域类型 4 个汉字长,井口区域、重点区域、限制区域、其它区域';
COMMENT ON COLUMN "public"."position_well_area"."limit_person" IS '区域核定人数,';
COMMENT ON COLUMN "public"."position_well_area"."area_code" IS '区域编码,';
COMMENT ON COLUMN "public"."position_well_area"."area_name" IS '区域名称,';
COMMENT ON COLUMN "public"."position_well_area"."data_time" IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."position_well_area"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."position_well_area"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."position_well_area"."station_count" IS '分站个数';
COMMENT ON TABLE "public"."position_well_area" IS '井下区域';

-- ----------------------------
-- Triggers structure for table online_driver_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_online_driver_definition" BEFORE UPDATE ON "public"."online_driver_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table online_driver_definition
-- ----------------------------
ALTER TABLE "public"."online_driver_definition" ADD CONSTRAINT "online_driver_definition_id" PRIMARY KEY ("edge_code");

-- ----------------------------
-- Primary Key structure for table position_employee
-- ----------------------------
ALTER TABLE "public"."position_employee" ADD CONSTRAINT "position_employee_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Primary Key structure for table position_help_realtime
-- ----------------------------
ALTER TABLE "public"."position_help_realtime" ADD CONSTRAINT "position_help_realtime_pkey" PRIMARY KEY ("card_id", "begin_time");

-- ----------------------------
-- Primary Key structure for table position_overman_realtime
-- ----------------------------
ALTER TABLE "public"."position_overman_realtime" ADD CONSTRAINT "position_overman_realtime_pkey" PRIMARY KEY ("area_code", "begin_time");

-- ----------------------------
-- Primary Key structure for table position_realtime
-- ----------------------------
ALTER TABLE "public"."position_realtime" ADD CONSTRAINT "position_realtime_pkey" PRIMARY KEY ("card_id", "down_time");

-- ----------------------------
-- Primary Key structure for table position_restricted_area_realtime
-- ----------------------------
ALTER TABLE "public"."position_restricted_area_realtime" ADD CONSTRAINT "position_restricted_area_realtime_pkey" PRIMARY KEY ("card_id", "begin_time");

-- ----------------------------
-- Primary Key structure for table position_scheduling_definition
-- ----------------------------
ALTER TABLE "public"."position_scheduling_definition" ADD CONSTRAINT "position_scheduling_definition_pkey" PRIMARY KEY ("shift_code");

-- ----------------------------
-- Primary Key structure for table position_station_definition
-- ----------------------------
ALTER TABLE "public"."position_station_definition" ADD CONSTRAINT "position_station_definition_pkey" PRIMARY KEY ("station_code");

-- ----------------------------
-- Primary Key structure for table position_timeout_realtime
-- ----------------------------
ALTER TABLE "public"."position_timeout_realtime" ADD CONSTRAINT "position_timeout_realtime_pkey" PRIMARY KEY ("card_id", "begin_time");

-- ----------------------------
-- Primary Key structure for table position_well_area
-- ----------------------------
ALTER TABLE "public"."position_well_area" ADD CONSTRAINT "position_well_area_pkey" PRIMARY KEY ("area_code");

-- ----------------------------
-- Table structure for roof_stress_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."roof_stress_definition" CASCADE;
CREATE TABLE "public"."roof_stress_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_status" int4,
  "sensor_type" varchar COLLATE "pg_catalog"."default",
  "point_monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "description" varchar COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "unit" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "point_count" int4,
  "point_position" varchar(255) COLLATE "pg_catalog"."default",
  "station_type" varchar(255) COLLATE "pg_catalog"."default",
  "station_number" varchar COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "deep_depth" varchar(255) COLLATE "pg_catalog"."default",
  "shallow_depth" varchar(255) COLLATE "pg_catalog"."default",
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."roof_stress_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."roof_stress_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."roof_stress_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."roof_stress_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."roof_stress_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."roof_stress_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."roof_stress_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."roof_stress_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."roof_stress_definition"."sensor_type" IS '传感器名称';
COMMENT ON COLUMN "public"."roof_stress_definition"."point_monitor_type" IS '通道监测类型';
COMMENT ON COLUMN "public"."roof_stress_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."roof_stress_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."roof_stress_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."roof_stress_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."roof_stress_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."roof_stress_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."roof_stress_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."roof_stress_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."roof_stress_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."roof_stress_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."roof_stress_definition"."point_count" IS '基点数量';
COMMENT ON COLUMN "public"."roof_stress_definition"."point_position" IS '基点位置';
COMMENT ON COLUMN "public"."roof_stress_definition"."station_type" IS '测站类型';
COMMENT ON COLUMN "public"."roof_stress_definition"."station_number" IS '测站编号';
COMMENT ON COLUMN "public"."roof_stress_definition"."channel_no" IS '数据使用通道';
COMMENT ON COLUMN "public"."roof_stress_definition"."deep_depth" IS '深基点初始安装深度';
COMMENT ON COLUMN "public"."roof_stress_definition"."shallow_depth" IS '浅基点初始安装深度';
COMMENT ON COLUMN "public"."roof_stress_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."roof_stress_definition" IS '矿压顶板离层 ';

-- ----------------------------
-- Records of roof_stress_definition
-- ----------------------------

-- ----------------------------
-- Table structure for safety_alarm_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_alarm_realtime" CASCADE;
CREATE TABLE "public"."safety_alarm_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "point_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_code" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_name" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_code" int2 NOT NULL,
  "unit_code" varchar(20) COLLATE "pg_catalog"."default",
  "unit_name" varchar(20) COLLATE "pg_catalog"."default",
  "alarm_type_code" varchar(5) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_type_name" varchar(20) COLLATE "pg_catalog"."default" NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "alarm_during" int8 NOT NULL,
  "max_value" float8,
  "max_time" timestamp(6),
  "min_value" float8,
  "min_time" timestamp(6),
  "avg_value" float8,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "reason" varchar(50) COLLATE "pg_catalog"."default",
  "measures" varchar(50) COLLATE "pg_catalog"."default",
  "record_time" timestamp(6),
  "record_person" varchar(50) COLLATE "pg_catalog"."default",
  "handle_status" int4 NOT NULL DEFAULT 0,
  "notice" int4 NOT NULL DEFAULT 0,
  "collect_status" int4 NOT NULL DEFAULT 0,
  "raw_data" varchar(255),
  "timestamp" timestamp(6)
)
;
COMMENT ON COLUMN "public"."safety_alarm_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."point_id" IS '测点编号,编码规则';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."sensor_type_code" IS '传感器类型名称,传感器描述';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."sensor_type_name" IS '传感器类型名称,传感器类型名称';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."location" IS '测点安装位置,传感器实际安装位置';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."point_value_type_code" IS '测点数值类型: 0:模拟量,1:开关量,2:多态量,3:累计量';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."unit_code" IS '测点数值单位code,若为模拟量则为必填项，其他类型为空。字典值';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."unit_name" IS '测点数值单位名称,若为模拟量则为必填项';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."alarm_type_code" IS '异常类型code,字典值';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."alarm_type_name" IS '异常类型名称,字典值';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."begin_time" IS '异常开始时间,日期时间格式yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."end_time" IS '异常结束时间,日期时间格式yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."alarm_during" IS '异常时长,单位s';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."max_value" IS '异常期间最大值,模拟量时该字段为必填项，其他类型为空。保留2位小数。';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."max_time" IS '最大值时刻,"测点值类型为模拟量时该字段为必填项，其他类型为空';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."min_value" IS '报警期间最小值,测点值类型为模拟量时该字段为必填项，其他类型为空。保留两位小数。';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."min_time" IS '最小值时刻,"测点值类型为模拟量时该字段为必填项，其他类型为空';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."avg_value" IS '异常期间平均值,测点值类型为模拟量时该字段为必填项，其他类型为空。保留两位小数。';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."data_time" IS '数据时间,定义时间';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."reason" IS '异常原因,手动录入';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."measures" IS '处理措施,手动录入';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."record_time" IS '录入时间, 录入时间';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."record_person" IS '录入人,手动录入';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."handle_status" IS '已处理标志,已处理：1;默认0';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."notice" IS '报警超时发送标志 默认 未发送 0 ，已发送：1 ';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."collect_status" IS '状态 正常：100 ，90 断点续传 ，80:超时结束 ，70：比较结束';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."raw_data" IS '原始数据';
COMMENT ON COLUMN "public"."safety_alarm_realtime"."timestamp" IS '同 data_time';
COMMENT ON TABLE "public"."safety_alarm_realtime" IS '安监报警实时表 更新频率： 20s';

-- ----------------------------
-- Table structure for safety_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_definition" CASCADE;
CREATE TABLE "public"."safety_definition" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "system_code" varchar(6) COLLATE "pg_catalog"."default",
  "system_name" varchar(20) COLLATE "pg_catalog"."default",
  "substation_code" varchar(100) COLLATE "pg_catalog"."default",
  "substation_name" varchar(100) COLLATE "pg_catalog"."default",
  "point_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_code" varchar(6) COLLATE "pg_catalog"."default",
  "sensor_type_name" varchar(24) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_code" int2 NOT NULL,
  "unit_code" varchar(20) COLLATE "pg_catalog"."default",
  "unit_name" varchar(20) COLLATE "pg_catalog"."default",
  "upper_range_value" float8,
  "lower_range_value" float8,
  "upper_alarm_value" float8,
  "upper_alarm_reset_value" float8,
  "lower_alarm_value" float8,
  "lower_alarm_reset_value" float8,
  "upper_cut_value" float8,
  "upper_reset_value" float8,
  "lower_cut_value" float8,
  "lower_reset_value" float8,
  "translate" varchar(100) COLLATE "pg_catalog"."default",
  "data_type" varchar(100) COLLATE "pg_catalog"."default",
  "location" varchar(100) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "relation" text COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "collect_status" int2,
  "system_id" varchar COLLATE "pg_catalog"."default",
  "deleted_at" timestamp(6) NULL
)
;
COMMENT ON COLUMN "public"."safety_definition"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_definition"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_definition"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_definition"."system_code" IS '系统编码,关联系统表';
COMMENT ON COLUMN "public"."safety_definition"."system_name" IS '系统名称,关联系统表';
COMMENT ON COLUMN "public"."safety_definition"."substation_code" IS '分站编码,关联分站表';
COMMENT ON COLUMN "public"."safety_definition"."substation_name" IS '分站名称,关联分站表';
COMMENT ON COLUMN "public"."safety_definition"."point_id" IS '测点编号,测点编码';
COMMENT ON COLUMN "public"."safety_definition"."sensor_type_code" IS '传感器类型编码,传感器编码';
COMMENT ON COLUMN "public"."safety_definition"."sensor_type_name" IS '传感器类型名称,传感器类型名称';
COMMENT ON COLUMN "public"."safety_definition"."point_value_type_code" IS '测点数值类型';
COMMENT ON COLUMN "public"."safety_definition"."unit_code" IS '测点数值单位编码,如果测点数值类型是mn（模拟量）则为必填项，否则为空。字典值';
COMMENT ON COLUMN "public"."safety_definition"."unit_name" IS '测点数值单位名称,字典';
COMMENT ON COLUMN "public"."safety_definition"."upper_range_value" IS '高量程,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."lower_range_value" IS '低量程,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."upper_alarm_value" IS '上限报警门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."upper_alarm_reset_value" IS '上限解报门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."lower_alarm_value" IS '下限报警门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."lower_alarm_reset_value" IS '下限解报门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."upper_cut_value" IS '上限断电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空';
COMMENT ON COLUMN "public"."safety_definition"."upper_reset_value" IS '上限复电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."lower_cut_value" IS '下限断电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."lower_reset_value" IS '下限复电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition"."translate" IS '当为开关量时,对1和0含义的描述';
COMMENT ON COLUMN "public"."safety_definition"."data_type" IS '数据类型';
COMMENT ON COLUMN "public"."safety_definition"."location" IS '测点安装位置,传感器实际安装位置';
COMMENT ON COLUMN "public"."safety_definition"."data_time" IS '数据采集时间格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."safety_definition"."relation" IS '测点关联关系';
COMMENT ON COLUMN "public"."safety_definition"."x" IS '坐标';
COMMENT ON COLUMN "public"."safety_definition"."y" IS '坐标';
COMMENT ON COLUMN "public"."safety_definition"."z" IS '坐标';
COMMENT ON COLUMN "public"."safety_definition"."system_id" IS '系统ID';
COMMENT ON COLUMN "public"."safety_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."safety_definition" IS '安监定义表，每天至少上传一次，数据变化时全量数据上传';

-- ----------------------------
-- Table structure for safety_definition_change
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_definition_change" CASCADE;
CREATE TABLE "public"."safety_definition_change" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "system_code" varchar(6) COLLATE "pg_catalog"."default" NOT NULL,
  "system_name" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "substation_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "substation_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "point_id" varchar(28) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_code" varchar(6) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_name" varchar(24) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_code" varchar(2) COLLATE "pg_catalog"."default" NOT NULL,
  "unit_code" varchar(20) COLLATE "pg_catalog"."default",
  "unit_name" varchar(20) COLLATE "pg_catalog"."default",
  "location" varchar(100) COLLATE "pg_catalog"."default",
  "old_sensor_type_name" varchar(10) COLLATE "pg_catalog"."default",
  "upper_range_value" float8,
  "lower_range_value" float8,
  "upper_alarm_value" float8,
  "upper_alarm_reset_value" float8,
  "lower_alarm_value" float8,
  "lower_alarm_reset_value" float8,
  "upper_cut_value" float8,
  "upper_reset_value" float8,
  "lower_cut_value" float8,
  "lower_reset_value" float8,
  "change_type" int4 NOT NULL,
  "change_note" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "end_time" timestamp(6),
  "begin_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "translate" varchar(100) COLLATE "pg_catalog"."default",
  "data_type" varchar(100) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6),
  "x" float8,
  "y" float8,
  "z" float8,
  "collect_status" int2,
  "relation" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."safety_definition_change"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_definition_change"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_definition_change"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_definition_change"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_definition_change"."system_code" IS '系统编码,关联系统表';
COMMENT ON COLUMN "public"."safety_definition_change"."system_name" IS '系统名称,关联系统表';
COMMENT ON COLUMN "public"."safety_definition_change"."substation_code" IS '分站编码,关联分站表';
COMMENT ON COLUMN "public"."safety_definition_change"."substation_name" IS '分站名称,关联分站表';
COMMENT ON COLUMN "public"."safety_definition_change"."point_id" IS '测点编号,28位';
COMMENT ON COLUMN "public"."safety_definition_change"."sensor_type_code" IS '传感器类型编码,传感器编码';
COMMENT ON COLUMN "public"."safety_definition_change"."sensor_type_name" IS '传感器类型名称,传感器类型名称';
COMMENT ON COLUMN "public"."safety_definition_change"."point_value_type_code" IS '测点数值类型," mn:模拟量';
COMMENT ON COLUMN "public"."safety_definition_change"."unit_code" IS '测点数值单位编码,如果测点数值类型是mn（模拟量）则为必填项，否则为空。字典值';
COMMENT ON COLUMN "public"."safety_definition_change"."unit_name" IS '测点数值单位名称,字典';
COMMENT ON COLUMN "public"."safety_definition_change"."location" IS '测点安装位置,传感器实际安装位置';
COMMENT ON COLUMN "public"."safety_definition_change"."old_sensor_type_name" IS '传感器类型名称,传感器类型名称';
COMMENT ON COLUMN "public"."safety_definition_change"."upper_range_value" IS '高量程,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."lower_range_value" IS '低量程,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."upper_alarm_value" IS '上限报警门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."upper_alarm_reset_value" IS '上限解报门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."lower_alarm_value" IS '下限报警门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."lower_alarm_reset_value" IS '下限解报门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."upper_cut_value" IS '上限断电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空';
COMMENT ON COLUMN "public"."safety_definition_change"."upper_reset_value" IS '上限复电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."lower_cut_value" IS '下限断电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."lower_reset_value" IS '下限复电门限,当测点数值类型字段为mn（模拟量）时，该字段为必填项；为其他值时字段为空。';
COMMENT ON COLUMN "public"."safety_definition_change"."change_type" IS '操作类型,新增：1修改：2删除：3';
COMMENT ON COLUMN "public"."safety_definition_change"."change_note" IS '操作描述,填写原信息 和本次记录的新信息，详细';
COMMENT ON COLUMN "public"."safety_definition_change"."end_time" IS '定义结束时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_definition_change"."begin_time" IS '定义开始时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_definition_change"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_definition_change"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON TABLE "public"."safety_definition_change" IS '安监定义变更表 有变更时新增';

-- ----------------------------
-- Table structure for safety_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_realtime" CASCADE;
CREATE TABLE "public"."safety_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "point_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_code" varchar(6) COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type_name" varchar(24) COLLATE "pg_catalog"."default" NOT NULL,
  "point_value_type_code" int2 NOT NULL,
  "unit_code" varchar(20) COLLATE "pg_catalog"."default",
  "unit_name" varchar(20) COLLATE "pg_catalog"."default",
  "location" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "alarm_status_code" varchar(20) COLLATE "pg_catalog"."default",
  "alarm_status_name" varchar(20) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) DEFAULT now(),
  "collect_status" int2,
  "timestamp" timestamp(6)
)
;
COMMENT ON COLUMN "public"."safety_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_realtime"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_realtime"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_realtime"."point_id" IS '测点编号,28位';
COMMENT ON COLUMN "public"."safety_realtime"."sensor_type_code" IS '传感器类型编码,传感器编码';
COMMENT ON COLUMN "public"."safety_realtime"."sensor_type_name" IS '传感器类型名称,传感器类型名称';
COMMENT ON COLUMN "public"."safety_realtime"."point_value_type_code" IS '测点数值类型," mn:模拟量';
COMMENT ON COLUMN "public"."safety_realtime"."unit_code" IS '测点数值单位编码,如果测点数值类型是mn（模拟量）则为必填项，否则为空。字典值';
COMMENT ON COLUMN "public"."safety_realtime"."unit_name" IS '测点数值单位名称,字典';
COMMENT ON COLUMN "public"."safety_realtime"."location" IS '测点安装位置,传感器实际安装位置';
COMMENT ON COLUMN "public"."safety_realtime"."value" IS '测点值,如果测点值类型是模拟量，值为测点检测到的实际值；最多保留2位小数。如果是开关量，值为0或者1；如果是多态量，根据实际值填写';
COMMENT ON COLUMN "public"."safety_realtime"."alarm_status_code" IS '测点状态,关联测点状表';
COMMENT ON COLUMN "public"."safety_realtime"."alarm_status_name" IS '测点名称,关联测点状表';
COMMENT ON COLUMN "public"."safety_realtime"."data_time" IS '数据时间,文件定义时间';
COMMENT ON COLUMN "public"."safety_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."safety_realtime"."collect_status" IS '0表示异常 1表示正常';
COMMENT ON COLUMN "public"."safety_realtime"."timestamp" IS '同 data_time';
COMMENT ON TABLE "public"."safety_realtime" IS '安监实时表  测点1分钟感知数据的集合';

-- ----------------------------
-- Table structure for safety_substation_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_substation_definition" CASCADE;
CREATE TABLE "public"."safety_substation_definition" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "substation_code" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "substation_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_point" "public"."geometry",
  "collect_status" int2,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."safety_substation_definition"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_substation_definition"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_substation_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_substation_definition"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_substation_definition"."substation_code" IS '分站编码,关联分站表';
COMMENT ON COLUMN "public"."safety_substation_definition"."substation_name" IS '分站安装位置描述';
COMMENT ON COLUMN "public"."safety_substation_definition"."geometry_point" IS '安装位置';
COMMENT ON COLUMN "public"."safety_substation_definition"."collect_status" IS '采集状态';
COMMENT ON COLUMN "public"."safety_substation_definition"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:sss';
COMMENT ON COLUMN "public"."safety_substation_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_substation_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."safety_substation_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."safety_substation_definition" IS '分站表 每天至少一次，变化时更新';

-- ----------------------------
-- Table structure for safety_substation_definition_change
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_substation_definition_change" CASCADE;
CREATE TABLE "public"."safety_substation_definition_change" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar(20) COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "substation_code" varchar(17) COLLATE "pg_catalog"."default" NOT NULL,
  "substation_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "old_name" varchar(100) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "change_type" int4 NOT NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6),
  "change_note" varchar(255) COLLATE "pg_catalog"."default",
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."safety_substation_definition_change"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."mine_name" IS '矿名称,矿名称';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."substation_code" IS '分站编码,关联分站表';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."substation_name" IS '分站名称,关联分站表';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."old_name" IS '原分站名称';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."change_type" IS '变更类型,变更点：0，新增：10，删除：20';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."begin_time" IS '测点开始时间,第一次插入';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."end_time" IS '测点结束时间,更改时间';
COMMENT ON COLUMN "public"."safety_substation_definition_change"."change_note" IS '变化说明';
COMMENT ON TABLE "public"."safety_substation_definition_change" IS ' 分站表 每次比较后插入';

-- ----------------------------
-- Records of safety_substation_definition_change
-- ----------------------------

-- ----------------------------
-- Table structure for safety_substation_status_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."safety_substation_status_realtime" CASCADE;
CREATE TABLE "public"."safety_substation_status_realtime" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "mine_name" varchar(100) COLLATE "pg_catalog"."default",
  "group_code" varchar COLLATE "pg_catalog"."default",
  "group_name" varchar(20) COLLATE "pg_catalog"."default",
  "substation_code" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "substation_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "run_status" int4 NOT NULL,
  "power_status" int4 NOT NULL,
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "collect_status" int2
)
;
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."mine_code" IS '矿编码,矿编码';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."mine_name" IS '矿名称';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."group_name" IS '集团名称';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."substation_code" IS '分站编码,关联分站表';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."substation_name" IS '分站名称';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."run_status" IS '分站运行状态,字典';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."power_status" IS '分站供电状态,字典';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."data_time" IS '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."safety_substation_status_realtime"."collect_status" IS '采集状态100正常';
COMMENT ON TABLE "public"."safety_substation_status_realtime" IS '分站实时状态表 更新频率：1m';

-- ----------------------------
-- Records of safety_substation_status_realtime
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table roof_stress_definition
-- ----------------------------
ALTER TABLE "public"."roof_stress_definition" ADD CONSTRAINT "roof_stress-definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table safety_alarm_realtime
-- ----------------------------
ALTER TABLE "public"."safety_alarm_realtime" ADD CONSTRAINT "safety_alarm_realtime_pkey" PRIMARY KEY ("point_id", "alarm_type_code", "begin_time");

-- ----------------------------
-- Primary Key structure for table safety_definition
-- ----------------------------
ALTER TABLE "public"."safety_definition" ADD CONSTRAINT "safety_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table safety_definition_change
-- ----------------------------
ALTER TABLE "public"."safety_definition_change" ADD CONSTRAINT "safety_definition_change_pkey" PRIMARY KEY ("point_id", "begin_time");

-- ----------------------------
-- Primary Key structure for table safety_realtime
-- ----------------------------
ALTER TABLE "public"."safety_realtime" ADD CONSTRAINT "safety_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table safety_substation_definition
-- ----------------------------
ALTER TABLE "public"."safety_substation_definition" ADD CONSTRAINT "safety_substation_definition_pkey" PRIMARY KEY ("substation_code");

-- ----------------------------
-- Primary Key structure for table safety_substation_definition_change
-- ----------------------------
ALTER TABLE "public"."safety_substation_definition_change" ADD CONSTRAINT "safety_substation_definition_change_pkey" PRIMARY KEY ("substation_code");

-- ----------------------------
-- Primary Key structure for table safety_substation_status_realtime
-- ----------------------------
ALTER TABLE "public"."safety_substation_status_realtime" ADD CONSTRAINT "safety_substation_status_realtime_pkey" PRIMARY KEY ("substation_code");

-- ----------------------------
-- Foreign Keys structure for table safety_definition
-- ----------------------------
ALTER TABLE "public"."safety_definition" ADD CONSTRAINT "point_pkey" FOREIGN KEY ("point_id") REFERENCES "public"."safety_definition" ("point_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Table structure for slight_shock_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."slight_shock_definition" CASCADE;
CREATE TABLE "public"."slight_shock_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "area_name" varchar COLLATE "pg_catalog"."default",
  "passageway_amount" varchar(100) COLLATE "pg_catalog"."default",
  "sampling_length" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_length_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_frequency" varchar(255) COLLATE "pg_catalog"."default",
  "sampling_frequency_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_direction" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_direction_name" varchar(255) COLLATE "pg_catalog"."default",
  "install_mode" varchar(255) COLLATE "pg_catalog"."default",
  "sensitivity" varchar(255) COLLATE "pg_catalog"."default",
  "sensitivity_unit" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_type" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" varchar(255) COLLATE "pg_catalog"."default",
  "y" varchar(255) COLLATE "pg_catalog"."default",
  "z" varchar(255) COLLATE "pg_catalog"."default",
  "install_time" timestamptz(6),
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."slight_shock_definition"."area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."slight_shock_definition"."passageway_amount" IS '记录通道数';
COMMENT ON COLUMN "public"."slight_shock_definition"."sampling_length" IS '采样长度';
COMMENT ON COLUMN "public"."slight_shock_definition"."sampling_length_unit" IS '采样长度单位';
COMMENT ON COLUMN "public"."slight_shock_definition"."sampling_frequency" IS '采样频率';
COMMENT ON COLUMN "public"."slight_shock_definition"."sampling_frequency_unit" IS '采样频率单位';
COMMENT ON COLUMN "public"."slight_shock_definition"."sensor_direction" IS '传感器方向';
COMMENT ON COLUMN "public"."slight_shock_definition"."sensor_direction_name" IS '传感器方向名称';
COMMENT ON COLUMN "public"."slight_shock_definition"."install_mode" IS '安装方式';
COMMENT ON COLUMN "public"."slight_shock_definition"."sensitivity" IS '灵敏度';
COMMENT ON COLUMN "public"."slight_shock_definition"."sensitivity_unit" IS '灵敏度单位';
COMMENT ON COLUMN "public"."slight_shock_definition"."sensor_type" IS '传感器类型';
COMMENT ON COLUMN "public"."slight_shock_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."slight_shock_definition"."x" IS '传感器坐标 X';
COMMENT ON COLUMN "public"."slight_shock_definition"."y" IS '传感器坐标Y';
COMMENT ON COLUMN "public"."slight_shock_definition"."z" IS '传感器坐标 Z';
COMMENT ON COLUMN "public"."slight_shock_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."slight_shock_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."slight_shock_definition"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."slight_shock_definition" IS '微震采集点信息表';

-- ----------------------------
-- Table structure for slight_shock_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."slight_shock_realtime" CASCADE;
CREATE TABLE "public"."slight_shock_realtime" (
  "event_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "passageway_amount" varchar(100) COLLATE "pg_catalog"."default",
  "x" varchar(255) COLLATE "pg_catalog"."default",
  "y" varchar(255) COLLATE "pg_catalog"."default",
  "z" varchar(255) COLLATE "pg_catalog"."default",
  "energy" varchar(255) COLLATE "pg_catalog"."default",
  "level" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "max_amplitude" varchar(255) COLLATE "pg_catalog"."default",
  "amplitude_unit" varchar(255) COLLATE "pg_catalog"."default",
  "avg_amplitude" varchar(255) COLLATE "pg_catalog"."default",
  "dominant_frequency" varchar(255) COLLATE "pg_catalog"."default",
  "dominant_frequency_unit" varchar(255) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6),
  "analysis_results" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."slight_shock_realtime"."event_id" IS '事件编号';
COMMENT ON COLUMN "public"."slight_shock_realtime"."passageway_amount" IS '记录地音事件的通道号';
COMMENT ON COLUMN "public"."slight_shock_realtime"."x" IS '震源坐标X';
COMMENT ON COLUMN "public"."slight_shock_realtime"."y" IS '震源坐标Y';
COMMENT ON COLUMN "public"."slight_shock_realtime"."z" IS '震源坐标Z';
COMMENT ON COLUMN "public"."slight_shock_realtime"."energy" IS '震源能量';
COMMENT ON COLUMN "public"."slight_shock_realtime"."level" IS '震源震级';
COMMENT ON COLUMN "public"."slight_shock_realtime"."location" IS '微震事件位置描述';
COMMENT ON COLUMN "public"."slight_shock_realtime"."max_amplitude" IS '最大振幅';
COMMENT ON COLUMN "public"."slight_shock_realtime"."amplitude_unit" IS '振幅单位';
COMMENT ON COLUMN "public"."slight_shock_realtime"."avg_amplitude" IS '平均振幅';
COMMENT ON COLUMN "public"."slight_shock_realtime"."dominant_frequency" IS '微震事件波形主频';
COMMENT ON COLUMN "public"."slight_shock_realtime"."dominant_frequency_unit" IS '微震事件波形主频单位';
COMMENT ON COLUMN "public"."slight_shock_realtime"."data_time" IS '发生时间';
COMMENT ON COLUMN "public"."slight_shock_realtime"."analysis_results" IS '事件分析结论';
COMMENT ON COLUMN "public"."slight_shock_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."slight_shock_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."slight_shock_realtime" IS '高频微震实时监测信息表';

-- ----------------------------
-- Table structure for stress_monitor_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."stress_monitor_definition" CASCADE;
CREATE TABLE "public"."stress_monitor_definition" (
  "system_name" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_status" int4,
  "unit" varchar COLLATE "pg_catalog"."default",
  "working_face" varchar COLLATE "pg_catalog"."default",
  "measurement_area" varchar COLLATE "pg_catalog"."default",
  "pre_pressure" float8,
  "distance" float8,
  "depth" float8,
  "group_id" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "use_type" int4 DEFAULT 1,
  "view_type" int4 DEFAULT 3,
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "tunnel_guid" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "geometry_point" "public"."geometry",
  "point_type" int4,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "area_id" int4,
  "x" float8,
  "y" float8,
  "z" float8,
  "reserve1" varchar COLLATE "pg_catalog"."default",
  "reserve2" varchar COLLATE "pg_catalog"."default",
  "reserve3" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "sensor_type" varchar COLLATE "pg_catalog"."default",
  "memo" varchar COLLATE "pg_catalog"."default",
  "description" varchar COLLATE "pg_catalog"."default",
  "yellow_warn_value" float8,
  "red_warn_value" float8,
  "initial_value" float8,
  "device_id" int4,
  "upand_down" int4,
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6),
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "warn_information" varchar(255) COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "point_count" int4,
  "point_position" varchar(255) COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "record_channel_count" int4,
  "sampling_length" int4,
  "sampling_frequency" int4,
  "install_way" varchar(255) COLLATE "pg_catalog"."default",
  "sensitivity" float8,
  "vibration_energy" float8,
  "vibration_level" float8,
  "max_amplitude" float8,
  "avg_amplitude" float8,
  "max_frequency" float8,
  "vibration_channel_no" float8
)
;
COMMENT ON COLUMN "public"."stress_monitor_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."stress_monitor_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."stress_monitor_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."stress_monitor_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."stress_monitor_definition"."working_face" IS '工作面';
COMMENT ON COLUMN "public"."stress_monitor_definition"."measurement_area" IS '测量区域';
COMMENT ON COLUMN "public"."stress_monitor_definition"."pre_pressure" IS '压力';
COMMENT ON COLUMN "public"."stress_monitor_definition"."distance" IS '距离';
COMMENT ON COLUMN "public"."stress_monitor_definition"."depth" IS '深度';
COMMENT ON COLUMN "public"."stress_monitor_definition"."group_id" IS '集团代码';
COMMENT ON COLUMN "public"."stress_monitor_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."stress_monitor_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."stress_monitor_definition"."use_type" IS '使用类型  默认1';
COMMENT ON COLUMN "public"."stress_monitor_definition"."view_type" IS '视图类型  默认3';
COMMENT ON COLUMN "public"."stress_monitor_definition"."mining_area_id" IS '矿区域id';
COMMENT ON COLUMN "public"."stress_monitor_definition"."geolocation_area_id" IS '地理位置';
COMMENT ON COLUMN "public"."stress_monitor_definition"."tunnel_guid" IS '巷道id';
COMMENT ON COLUMN "public"."stress_monitor_definition"."name" IS '名称';
COMMENT ON COLUMN "public"."stress_monitor_definition"."geometry_point" IS '几何点';
COMMENT ON COLUMN "public"."stress_monitor_definition"."point_type" IS '测点类型';
COMMENT ON COLUMN "public"."stress_monitor_definition"."data_type" IS '数据类型';
COMMENT ON COLUMN "public"."stress_monitor_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."stress_monitor_definition"."type" IS '类型';
COMMENT ON COLUMN "public"."stress_monitor_definition"."area_id" IS '区域id';
COMMENT ON COLUMN "public"."stress_monitor_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."stress_monitor_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."stress_monitor_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."stress_monitor_definition"."reserve1" IS '储量1';
COMMENT ON COLUMN "public"."stress_monitor_definition"."reserve2" IS '储量2';
COMMENT ON COLUMN "public"."stress_monitor_definition"."reserve3" IS '储量3';
COMMENT ON COLUMN "public"."stress_monitor_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."stress_monitor_definition"."sensor_type" IS '传感器名称';
COMMENT ON COLUMN "public"."stress_monitor_definition"."memo" IS '备忘录';
COMMENT ON COLUMN "public"."stress_monitor_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."stress_monitor_definition"."yellow_warn_value" IS '黄色警告值';
COMMENT ON COLUMN "public"."stress_monitor_definition"."red_warn_value" IS '红色警告值';
COMMENT ON COLUMN "public"."stress_monitor_definition"."initial_value" IS '监测值';
COMMENT ON COLUMN "public"."stress_monitor_definition"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."stress_monitor_definition"."upand_down" IS '上下';
COMMENT ON COLUMN "public"."stress_monitor_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."stress_monitor_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."stress_monitor_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."stress_monitor_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."stress_monitor_definition"."timestamp" IS '采集时刻';
COMMENT ON COLUMN "public"."stress_monitor_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."stress_monitor_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."stress_monitor_definition"."warn_information" IS '预警记录';
COMMENT ON COLUMN "public"."stress_monitor_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."stress_monitor_definition"."point_count" IS '基点数量';
COMMENT ON COLUMN "public"."stress_monitor_definition"."point_position" IS '基点位置';
COMMENT ON COLUMN "public"."stress_monitor_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."stress_monitor_definition"."record_channel_count" IS '记录通道数';
COMMENT ON COLUMN "public"."stress_monitor_definition"."sampling_length" IS '采样长度';
COMMENT ON COLUMN "public"."stress_monitor_definition"."sampling_frequency" IS '采样频率';
COMMENT ON COLUMN "public"."stress_monitor_definition"."install_way" IS '传感器安装方式';
COMMENT ON COLUMN "public"."stress_monitor_definition"."sensitivity" IS '灵敏度';
COMMENT ON COLUMN "public"."stress_monitor_definition"."vibration_energy" IS '震源能量';
COMMENT ON COLUMN "public"."stress_monitor_definition"."vibration_level" IS '震源震级';
COMMENT ON COLUMN "public"."stress_monitor_definition"."max_amplitude" IS '最大振幅';
COMMENT ON COLUMN "public"."stress_monitor_definition"."avg_amplitude" IS '平均振幅';
COMMENT ON COLUMN "public"."stress_monitor_definition"."max_frequency" IS '微震时间波形频谱最大值';
COMMENT ON COLUMN "public"."stress_monitor_definition"."vibration_channel_no" IS '微震事件的通道号';

-- ----------------------------
-- Records of stress_monitor_definition
-- ----------------------------

-- ----------------------------
-- Table structure for stress_monitor_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."stress_monitor_realtime" CASCADE;
CREATE TABLE "public"."stress_monitor_realtime" (
  "mine_code" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" float8,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "state" varchar COLLATE "pg_catalog"."default",
  "area_id" int4,
  "x" float8,
  "y" float8,
  "z" float8,
  "zf_value" float8,
  "float_time" float8,
  "reserve1" varchar COLLATE "pg_catalog"."default",
  "reserve2" varchar COLLATE "pg_catalog"."default",
  "reserve3" varchar COLLATE "pg_catalog"."default",
  "mpname" varchar COLLATE "pg_catalog"."default",
  "memo" varchar COLLATE "pg_catalog"."default",
  "specific_status" varchar COLLATE "pg_catalog"."default",
  "mine_name" varchar(255) COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "monitor_value" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."stress_monitor_realtime"."mine_code" IS '煤矿编码';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."timestamp" IS '监测时间';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."value" IS '值';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."state" IS '状态';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."mine_name" IS '矿井名称';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."channel_no" IS '通道号';
COMMENT ON COLUMN "public"."stress_monitor_realtime"."monitor_value" IS '监测值';

-- ----------------------------
-- Records of stress_monitor_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for system_monitor
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_monitor" CASCADE;
CREATE TABLE "public"."system_monitor" (
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" int4 NOT NULL,
  "message" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "good_percent" float8
)
;
COMMENT ON COLUMN "public"."system_monitor"."name" IS '系统名称';
COMMENT ON COLUMN "public"."system_monitor"."state" IS '状态';

-- ----------------------------
-- Records of system_monitor
-- ----------------------------

-- ----------------------------
-- Table structure for system_statistics
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_statistics" CASCADE;
CREATE TABLE "public"."system_statistics" (
  "system" text COLLATE "pg_catalog"."default" NOT NULL,
  "count" int4 NOT NULL
)
;
COMMENT ON COLUMN "public"."system_statistics"."system" IS '系统名称';
COMMENT ON COLUMN "public"."system_statistics"."count" IS '数据上传数量';
COMMENT ON TABLE "public"."system_statistics" IS '系统接入统计表';

-- ----------------------------
-- Records of system_statistics
-- ----------------------------
-- INSERT INTO "public"."system_statistics" VALUES ('水文观察系统', 89);
-- INSERT INTO "public"."system_statistics" VALUES ('矿压监测系统', 160);

-- ----------------------------
-- Primary Key structure for table slight_shock_definition
-- ----------------------------
ALTER TABLE "public"."slight_shock_definition" ADD CONSTRAINT "slight_shock_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table slight_shock_realtime
-- ----------------------------
ALTER TABLE "public"."slight_shock_realtime" ADD CONSTRAINT "slight_shock_realtime_pkey" PRIMARY KEY ("event_id");

-- ----------------------------
-- Indexes structure for table stress_monitor_definition
-- ----------------------------
CREATE UNIQUE INDEX "stress_measure_point_id_area_id_idx" ON "public"."stress_monitor_definition" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "area_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table stress_monitor_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_stress_monitor_definition" BEFORE UPDATE ON "public"."stress_monitor_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table stress_monitor_definition
-- ----------------------------
ALTER TABLE "public"."stress_monitor_definition" ADD CONSTRAINT "stress_monitor_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table stress_monitor_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_stress_monitor_realtime" BEFORE UPDATE ON "public"."stress_monitor_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table stress_monitor_realtime
-- ----------------------------
ALTER TABLE "public"."stress_monitor_realtime" ADD CONSTRAINT "stress_monitor_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table system_monitor
-- ----------------------------
CREATE TRIGGER "upd_system_monitor" BEFORE UPDATE ON "public"."system_monitor"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table system_monitor
-- ----------------------------
ALTER TABLE "public"."system_monitor" ADD CONSTRAINT "name_pk" PRIMARY KEY ("name");

-- ----------------------------
-- Primary Key structure for table system_statistics
-- ----------------------------
ALTER TABLE "public"."system_statistics" ADD CONSTRAINT "system_statistics_pkey" PRIMARY KEY ("system");

-- ----------------------------
-- Foreign Keys structure for table stress_monitor_definition
-- ----------------------------
ALTER TABLE "public"."stress_monitor_definition" ADD CONSTRAINT "stress_monitor_definition_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."stress_monitor_definition" ADD CONSTRAINT "stress_monitor_definition_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table stress_monitor_realtime
-- ----------------------------
ALTER TABLE "public"."stress_monitor_realtime" ADD CONSTRAINT "stress_monitor_definition_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."stress_monitor_definition" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for tube_monitor_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."tube_monitor_definition" CASCADE;
CREATE TABLE "public"."tube_monitor_definition" (
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "point_name" varchar(255) COLLATE "pg_catalog"."default" NULL,
  "system_type" varchar(100) COLLATE "pg_catalog"."default",
  "system_name" varchar(100) COLLATE "pg_catalog"."default",
  "manufacturer_name" varchar(100) COLLATE "pg_catalog"."default",
  "sensor_type" varchar(100) COLLATE "pg_catalog"."default",
  "data_type" varchar(100) COLLATE "pg_catalog"."default",
  "unit" varchar(10) COLLATE "pg_catalog"."default",
  "high_range" float8,
  "low_range" float8,
  "upper_alarm" float8,
  "area_code" varchar(10) COLLATE "pg_catalog"."default",
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "x" varchar(255) COLLATE "pg_catalog"."default",
  "y" varchar(255) COLLATE "pg_catalog"."default",
  "z" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  "data_time" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."tube_monitor_definition"."mine_code" IS '矿井编码';
COMMENT ON COLUMN "public"."tube_monitor_definition"."point_id" IS '传感器编码';
COMMENT ON COLUMN "public"."tube_monitor_definition"."point_name" IS '传感器名称';
COMMENT ON COLUMN "public"."tube_monitor_definition"."system_type" IS '系统型号';
COMMENT ON COLUMN "public"."tube_monitor_definition"."system_name" IS '系统名称';
COMMENT ON COLUMN "public"."tube_monitor_definition"."manufacturer_name" IS '厂家名称';
COMMENT ON COLUMN "public"."tube_monitor_definition"."sensor_type" IS '传感器类型（CH4、CO等）';
COMMENT ON COLUMN "public"."tube_monitor_definition"."data_type" IS '传感器数值类型（模拟量）';
COMMENT ON COLUMN "public"."tube_monitor_definition"."unit" IS '传感器数值单位（%、ppm）';
COMMENT ON COLUMN "public"."tube_monitor_definition"."high_range" IS '高量程';
COMMENT ON COLUMN "public"."tube_monitor_definition"."low_range" IS '低量程';
COMMENT ON COLUMN "public"."tube_monitor_definition"."upper_alarm" IS '上限报警门限';
COMMENT ON COLUMN "public"."tube_monitor_definition"."area_code" IS '传感器监测区域编码';
COMMENT ON COLUMN "public"."tube_monitor_definition"."area_name" IS '传感器监测区域名称';
COMMENT ON COLUMN "public"."tube_monitor_definition"."x" IS '位置x';
COMMENT ON COLUMN "public"."tube_monitor_definition"."y" IS '位置y';
COMMENT ON COLUMN "public"."tube_monitor_definition"."z" IS '位置z';
COMMENT ON COLUMN "public"."tube_monitor_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tube_monitor_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."tube_monitor_definition"."data_time" IS '数据时间';
COMMENT ON TABLE "public"."tube_monitor_definition" IS '束管监测系统';

-- ----------------------------
-- Table structure for tube_monitor_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."tube_monitor_realtime" CASCADE;
CREATE TABLE "public"."tube_monitor_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "sensor_type" varchar(100) COLLATE "pg_catalog"."default",
  "area_code" varchar(10) COLLATE "pg_catalog"."default",
  "area_name" varchar(100) COLLATE "pg_catalog"."default",
  "value" float8,
  "status" varchar(10) COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "unit" varchar(10) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6),
  "state" varchar COLLATE "pg_catalog"."default",
  "mine_code" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."tube_monitor_realtime"."point_id" IS '传感器编码';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."sensor_type" IS '传感器类型';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."area_code" IS '传感器监测区域编码';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."area_name" IS '传感器监测区域名称';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."value" IS '传感器实时值';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."status" IS '传感器状态';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."timestamp" IS '上传数据时间yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."unit" IS '传感器数值单位';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."state" IS '传感器上传数据状态';
COMMENT ON COLUMN "public"."tube_monitor_realtime"."mine_code" IS '矿编码';

-- ----------------------------
-- Table structure for tunnel_distance_stress_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."tunnel_distance_stress_definition" CASCADE;
CREATE TABLE "public"."tunnel_distance_stress_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_status" int4,
  "sensor_type" varchar COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "description" varchar COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "unit" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "station_type" varchar(255) COLLATE "pg_catalog"."default",
  "station_number" varchar COLLATE "pg_catalog"."default",
  "point_monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."sensor_type" IS '传感器名称';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."station_type" IS '测站类型';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."station_number" IS '测站编号';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."point_monitor_type" IS '通道监测类型';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."channel_no" IS '数据使用通道';
COMMENT ON COLUMN "public"."tunnel_distance_stress_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."tunnel_distance_stress_definition" IS '矿压巷道位移 ';

-- ----------------------------
-- Records of tunnel_distance_stress_definition
-- ----------------------------

-- ----------------------------
-- Table structure for vehicle_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."vehicle_definition" CASCADE;
CREATE TABLE "public"."vehicle_definition" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "car_no" varchar COLLATE "pg_catalog"."default",
  "car_name" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "car_type" varchar COLLATE "pg_catalog"."default",
  "dept_name" varchar COLLATE "pg_catalog"."default",
  "group_name" varchar COLLATE "pg_catalog"."default",
  "class_type" varchar COLLATE "pg_catalog"."default",
  "type" int4,
  "geometry_point" "public"."geometry"
)
;
COMMENT ON COLUMN "public"."vehicle_definition"."card_id" IS '车辆卡号';
COMMENT ON COLUMN "public"."vehicle_definition"."car_no" IS '车辆编号';
COMMENT ON COLUMN "public"."vehicle_definition"."car_name" IS '车辆名称';
COMMENT ON COLUMN "public"."vehicle_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."vehicle_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."vehicle_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."vehicle_definition"."car_type" IS '车辆类型';
COMMENT ON COLUMN "public"."vehicle_definition"."dept_name" IS '所属部门';
COMMENT ON COLUMN "public"."vehicle_definition"."group_name" IS '所属班组';
COMMENT ON COLUMN "public"."vehicle_definition"."class_type" IS '班次';
COMMENT ON COLUMN "public"."vehicle_definition"."type" IS '类型，区分车辆、分站、道岔';

-- ----------------------------
-- Records of vehicle_definition
-- ----------------------------

-- ----------------------------
-- Table structure for vehicle_position
-- ----------------------------
DROP TABLE IF EXISTS "public"."vehicle_position" CASCADE;
CREATE TABLE "public"."vehicle_position" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "x" float8,
  "y" float8,
  "drive_time" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "enter_area_time" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "rec_time" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "map_id" int4,
  "area_name" varchar(32) COLLATE "pg_catalog"."default",
  "state_card" varchar(32) COLLATE "pg_catalog"."default",
  "state_object" int4,
  "state_bz" varchar(32) COLLATE "pg_catalog"."default",
  "speed" float8,
  "mark_name" varchar(32) COLLATE "pg_catalog"."default",
  "mark_direction" varchar(32) COLLATE "pg_catalog"."default",
  "mark_distance" float8,
  "td_vehicle" int4,
  "set_move" int4,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."vehicle_position"."card_id" IS '车辆卡号';
COMMENT ON COLUMN "public"."vehicle_position"."drive_time" IS '出车时间';
COMMENT ON COLUMN "public"."vehicle_position"."enter_area_time" IS '进入区域时间';
COMMENT ON COLUMN "public"."vehicle_position"."rec_time" IS '接收时间';
COMMENT ON COLUMN "public"."vehicle_position"."map_id" IS '地图';
COMMENT ON COLUMN "public"."vehicle_position"."area_name" IS '区域';
COMMENT ON COLUMN "public"."vehicle_position"."state_card" IS '电量状态';
COMMENT ON COLUMN "public"."vehicle_position"."state_object" IS '绑定对象状态';
COMMENT ON COLUMN "public"."vehicle_position"."state_bz" IS '卡业务状态';
COMMENT ON COLUMN "public"."vehicle_position"."speed" IS '速度';
COMMENT ON COLUMN "public"."vehicle_position"."mark_name" IS '地标编号';
COMMENT ON COLUMN "public"."vehicle_position"."mark_direction" IS '方向';
COMMENT ON COLUMN "public"."vehicle_position"."mark_distance" IS '距离';
COMMENT ON COLUMN "public"."vehicle_position"."td_vehicle" IS '当天是否出车';
COMMENT ON COLUMN "public"."vehicle_position"."set_move" IS '是否做动画';
COMMENT ON COLUMN "public"."vehicle_position"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."vehicle_position"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of vehicle_position
-- ----------------------------

-- ----------------------------
-- Table structure for vehicle_time
-- ----------------------------
DROP TABLE IF EXISTS "public"."vehicle_time" CASCADE;
CREATE TABLE "public"."vehicle_time" (
  "card_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "drive_time" timestamptz(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "enter_area_time" timestamptz(6),
  "rec_time" timestamptz(6),
  "work_time" float8,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."vehicle_time"."card_id" IS '车辆卡号';
COMMENT ON COLUMN "public"."vehicle_time"."drive_time" IS '出车时间';
COMMENT ON COLUMN "public"."vehicle_time"."enter_area_time" IS '进入区域时间';
COMMENT ON COLUMN "public"."vehicle_time"."rec_time" IS '接收时间';
COMMENT ON COLUMN "public"."vehicle_time"."work_time" IS '工作时长';
COMMENT ON COLUMN "public"."vehicle_time"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."vehicle_time"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of vehicle_time
-- ----------------------------

-- ----------------------------
-- Table structure for vibration_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."vibration_definition" CASCADE;
CREATE TABLE "public"."vibration_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "data_type" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "point_type" int4,
  "type" varchar COLLATE "pg_catalog"."default",
  "unit" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."vibration_definition"."point_id" IS '传感器名';
COMMENT ON COLUMN "public"."vibration_definition"."name" IS '名称';
COMMENT ON COLUMN "public"."vibration_definition"."data_type" IS '值类型';
COMMENT ON COLUMN "public"."vibration_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."vibration_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."vibration_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."vibration_definition"."point_type" IS '0,模拟量 1，开关量';
COMMENT ON COLUMN "public"."vibration_definition"."type" IS '类型';
COMMENT ON COLUMN "public"."vibration_definition"."unit" IS '单位';
COMMENT ON TABLE "public"."vibration_definition" IS '振动传感器定义表';

-- ----------------------------
-- Records of vibration_definition
-- ----------------------------

-- ----------------------------
-- Table structure for vibration_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."vibration_realtime" CASCADE;
CREATE TABLE "public"."vibration_realtime" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "alarm" bool NOT NULL,
  "alarm_level" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "timestamp" timestamptz(6) NOT NULL,
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "specific_status" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."vibration_realtime"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."vibration_realtime"."value" IS '实时值';
COMMENT ON COLUMN "public"."vibration_realtime"."state" IS '状态描述';
COMMENT ON COLUMN "public"."vibration_realtime"."alarm" IS '是否报警';
COMMENT ON COLUMN "public"."vibration_realtime"."alarm_level" IS '报警级别';
COMMENT ON COLUMN "public"."vibration_realtime"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."vibration_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."vibration_realtime"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."vibration_realtime" IS '振动传感器实时表';

-- ----------------------------
-- Records of vibration_realtime
-- ----------------------------

-- ----------------------------
-- Table structure for water_observation_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."water_observation_definition" CASCADE;
CREATE TABLE "public"."water_observation_definition" (
  "mine_code" varchar COLLATE "pg_catalog"."default",
  "area_name" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "observe_positon" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "observe_thickness" float8,
  "observe_depth" float8,
  "hole_depth" float8,
  "hole_diameter" float8,
  "geometry_point" "public"."geometry",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "view_type" int4 DEFAULT 3,
  "use_type" int4 DEFAULT 1,
  "mining_area_id" int4,
  "geolocation_area_id" int4,
  "tunnel_guid" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "hole_point" "public"."geometry",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "point_type" int4,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "parent_id" text COLLATE "pg_catalog"."default",
  "embed_type" int4,
  "upper_range_value" numeric,
  "upper_warn_value" numeric,
  "lower_warn_value" numeric,
  "ground_elevation" numeric,
  "aditus_width" numeric,
  "depth_water_adjust" numeric,
  "zero_value" numeric,
  "slope_ratio" numeric,
  "structure_height" numeric,
  "pipe_ratio" numeric,
  "type_code" varchar(255) COLLATE "pg_catalog"."default",
  "geo_x" numeric(255,0),
  "geo_y" numeric(255,0),
  "geo_z" numeric(255,0),
  "water_typ" varchar(255) COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6),
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "location_type" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."water_observation_definition"."mine_code" IS '矿井编码';
COMMENT ON COLUMN "public"."water_observation_definition"."area_name" IS '地段名称';
COMMENT ON COLUMN "public"."water_observation_definition"."point_id" IS '水文点名';
COMMENT ON COLUMN "public"."water_observation_definition"."observe_positon" IS '观测层位';
COMMENT ON COLUMN "public"."water_observation_definition"."type" IS '测点属性类型:传感器类型名称:如流量，管道流量等';
COMMENT ON COLUMN "public"."water_observation_definition"."observe_thickness" IS '观测层厚';
COMMENT ON COLUMN "public"."water_observation_definition"."observe_depth" IS '观测层深';
COMMENT ON COLUMN "public"."water_observation_definition"."hole_depth" IS '孔深';
COMMENT ON COLUMN "public"."water_observation_definition"."hole_diameter" IS '孔径';
COMMENT ON COLUMN "public"."water_observation_definition"."geometry_point" IS '地理信息';
COMMENT ON COLUMN "public"."water_observation_definition"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."water_observation_definition"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."water_observation_definition"."hole_point" IS '孔坐标';
COMMENT ON COLUMN "public"."water_observation_definition"."point_type" IS '测点类型:整形 0-模拟量 1-开关量 2- 状态量 3-累计量 4-文本量';
COMMENT ON COLUMN "public"."water_observation_definition"."data_type" IS '值类型:必填项,int/float/string';
COMMENT ON COLUMN "public"."water_observation_definition"."type_code" IS '测点属性编号:传感器类型编号:如流量，管道流量等';
COMMENT ON COLUMN "public"."water_observation_definition"."geo_x" IS '采集点X（pos_x）';
COMMENT ON COLUMN "public"."water_observation_definition"."geo_y" IS '采集点Y（pos_y）';
COMMENT ON COLUMN "public"."water_observation_definition"."geo_z" IS '采集点Z（pos_z）';
COMMENT ON COLUMN "public"."water_observation_definition"."water_typ" IS '类型（1：水文观测孔   2：涌水量，3.降水量，4：地表沉陷，5：抽水实验，6：突水点）';
COMMENT ON COLUMN "public"."water_observation_definition"."timestamp" IS '监测时间';
COMMENT ON COLUMN "public"."water_observation_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."water_observation_definition"."location_type" IS '位置类别，井上/井下';

-- ----------------------------
-- Table structure for water_observation_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."water_observation_realtime" CASCADE;
CREATE TABLE "public"."water_observation_realtime" (
  "mine_code" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "value" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamptz(6) NOT NULL,
  "water_sample_code" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "state" varchar COLLATE "pg_catalog"."default",
  "specific_status" varchar COLLATE "pg_catalog"."default",
  "water_type" varchar(255) COLLATE "pg_catalog"."default",
  "type_code" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "status" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."water_observation_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."water_observation_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."water_observation_realtime"."water_type" IS '（1：水文观测孔   2：涌水量，3.降水量，4：地表沉陷，5：抽水实验，6：突水点）';
COMMENT ON COLUMN "public"."water_observation_realtime"."type_code" IS '传感器类型编号:如流量，管道流量等';
COMMENT ON COLUMN "public"."water_observation_realtime"."type" IS '传感器类型名称:如流量，管道流量等';
COMMENT ON COLUMN "public"."water_observation_realtime"."unit" IS '单位';
COMMENT ON COLUMN "public"."water_observation_realtime"."status" IS '状态';

-- ----------------------------
-- Primary Key structure for table tube_monitor_definition
-- ----------------------------
ALTER TABLE "public"."tube_monitor_definition" ADD CONSTRAINT "tube_monitor_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table tube_monitor_realtime
-- ----------------------------
ALTER TABLE "public"."tube_monitor_realtime" ADD CONSTRAINT "tube_monitor_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Primary Key structure for table tunnel_distance_stress_definition
-- ----------------------------
ALTER TABLE "public"."tunnel_distance_stress_definition" ADD CONSTRAINT "tunnel_distance_stress_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table vehicle_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_vehicle_definition" BEFORE UPDATE ON "public"."vehicle_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table vehicle_definition
-- ----------------------------
ALTER TABLE "public"."vehicle_definition" ADD CONSTRAINT "vehicle_definition_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Triggers structure for table vehicle_position
-- ----------------------------
CREATE TRIGGER "upd_timestamp_vehicle_position" BEFORE UPDATE ON "public"."vehicle_position"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table vehicle_position
-- ----------------------------
ALTER TABLE "public"."vehicle_position" ADD CONSTRAINT "vehicle_position_pkey" PRIMARY KEY ("card_id");

-- ----------------------------
-- Triggers structure for table vehicle_time
-- ----------------------------
CREATE TRIGGER "upd_timestamp_vehicle_time" BEFORE UPDATE ON "public"."vehicle_time"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table vehicle_time
-- ----------------------------
ALTER TABLE "public"."vehicle_time" ADD CONSTRAINT "vehicle_time_pkey" PRIMARY KEY ("card_id", "drive_time");

-- ----------------------------
-- Triggers structure for table vibration_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_vibration_definition" BEFORE UPDATE ON "public"."vibration_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table vibration_definition
-- ----------------------------
ALTER TABLE "public"."vibration_definition" ADD CONSTRAINT "vibration_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table vibration_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_vibration_realtime" BEFORE UPDATE ON "public"."vibration_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table vibration_realtime
-- ----------------------------
ALTER TABLE "public"."vibration_realtime" ADD CONSTRAINT "vibration_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table water_observation_definition
-- ----------------------------
CREATE TRIGGER "upd_timestamp_water_observation_definition" BEFORE UPDATE ON "public"."water_observation_definition"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table water_observation_definition
-- ----------------------------
ALTER TABLE "public"."water_observation_definition" ADD CONSTRAINT "water_observation_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Triggers structure for table water_observation_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_water_observation_realtime" BEFORE UPDATE ON "public"."water_observation_realtime"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table water_observation_realtime
-- ----------------------------
ALTER TABLE "public"."water_observation_realtime" ADD CONSTRAINT "water_observation_realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Foreign Keys structure for table vehicle_position
-- ----------------------------
ALTER TABLE "public"."vehicle_position" ADD CONSTRAINT "vehicle_position_card_id_fkey" FOREIGN KEY ("card_id") REFERENCES "public"."vehicle_definition" ("card_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table vehicle_time
-- ----------------------------
ALTER TABLE "public"."vehicle_time" ADD CONSTRAINT "vehicle_time_card_id_fkey" FOREIGN KEY ("card_id") REFERENCES "public"."vehicle_position" ("card_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table vibration_realtime
-- ----------------------------
ALTER TABLE "public"."vibration_realtime" ADD CONSTRAINT "vibration_realtime_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."vibration_definition" ("point_id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table water_observation_definition
-- ----------------------------
ALTER TABLE "public"."water_observation_definition" ADD CONSTRAINT "water_observation_definition_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."water_observation_definition" ADD CONSTRAINT "water_observation_definition_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table water_observation_realtime
-- ----------------------------
ALTER TABLE "public"."water_observation_realtime" ADD CONSTRAINT "water_observation_definition_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."water_observation_definition" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for mapping_equip_attr_point
-- ----------------------------
DROP TABLE IF EXISTS "public"."mapping_equip_attr_point";
CREATE TABLE "public"."mapping_equip_attr_point" (
   "equip_id" int4 NOT NULL,
   "attr_id" int4 NOT NULL,
   "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "id" SERIAL NOT NULL,
   "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
   "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."mapping_equip_attr_point"."equip_id" IS 'equipment id    ： 设备id';
COMMENT ON COLUMN "public"."mapping_equip_attr_point"."attr_id" IS 'attribute id ： 属性id';
COMMENT ON COLUMN "public"."mapping_equip_attr_point"."point_id" IS '点id';
COMMENT ON COLUMN "public"."mapping_equip_attr_point"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."mapping_equip_attr_point"."created_at" IS '更新时间';

-- ----------------------------
-- Triggers structure for table mapping_equip_attr_point
-- ----------------------------
CREATE TRIGGER "upd_timestamp_mapping_equip_attr_point" BEFORE UPDATE ON "public"."mapping_equip_attr_point"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Uniques structure for table mapping_equip_attr_point
-- ----------------------------
ALTER TABLE "public"."mapping_equip_attr_point" ADD CONSTRAINT "uniq_equip_id_pointattr_id" UNIQUE ("equip_id", "attr_id", "point_id");

-- ----------------------------
-- Primary Key structure for table mapping_equip_attr_point
-- ----------------------------
ALTER TABLE "public"."mapping_equip_attr_point" ADD CONSTRAINT "mapping_equip_attr_point_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Table structure for cms_attribute
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_attribute";
CREATE TABLE "public"."cms_attribute" (
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "id" SERIAL NOT NULL,
  "attr_type_id" int4,
  "unit" int4,
  "monitor_type_int" int4,
  "oid" varchar(225) COLLATE "pg_catalog"."default",
  "flag" int2 NOT NULL DEFAULT 0,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cms_attribute"."name" IS '属性名';
COMMENT ON COLUMN "public"."cms_attribute"."id" IS '自增，PK';
COMMENT ON COLUMN "public"."cms_attribute"."attr_type_id" IS '外键，不可为null';
COMMENT ON COLUMN "public"."cms_attribute"."unit" IS '和mine_unit的id对应';
COMMENT ON COLUMN "public"."cms_attribute"."monitor_type_int" IS '监控数据类型  1表示开关量0表示数字量';
COMMENT ON COLUMN "public"."cms_attribute"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of cms_attribute
-- ----------------------------

-- ----------------------------
-- Table structure for cms_china_province
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_china_province";
CREATE TABLE "public"."cms_china_province" (
  "province_name" varchar(255) COLLATE "pg_catalog"."default",
  "province_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."cms_china_province"."province_name" IS '省名';

-- ----------------------------
-- Records of cms_china_province
-- ----------------------------

-- ----------------------------
-- Table structure for cms_device_model
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_device_model";
CREATE TABLE "public"."cms_device_model" (
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "id" SERIAL NOT NULL,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cms_device_model"."name" IS '设备模型--属性对应表';
COMMENT ON COLUMN "public"."cms_device_model"."id" IS '自增，PK';
COMMENT ON COLUMN "public"."cms_device_model"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of cms_device_model
-- ----------------------------

-- ----------------------------
-- Table structure for cms_equipment
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_equipment";
CREATE TABLE "public"."cms_equipment" (
  "id" SERIAL NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "manufacturer" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "mid" int4,
  "label" varchar(255) COLLATE "pg_catalog"."default",
  "oid" varchar COLLATE "pg_catalog"."default",
  "code" int4,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cms_equipment"."id" IS '自增 id';
COMMENT ON COLUMN "public"."cms_equipment"."name" IS '设备名称';
COMMENT ON COLUMN "public"."cms_equipment"."manufacturer" IS '制造商';
COMMENT ON COLUMN "public"."cms_equipment"."type" IS '设备型号';
COMMENT ON COLUMN "public"."cms_equipment"."mid" IS '可选设备模型';
COMMENT ON COLUMN "public"."cms_equipment"."label" IS '别名';
COMMENT ON COLUMN "public"."cms_equipment"."code" IS '关联矿';
COMMENT ON COLUMN "public"."cms_equipment"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of cms_equipment
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mapping_model_attr
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mapping_model_attr";
CREATE TABLE "public"."cms_mapping_model_attr" (
  "model_id" int4 NOT NULL,
  "attr_id" int4 NOT NULL,
  "id" SERIAL NOT NULL,
  "updated_at" timestamptz(6) NOT NULL DEFAULT now(),
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cms_mapping_model_attr"."updated_at" IS '更新时间';

-- ----------------------------
-- Records of cms_mapping_model_attr
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mine_attr_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mine_attr_type";
CREATE TABLE "public"."cms_mine_attr_type" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "unit" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  "unit_id" int4
)
;
COMMENT ON COLUMN "public"."cms_mine_attr_type"."id" IS '自增，cms';
COMMENT ON COLUMN "public"."cms_mine_attr_type"."name" IS '属性名称，目前参照了贵州标准';
COMMENT ON COLUMN "public"."cms_mine_attr_type"."unit" IS '冗余 mine_unit 表中unit 字段';
COMMENT ON COLUMN "public"."cms_mine_attr_type"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."cms_mine_attr_type"."updated_at" IS '修改时间';
COMMENT ON COLUMN "public"."cms_mine_attr_type"."unit_id" IS '属性单位id';
COMMENT ON TABLE "public"."cms_mine_attr_type" IS '属性类型表';

-- ----------------------------
-- Records of cms_mine_attr_type
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mine_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mine_group";
CREATE TABLE "public"."cms_mine_group" (
  "code" SERIAL NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "province_code" char(20) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."cms_mine_group"."code" IS '5位数字，全国煤矿集团统一编码，手工创建';
COMMENT ON COLUMN "public"."cms_mine_group"."name" IS '煤矿集团名称';
COMMENT ON COLUMN "public"."cms_mine_group"."province_code" IS '全国省代码';

-- ----------------------------
-- Records of cms_mine_group
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mine_name
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mine_name";
CREATE TABLE "public"."cms_mine_name" (
  "code" SERIAL NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "group_code" int2 NOT NULL
)
;
COMMENT ON COLUMN "public"."cms_mine_name"."code" IS '6位数字，全国统一矿编码，手动设置';
COMMENT ON COLUMN "public"."cms_mine_name"."name" IS '煤矿名称';
COMMENT ON COLUMN "public"."cms_mine_name"."group_code" IS '5位集团编码，对应mine_group：code';

-- ----------------------------
-- Records of cms_mine_name
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mine_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mine_system";
CREATE TABLE "public"."cms_mine_system" (
  "code" SERIAL NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "definition_table" varchar(255) COLLATE "pg_catalog"."default",
  "rt_table" varchar(255) COLLATE "pg_catalog"."default",
  "history_table" varchar(255) COLLATE "pg_catalog"."default",
  "conf_table_struct" text COLLATE "pg_catalog"."default",
  "exchange" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;
COMMENT ON COLUMN "public"."cms_mine_system"."code" IS '三位，999代表内部扩展业务';
COMMENT ON COLUMN "public"."cms_mine_system"."name" IS '名称';
COMMENT ON COLUMN "public"."cms_mine_system"."definition_table" IS '定义表名';
COMMENT ON COLUMN "public"."cms_mine_system"."rt_table" IS '实时表名，仅供husra使用';
COMMENT ON COLUMN "public"."cms_mine_system"."history_table" IS '历史表';
COMMENT ON COLUMN "public"."cms_mine_system"."conf_table_struct" IS '配置表结构';
COMMENT ON COLUMN "public"."cms_mine_system"."exchange" IS '交换机名';
COMMENT ON COLUMN "public"."cms_mine_system"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."cms_mine_system"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."cms_mine_system" IS '业务系统类别定义表';

-- ----------------------------
-- Records of cms_mine_system
-- ----------------------------

-- ----------------------------
-- Table structure for cms_mine_unit
-- ----------------------------
DROP TABLE IF EXISTS "public"."cms_mine_unit";
CREATE TABLE "public"."cms_mine_unit" (
  "id" SERIAL NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "unit" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;
COMMENT ON COLUMN "public"."cms_mine_unit"."id" IS '自增字段';
COMMENT ON COLUMN "public"."cms_mine_unit"."name" IS '属性单位名称';
COMMENT ON COLUMN "public"."cms_mine_unit"."unit" IS '属性单位';

-- ----------------------------
-- Table structure for config_automation_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."config_automation_group" CASCADE;
CREATE TABLE "public"."config_automation_group" (
    "id" Serial NOT NULL,
    "title" text COLLATE "pg_catalog"."default" NOT NULL,
    "pid" int4,
    "pids" text COLLATE "pg_catalog"."default",
    "order" int4,
    "custom" json,
    CONSTRAINT "config_automation_group_pkey" PRIMARY KEY ("id")
);

-- ----------------------------
-- Table structure for config_automation_group_device_point_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."config_automation_group_device_point_map" CASCADE;
CREATE TABLE "public"."config_automation_group_device_point_map" (
     "id" Serial NOT NULL,
     "group_id" int4 NOT NULL,
     "point_id" text COLLATE "pg_catalog"."default" NOT NULL,
     CONSTRAINT "config_automation_group_device_point_map_pkey" PRIMARY KEY ("id"),
     CONSTRAINT "config_automation_group_device_point_map_device_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."device_point" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE,
     CONSTRAINT "config_automation_group_device_point_map_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."config_automation_group" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);


-- ----------------------------
-- Triggers structure for table cms_attribute
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_attribute" BEFORE UPDATE ON "public"."cms_attribute"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_attribute
-- ----------------------------
ALTER TABLE "public"."cms_attribute" ADD CONSTRAINT "attribute_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table cms_china_province
-- ----------------------------
ALTER TABLE "public"."cms_china_province" ADD CONSTRAINT "china_province_pkey" PRIMARY KEY ("province_code");

-- ----------------------------
-- Triggers structure for table cms_device_model
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_device_model" BEFORE UPDATE ON "public"."cms_device_model"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_device_model
-- ----------------------------
ALTER TABLE "public"."cms_device_model" ADD CONSTRAINT "device_model_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table cms_equipment
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_cms_equipment" BEFORE UPDATE ON "public"."cms_equipment"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_equipment
-- ----------------------------
ALTER TABLE "public"."cms_equipment" ADD CONSTRAINT "equipment_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table cms_mapping_model_attr
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_mapping_model_attr" BEFORE UPDATE ON "public"."cms_mapping_model_attr"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_mapping_model_attr
-- ----------------------------
ALTER TABLE "public"."cms_mapping_model_attr" ADD CONSTRAINT "mapping_model_attr_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table cms_mine_attr_type
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_mine_attr_type" BEFORE UPDATE ON "public"."cms_mine_attr_type"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_mine_group
-- ----------------------------
ALTER TABLE "public"."cms_mine_group" ADD CONSTRAINT "mine_group_pkey" PRIMARY KEY ("code");

-- ----------------------------
-- Primary Key structure for table cms_mine_name
-- ----------------------------
ALTER TABLE "public"."cms_mine_name" ADD CONSTRAINT "mine_name_pkey" PRIMARY KEY ("code");

-- ----------------------------
-- Triggers structure for table cms_mine_system
-- ----------------------------
CREATE TRIGGER "upd_timestamp_cms_mine_system" BEFORE UPDATE ON "public"."cms_mine_system"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table cms_mine_system
-- ----------------------------
ALTER TABLE "public"."cms_mine_system" ADD CONSTRAINT "mine_system_temp_pkey" PRIMARY KEY ("code");

-- ----------------------------
-- Table structure for mine_stress_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."mine_stress_realtime";
CREATE TABLE "public"."mine_stress_realtime" (
  "mine_code" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6) NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default",
  "mine_name" varchar(255) COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "monitor_value" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "deep_monitor_value" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."mine_stress_realtime"."mine_code" IS '煤矿编码';
COMMENT ON COLUMN "public"."mine_stress_realtime"."timestamp" IS '采集时间';
COMMENT ON COLUMN "public"."mine_stress_realtime"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."mine_stress_realtime"."state" IS '状态';
COMMENT ON COLUMN "public"."mine_stress_realtime"."mine_name" IS '矿井名称';
COMMENT ON COLUMN "public"."mine_stress_realtime"."channel_no" IS '通道号';
COMMENT ON COLUMN "public"."mine_stress_realtime"."monitor_value" IS '监测值（谢桥），浅基点监测值(石拉乌素)';
COMMENT ON COLUMN "public"."mine_stress_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."mine_stress_realtime"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."mine_stress_realtime"."deep_monitor_value" IS '深基点监测值';

-- ----------------------------
-- Triggers structure for table mine_stress_realtime
-- ----------------------------
CREATE TRIGGER "upd_timestamp_mine_stress_realtime" BEFORE UPDATE ON "public"."mine_stress_realtime"
FOR EACH ROW EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table mine_stress_realtime
-- ----------------------------
ALTER TABLE "public"."mine_stress_realtime" ADD CONSTRAINT "mine_stress-realtime_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Table structure for model_image
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_image";
CREATE TABLE "public"."model_image" (
  "id" SERIAL NOT NULL,
  "fault_image" varchar COLLATE "pg_catalog"."default",
  "name" varchar COLLATE "pg_catalog"."default",
  "run_image" varchar COLLATE "pg_catalog"."default",
  "stop_image" varchar COLLATE "pg_catalog"."default",
  "type" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6),
  "updated_at" timestamptz(6)
)
;

-- ----------------------------
-- Triggers structure for table model_image
-- ----------------------------
CREATE TRIGGER "upd_timestamp_model_image" BEFORE UPDATE ON "public"."model_image"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Uniques structure for table model_image
-- ----------------------------
ALTER TABLE "public"."model_image" ADD CONSTRAINT "model_image_name_type_key" UNIQUE ("name", "type");

-- ----------------------------
-- Primary Key structure for table model_image
-- ----------------------------
ALTER TABLE "public"."model_image" ADD CONSTRAINT "model_image_pkey" PRIMARY KEY ("id");


-- ----------------------------
-- Table structure for model_business_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_business_type";
CREATE TABLE "public"."model_business_type" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "fault_image" varchar COLLATE "pg_catalog"."default",
  "run_image" varchar COLLATE "pg_catalog"."default",
  "stop_image" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Table structure for model_device_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_device_type" CASCADE;
CREATE TABLE "public"."model_device_type" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "fault_image" varchar COLLATE "pg_catalog"."default",
  "run_image" varchar COLLATE "pg_catalog"."default",
  "stop_image" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  CONSTRAINT "model_device_type_pkey" PRIMARY KEY ("id")
);


CREATE TRIGGER "upd_timestamp_model_device_type" BEFORE UPDATE ON "public"."model_device_type"
    FOR EACH ROW
    EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Triggers structure for table model_business_type
-- ----------------------------
CREATE TRIGGER "upd_timestamp_model_business_type" BEFORE UPDATE ON "public"."model_business_type"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table model_business_type
-- ----------------------------
ALTER TABLE "public"."model_business_type" ADD CONSTRAINT "model_business_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Table structure for model_attribute_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."model_attribute_type";
CREATE TABLE "public"."model_attribute_type" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Records of model_attribute_type
-- ----------------------------

-- ----------------------------
-- Triggers structure for table model_attribute_type
-- ----------------------------
CREATE TRIGGER "upd_timestamp_model_attribute_type" BEFORE UPDATE ON "public"."model_attribute_type"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

-- ----------------------------
-- Primary Key structure for table model_attribute_type
-- ----------------------------
ALTER TABLE "public"."model_attribute_type" ADD CONSTRAINT "model_attribute_type_pkey" PRIMARY KEY ("id");


-- ----------------------------
-- Table structure for broadcast
-- ----------------------------
DROP TABLE IF EXISTS "public"."broadcast" CASCADE;
CREATE TABLE "public"."broadcast" (
  "id" SERIAL NOT NULL,
  "group_code" varchar COLLATE "pg_catalog"."default",
  "group_name" varchar COLLATE "pg_catalog"."default",
  "mine_code" varchar COLLATE "pg_catalog"."default",
  "mining_area_id" int4,
  "mining_area_code" varchar COLLATE "pg_catalog"."default",
  "mine_area" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "location" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "ip" varchar COLLATE "pg_catalog"."default",
  "geometry_point" "public"."geometry",
  "geolocation_area_id" int4,
  "tunnel_guid" text COLLATE "pg_catalog"."default",
  "state" varchar COLLATE "pg_catalog"."default",
  "state_id" varchar COLLATE "pg_catalog"."default",
  "timestamp" timestamp(6),
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."broadcast"."id" IS '自增ID';
COMMENT ON COLUMN "public"."broadcast"."point_id" IS '广播编号';
COMMENT ON COLUMN "public"."broadcast"."name" IS '广播名称';
COMMENT ON COLUMN "public"."broadcast"."location" IS '安装位置名称';
COMMENT ON COLUMN "public"."broadcast"."ip" IS '广播IP地址';
COMMENT ON COLUMN "public"."broadcast"."state" IS '设备运行状态';
COMMENT ON COLUMN "public"."broadcast"."timestamp" IS '时间戳';
COMMENT ON COLUMN "public"."broadcast"."geometry_point" IS '坐标';
COMMENT ON COLUMN "public"."broadcast"."mining_area_id" IS '采区ID';
COMMENT ON COLUMN "public"."broadcast"."geolocation_area_id" IS '区域ID';
COMMENT ON COLUMN "public"."broadcast"."tunnel_guid" IS '巷道guid';
COMMENT ON COLUMN "public"."broadcast"."description" IS '描述';
COMMENT ON COLUMN "public"."broadcast"."system_id" IS '子系统编码';
COMMENT ON COLUMN "public"."broadcast"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."broadcast"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."broadcast"."mine_area" IS '区域名称';
COMMENT ON COLUMN "public"."broadcast"."state_id" IS '广播节点设备状态    0:在线 1:不在线  2:报警';
COMMENT ON COLUMN "public"."broadcast"."mining_area_code" IS '采区Code';
COMMENT ON COLUMN "public"."broadcast"."group_name" IS '设备所在分组名称';
COMMENT ON TABLE "public"."broadcast" IS '广播设备表';

-- ----------------------------
-- Records of broadcast
-- ----------------------------

-- ----------------------------
-- Table structure for broadcast_directive
-- ----------------------------
DROP TABLE IF EXISTS "public"."broadcast_directive" CASCADE;
CREATE TABLE "public"."broadcast_directive" (
  "id" SERIAL NOT NULL,
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "text" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "state" varchar COLLATE "pg_catalog"."default" DEFAULT '发送中'::character varying,
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6),
  "duration" varchar(255) COLLATE "pg_catalog"."default",
  "send_times" varchar COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."broadcast_directive"."id" IS '自增ID';
COMMENT ON COLUMN "public"."broadcast_directive"."point_id" IS '广播编号';
COMMENT ON COLUMN "public"."broadcast_directive"."text" IS '广播信息';
COMMENT ON COLUMN "public"."broadcast_directive"."state" IS '广播状态';
COMMENT ON COLUMN "public"."broadcast_directive"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."broadcast_directive"."updated_at" IS '更新时间';
COMMENT ON COLUMN "public"."broadcast_directive"."duration" IS '发送时长';
COMMENT ON COLUMN "public"."broadcast_directive"."send_times" IS '发送频次';
COMMENT ON COLUMN "public"."broadcast_directive"."name" IS '广播名称';
COMMENT ON TABLE "public"."broadcast_directive" IS '广播指令表';

-- ----------------------------
-- Records of broadcast_directive
-- ----------------------------

-- ----------------------------
-- Table structure for broadcast_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."broadcast_group" CASCADE;
CREATE TABLE "public"."broadcast_group" (
  "id" SERIAL NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."broadcast_group"."id" IS '自增ID';
COMMENT ON COLUMN "public"."broadcast_group"."name" IS '广播组名称';
COMMENT ON TABLE "public"."broadcast_group" IS '广播组表';

-- ----------------------------
-- Records of broadcast_group
-- ----------------------------

-- ----------------------------
-- Table structure for broadcast_map
-- ----------------------------
DROP TABLE IF EXISTS "public"."broadcast_map" CASCADE;
CREATE TABLE "public"."broadcast_map" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "boardcast_group_id" int4 NOT NULL,
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "public"."broadcast_map"."id" IS '主键ID = 广播组ID + 广播ID';
COMMENT ON COLUMN "public"."broadcast_map"."boardcast_group_id" IS '广播组ID';
COMMENT ON COLUMN "public"."broadcast_map"."point_id" IS '广播ID';
COMMENT ON TABLE "public"."broadcast_map" IS '广播映射表';

-- ----------------------------
-- Records of broadcast_map
-- ----------------------------

-- ----------------------------
-- Indexes structure for table broadcast
-- ----------------------------
CREATE INDEX "idx_broadcast_geometry_point" ON "public"."broadcast" USING gist (
  "geometry_point" "public"."gist_geometry_ops_2d"
);
CREATE UNIQUE INDEX "ix_broadcast_point_id" ON "public"."broadcast" USING btree (
  "point_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table broadcast
-- ----------------------------
CREATE TRIGGER "update_broadcast_geolocation_area_id" AFTER INSERT OR UPDATE OF "geometry_point" ON "public"."broadcast"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_point"();

-- ----------------------------
-- Primary Key structure for table broadcast
-- ----------------------------
ALTER TABLE "public"."broadcast" ADD CONSTRAINT "broadcast_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table broadcast_directive
-- ----------------------------
ALTER TABLE "public"."broadcast_directive" ADD CONSTRAINT "broadcast_directive_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table broadcast_group
-- ----------------------------
ALTER TABLE "public"."broadcast_group" ADD CONSTRAINT "broadcast_group_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table broadcast_group
-- ----------------------------
ALTER TABLE "public"."broadcast_group" ADD CONSTRAINT "broadcast_group_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table broadcast_map
-- ----------------------------
ALTER TABLE "public"."broadcast_map" ADD CONSTRAINT "broadcast_map_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table broadcast
-- ----------------------------
ALTER TABLE "public"."broadcast" ADD CONSTRAINT "broadcast_geolocation_area_id_fkey" FOREIGN KEY ("geolocation_area_id") REFERENCES "public"."geolocation_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "public"."broadcast" ADD CONSTRAINT "broadcast_mining_area_id_fkey" FOREIGN KEY ("mining_area_id") REFERENCES "public"."mining_area" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table broadcast_map
-- ----------------------------
ALTER TABLE "public"."broadcast_map" ADD CONSTRAINT "broadcast_map_boardcast_group_id_fkey" FOREIGN KEY ("boardcast_group_id") REFERENCES "public"."broadcast_group" ("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "public"."broadcast_map" ADD CONSTRAINT "broadcast_map_point_id_fkey" FOREIGN KEY ("point_id") REFERENCES "public"."broadcast" ("point_id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------------------------------------
-- 兴隆庄缺少补充 - 20220527
-- ----------------------------------------------------------

-- ----------------------------
-- Table structure for blade_modbus_client_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."blade_modbus_client_config";
CREATE TABLE "public"."blade_modbus_client_config" (
  "id" int8 NOT NULL,
  "scheme_id" int8,
  "scheme_code" varchar(255) COLLATE "pg_catalog"."default",
  "ip" varchar(255) COLLATE "pg_catalog"."default",
  "port" int4,
  "heartbeat_address" int4,
  "plc_commu_status" int4,
  "vfd_commu_address" int4,
  "vfd_commu_status" int4,
  "tenant_id" varchar(12) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_user" int8,
  "create_dept" int8,
  "create_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "update_user" int8,
  "update_time" timestamp(6) DEFAULT NULL::timestamp without time zone,
  "status" int4,
  "is_deleted" int4,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "heartbeat_station_num" int4,
  "vfd_commu_station_num" int4
)
;
COMMENT ON COLUMN "public"."blade_modbus_client_config"."id" IS '主键id';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."scheme_id" IS '方案id';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."scheme_code" IS '方案编码';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."ip" IS 'ip地址';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."port" IS '端口';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."heartbeat_address" IS 'PLC中心跳连接地址';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."plc_commu_status" IS 'PLC通讯状态（1正常， 0异常）';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."vfd_commu_address" IS 'PLC中存储变频器通讯状态的地址';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."vfd_commu_status" IS '变频器通讯状态（1 正常， 0异常）';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."create_user" IS '创建人';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."create_dept" IS '创建部门';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."update_user" IS '修改人';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."status" IS '状态';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."name" IS '客户端名称';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."heartbeat_station_num" IS '写心跳信息 站号';
COMMENT ON COLUMN "public"."blade_modbus_client_config"."vfd_commu_station_num" IS '变频器读通讯状态 站号';
COMMENT ON TABLE "public"."blade_modbus_client_config" IS 'modbus客户端配置';

-- ----------------------------
-- Records of blade_modbus_client_config
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table blade_modbus_client_config
-- ----------------------------
ALTER TABLE "public"."blade_modbus_client_config" ADD CONSTRAINT "blade_modbus_client_config_pkey" PRIMARY KEY ("id");

-- ----------------------------------------------------------
-- 新增液压支架阻力 - 20220515-route-v1.2.1.sql
-- ----------------------------------------------------------

-- ----------------------------
-- Table structure for bracket_stress_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."bracket_stress_definition";
CREATE TABLE "public"."bracket_stress_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_status" int4,
  "point_type" int4,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "sensor_type" varchar COLLATE "pg_catalog"."default",
  "point_monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "description" varchar COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "unit" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "station_type" varchar(255) COLLATE "pg_catalog"."default",
  "station_number" varchar COLLATE "pg_catalog"."default",
  "channel_no" varchar COLLATE "pg_catalog"."default",
  "decompression_value" varchar(255) COLLATE "pg_catalog"."default",
  "brace_value" varchar(255) COLLATE "pg_catalog"."default",
  "work_resistance_value" varchar(255) COLLATE "pg_catalog"."default",
  "alarm_upper_limit" varchar(255) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."bracket_stress_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."bracket_stress_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."bracket_stress_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."bracket_stress_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."bracket_stress_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."bracket_stress_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."bracket_stress_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."bracket_stress_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."bracket_stress_definition"."point_type" IS '测点类型:整形 0-模拟量 1-开关量 2- 状态量 3-累计量 4-文本量';
COMMENT ON COLUMN "public"."bracket_stress_definition"."data_type" IS '值类型:必填项,int/float/string';
COMMENT ON COLUMN "public"."bracket_stress_definition"."sensor_type" IS '传感器名称';
COMMENT ON COLUMN "public"."bracket_stress_definition"."point_monitor_type" IS '通道监测类型';
COMMENT ON COLUMN "public"."bracket_stress_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."bracket_stress_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."bracket_stress_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."bracket_stress_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."bracket_stress_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."bracket_stress_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."bracket_stress_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."bracket_stress_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."bracket_stress_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."bracket_stress_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."bracket_stress_definition"."station_type" IS '测站类型';
COMMENT ON COLUMN "public"."bracket_stress_definition"."station_number" IS '测站编号';
COMMENT ON COLUMN "public"."bracket_stress_definition"."channel_no" IS '数据使用通道';
COMMENT ON COLUMN "public"."bracket_stress_definition"."decompression_value" IS '泄压值';
COMMENT ON COLUMN "public"."bracket_stress_definition"."brace_value" IS '初撑力';
COMMENT ON COLUMN "public"."bracket_stress_definition"."work_resistance_value" IS '工作阻力';
COMMENT ON COLUMN "public"."bracket_stress_definition"."alarm_upper_limit" IS '报警上限';
COMMENT ON COLUMN "public"."bracket_stress_definition"."data_time" IS '数据采集时间格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."bracket_stress_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."bracket_stress_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."bracket_stress_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."bracket_stress_definition" IS '矿压-支架阻力';

-- ----------------------------
-- Primary Key structure for table bracket_stress_definition
-- ----------------------------
ALTER TABLE "public"."bracket_stress_definition" ADD CONSTRAINT "bracket_stress_definition_pkey" PRIMARY KEY ("point_id");

-- 新增钻孔应力

-- ----------------------------
-- Table structure for hole_stress_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."hole_stress_definition";
CREATE TABLE "public"."hole_stress_definition" (
  "point_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "point_name" varchar(50) COLLATE "pg_catalog"."default" NULL,
  "group_code" varchar(255) COLLATE "pg_catalog"."default",
  "mine_code" varchar(32) COLLATE "pg_catalog"."default",
  "monitoring_area_name" varchar(255) COLLATE "pg_catalog"."default",
  "monitoring_area_type" varchar(255) COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "monitor_type" varchar(255) COLLATE "pg_catalog"."default",
  "sensor_status" int4,
  "sensor_type" varchar(32) COLLATE "pg_catalog"."default",
  "point_type" int4,
  "data_type" varchar COLLATE "pg_catalog"."default",
  "point_relative_position" varchar(255) COLLATE "pg_catalog"."default",
  "location" varchar(255) COLLATE "pg_catalog"."default",
  "x" float8,
  "y" float8,
  "z" float8,
  "description" varchar COLLATE "pg_catalog"."default",
  "install_depth" varchar(255) COLLATE "pg_catalog"."default",
  "install_depth_unit" varchar(255) COLLATE "pg_catalog"."default",
  "install_time" timestamp(6),
  "unit" varchar COLLATE "pg_catalog"."default",
  "tunnel_name" varchar COLLATE "pg_catalog"."default",
  "station_type" varchar(255) COLLATE "pg_catalog"."default",
  "monitoring_direction" varchar(255) COLLATE "pg_catalog"."default",
  "initial_value" varchar(255) COLLATE "pg_catalog"."default",
  "data_time" timestamp(6) NOT NULL,
  "insert_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "update_db_time" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted_at" timestamp NULL
)
;
COMMENT ON COLUMN "public"."hole_stress_definition"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."hole_stress_definition"."point_name" IS '传感器名称';
COMMENT ON COLUMN "public"."hole_stress_definition"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."hole_stress_definition"."mine_code" IS '矿编码';
COMMENT ON COLUMN "public"."hole_stress_definition"."monitoring_area_name" IS '监测区名称';
COMMENT ON COLUMN "public"."hole_stress_definition"."monitoring_area_type" IS '监测区名称';
COMMENT ON COLUMN "public"."hole_stress_definition"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."hole_stress_definition"."system_name" IS '煤矿监测系统名称';
COMMENT ON COLUMN "public"."hole_stress_definition"."monitor_type" IS '监测系统型号';
COMMENT ON COLUMN "public"."hole_stress_definition"."sensor_status" IS '传感器类型（1 支架阻力、2 顶板离层、3 巷道位移、4 钻孔应力、5 锚杆索应力）';
COMMENT ON COLUMN "public"."hole_stress_definition"."sensor_type" IS '应力计类型';
COMMENT ON COLUMN "public"."hole_stress_definition"."point_type" IS '测点类型:整形 0-模拟量 1-开关量 2- 状态量 3-累计量 4-文本量';
COMMENT ON COLUMN "public"."hole_stress_definition"."data_type" IS '值类型:必填项,int/float/string';
COMMENT ON COLUMN "public"."hole_stress_definition"."point_relative_position" IS '相对位置描述';
COMMENT ON COLUMN "public"."hole_stress_definition"."location" IS '传感器位置';
COMMENT ON COLUMN "public"."hole_stress_definition"."x" IS 'x坐标';
COMMENT ON COLUMN "public"."hole_stress_definition"."y" IS 'y坐标';
COMMENT ON COLUMN "public"."hole_stress_definition"."z" IS 'z坐标';
COMMENT ON COLUMN "public"."hole_stress_definition"."description" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."hole_stress_definition"."install_depth" IS '安装深度';
COMMENT ON COLUMN "public"."hole_stress_definition"."install_depth_unit" IS '安装深度单位';
COMMENT ON COLUMN "public"."hole_stress_definition"."install_time" IS '安装时间';
COMMENT ON COLUMN "public"."hole_stress_definition"."unit" IS '单位';
COMMENT ON COLUMN "public"."hole_stress_definition"."tunnel_name" IS '巷道名称';
COMMENT ON COLUMN "public"."hole_stress_definition"."station_type" IS '测站类型';
COMMENT ON COLUMN "public"."hole_stress_definition"."monitoring_direction" IS '监测方向';
COMMENT ON COLUMN "public"."hole_stress_definition"."initial_value" IS '初始应力';
COMMENT ON COLUMN "public"."hole_stress_definition"."data_time" IS '数据采集时间格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN "public"."hole_stress_definition"."insert_db_time" IS '数据库插入时间,自动';
COMMENT ON COLUMN "public"."hole_stress_definition"."update_db_time" IS '数据库更新时间,自动';
COMMENT ON COLUMN "public"."hole_stress_definition"."deleted_at" IS '删除时间';
COMMENT ON TABLE "public"."hole_stress_definition" IS '矿压-钻孔应力';

-- ----------------------------
-- Primary Key structure for table hole_stress_definition
-- ----------------------------
ALTER TABLE "public"."hole_stress_definition" ADD CONSTRAINT "hole_stress_definition_pkey" PRIMARY KEY ("point_id");

-- ----------------------------
-- Table structure for mine_microseism_realtime
-- ----------------------------
DROP TABLE IF EXISTS "public"."mine_microseism_realtime" CASCADE;
CREATE TABLE "public"."mine_microseism_realtime"  (
  "id" BIGSERIAL NOT NULL,
  "timestamp" timestamp(6) NOT NULL,
  "mine_code" varchar(32) NULL,
  "group_code" varchar(32) NULL,
  "area_name" varchar(100) COLLATE "pg_catalog"."default" NULL DEFAULT NULL,
  "channel_count" int NULL DEFAULT NULL,
  "sample_length" int NULL DEFAULT NULL,
  "sample_req" int NULL DEFAULT NULL,
  "sensor_dir" varchar(5) COLLATE "pg_catalog"."default" NULL DEFAULT NULL,
  "install_mode" varchar(50) COLLATE "pg_catalog"."default" NULL DEFAULT NULL,
  "sensitivity" decimal(8, 2) NULL DEFAULT NULL,
  "sensor_type" varchar(50) COLLATE "pg_catalog"."default" NULL DEFAULT '',
  "centrum_x" decimal(16, 4) NULL DEFAULT NULL,
  "centrum_y" decimal(16, 4) NULL DEFAULT NULL,
  "centrum_z" decimal(16, 4) NULL DEFAULT NULL,
  "energy" decimal(8, 2) NULL DEFAULT NULL,
  "level" decimal(4, 2) NULL DEFAULT NULL,
  "pos_desc" varchar(250) COLLATE "pg_catalog"."default" NULL DEFAULT NULL,
  "max_swing" decimal(8, 2) NULL DEFAULT NULL,
  "avg_swing" decimal(8, 2) NULL DEFAULT NULL,
  "basic_freq" decimal(8, 2) NULL DEFAULT NULL,
  "trig_channel" varchar(50) COLLATE "pg_catalog"."default" NULL DEFAULT '',
  "state_text" text COLLATE "pg_catalog"."default" NULL,
  "memo" text COLLATE "pg_catalog"."default" NULL,
  "created_at" timestamp(6) DEFAULT now(),
  "updated_at" timestamp(6) DEFAULT now()
);
COMMENT ON TABLE "public"."mine_microseism_realtime" IS '矿震记录表';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."id" IS '自增ID';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."timestamp" IS '采集时刻';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."mine_code" IS '煤矿编码';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."group_code" IS '集团编码';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."area_name" IS '工作面名称';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."channel_count" IS '记录通道数';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."sample_length" IS '采样长度';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."sample_req" IS '采样频率';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."sensor_dir" IS '传感器方向';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."install_mode" IS '安装方式';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."sensitivity" IS '灵敏度';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."sensor_type" IS '传感器类型';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."centrum_x" IS '震源位置X';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."centrum_y" IS '震源位置Y';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."centrum_z" IS '震源位置Z';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."energy" IS '震源能量';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."level" IS '震源震级';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."pos_desc" IS '微震事件位置描述';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."max_swing" IS '最大振幅';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."avg_swing" IS '平均振幅';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."basic_freq" IS '波形频谱最大值';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."trig_channel" IS '矿震事件的通道号';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."state_text" IS '系统异常状态描述';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."memo" IS '备注';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."mine_microseism_realtime"."updated_at" IS '更新时间';

-- ----------------------------------------------------------
-- 预警报警指标批量配置规则保存表 - 20220517-ts-v1.2.4.sql
-- ----------------------------------------------------------

-- ----------------------------
-- Table structure for data_warn_rule_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."data_warn_rule_conf";
CREATE TABLE "public"."data_warn_rule_conf" (
  "point_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "indicator_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "indicator_title" varchar(255) COLLATE "pg_catalog"."default",
  "rule_id" varchar(255) COLLATE "pg_catalog"."default",
  "settig_type" varchar(255) COLLATE "pg_catalog"."default",
  "rules" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."data_warn_rule_conf"."point_id" IS '测点ID';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."indicator_id" IS '指标ID';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."indicator_title" IS '指标名称';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."rule_id" IS '模板规则ID';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."settig_type" IS '设定类型,single,multi';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."rules" IS '报警规则, JSON数组';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."data_warn_rule_conf"."updated_at" IS '更新时间';
COMMENT ON TABLE "public"."data_warn_rule_conf" IS '模板类型预警报警配置表';

-- ----------------------------
-- Primary Key structure for table data_warn_rule_conf
-- ----------------------------
ALTER TABLE "public"."data_warn_rule_conf" ADD CONSTRAINT "data_warn_rule_conf_pkey" PRIMARY KEY ("point_id", "indicator_id");

-- ----------------------------------------------------------
-- data_quick_query - 初始化脚本遗漏 20220713
-- ----------------------------------------------------------

-- 导出  表 public.data_point_temp 结构
DROP TABLE IF EXISTS "data_point_temp";
CREATE TABLE IF NOT EXISTS "data_point_temp" (
  "id" VARCHAR NOT NULL,
  "address" VARCHAR NULL DEFAULT NULL,
  "attribute_id" VARCHAR NULL DEFAULT NULL,
  "business_id" VARCHAR NULL DEFAULT NULL,
  "code" VARCHAR NULL DEFAULT NULL,
  "device_id" VARCHAR NULL DEFAULT NULL,
  "flag" INTEGER NULL DEFAULT NULL,
  "label" VARCHAR NULL DEFAULT NULL,
  "source_id" VARCHAR NULL DEFAULT NULL,
  "system_id" VARCHAR NULL DEFAULT NULL,
  "create_time" TIMESTAMPTZ NOT NULL DEFAULT 'now()',
  "update_time" TIMESTAMPTZ NOT NULL DEFAULT 'now()',
  "expression" TEXT NULL DEFAULT NULL,
  "device_location" VARCHAR NULL DEFAULT NULL,
  "name" VARCHAR NULL DEFAULT NULL,
  "data_type" VARCHAR NULL DEFAULT NULL,
  "point_id" VARCHAR NULL DEFAULT NULL,
  "temp" VARCHAR NULL DEFAULT NULL,
  PRIMARY KEY ("id")
);
ALTER TABLE "public"."data_point_temp" ADD CONSTRAINT "data_point_temp_code_key" UNIQUE ("code");

-- 导出  表 public.data_quick_query 结构
DROP TABLE IF EXISTS "data_quick_query";
CREATE TABLE IF NOT EXISTS "data_quick_query" (
  "id" SERIAL NOT NULL,
  "type" VARCHAR(32) NULL DEFAULT NULL,
  "point_id" VARCHAR NULL DEFAULT NULL,
  "system_name" VARCHAR(255) NULL DEFAULT NULL,
  "system_id" VARCHAR NULL DEFAULT NULL,
  "device_id" VARCHAR NULL DEFAULT NULL,
  "device_name" VARCHAR NULL DEFAULT NULL,
  "created_at" TIMESTAMPTZ NULL DEFAULT NULL,
  "updated_at" TIMESTAMPTZ NULL DEFAULT NULL,
  PRIMARY KEY ("id")
);
ALTER TABLE "public"."data_quick_query" ADD CONSTRAINT "data_quick_query_point_id_type_key" UNIQUE ("point_id", "type");

-- ----------------------------------------------------------
-- blade_conf_measure_point - 2.0 产品化缺失补充
-- ----------------------------------------------------------

-- public.blade_conf_measure_point definition
DROP TABLE IF EXISTS public."blade_conf_measure_point";
CREATE TABLE public.blade_conf_measure_point (
  id int8 NOT NULL, -- 主键
  out_point_id varchar(100) NULL, -- 输入的测点ID
  "name" varchar(100) NULL, -- 测点名称
  "type" int4 NULL, -- 测点类型(开关量，模拟量)
  control_type int4 NULL, -- 控制类型（风机，电源，等等..）
  url varchar(500) NULL, -- url
  create_user int8 NULL, -- 创建人
  create_time timestamp(6) NULL, -- 创建时间
  update_user int8 NULL, -- 更新人
  update_time timestamp(6) NULL, -- 更新时间
  status int4 NULL, -- 状态
  is_deleted int4 NULL, -- 是否删除
  "describe" varchar(200) NULL, -- 描述
  create_dept int8 NULL, -- 创建部门
  method_type varchar(50) NULL, -- 链接类型（GET,POST）
  param_json varchar(200) NULL, -- 参数json
  CONSTRAINT blade_conf_measure_point_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN public.blade_conf_measure_point.id IS '主键';
COMMENT ON COLUMN public.blade_conf_measure_point.out_point_id IS '输入的测点ID';
COMMENT ON COLUMN public.blade_conf_measure_point."name" IS '测点名称';
COMMENT ON COLUMN public.blade_conf_measure_point."type" IS '测点类型(开关量，模拟量)';
COMMENT ON COLUMN public.blade_conf_measure_point.control_type IS '控制类型（风机，电源，等等..）';
COMMENT ON COLUMN public.blade_conf_measure_point.url IS 'url';
COMMENT ON COLUMN public.blade_conf_measure_point.create_user IS '创建人';
COMMENT ON COLUMN public.blade_conf_measure_point.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_conf_measure_point.update_user IS '更新人';
COMMENT ON COLUMN public.blade_conf_measure_point.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_conf_measure_point.status IS '状态';
COMMENT ON COLUMN public.blade_conf_measure_point.is_deleted IS '是否删除';
COMMENT ON COLUMN public.blade_conf_measure_point."describe" IS '描述';
COMMENT ON COLUMN public.blade_conf_measure_point.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_conf_measure_point.method_type IS '链接类型（GET,POST）';
COMMENT ON COLUMN public.blade_conf_measure_point.param_json IS '参数json';

-- public.blade_command_issued_scheme definition
DROP TABLE IF EXISTS public."blade_command_issued_scheme";
CREATE TABLE public.blade_command_issued_scheme (
  id int8 NOT NULL, -- 主键id
  "name" varchar(255) NULL, -- 方案名称
  scheme_code varchar(255) NULL, -- 下发方案编码
  tenant_id varchar(12) NULL, -- 租户id
  create_user int8 NULL, -- 创建人
  create_dept int8 NULL, -- 创建部门
  create_time timestamp(6) NULL, -- 创建时间
  update_user int8 NULL, -- 修改人
  update_time timestamp(6) NULL, -- 修改时间
  status int4 NULL, -- 状态
  is_deleted int4 NULL, -- 是否已删除
  CONSTRAINT blade_command_issued_scheme_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_command_issued_scheme IS '命令下发方案';
COMMENT ON COLUMN public.blade_command_issued_scheme.id IS '主键id';
COMMENT ON COLUMN public.blade_command_issued_scheme."name" IS '方案名称';
COMMENT ON COLUMN public.blade_command_issued_scheme.scheme_code IS '下发方案编码';
COMMENT ON COLUMN public.blade_command_issued_scheme.tenant_id IS '租户id';
COMMENT ON COLUMN public.blade_command_issued_scheme.create_user IS '创建人';
COMMENT ON COLUMN public.blade_command_issued_scheme.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_command_issued_scheme.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_command_issued_scheme.update_user IS '修改人';
COMMENT ON COLUMN public.blade_command_issued_scheme.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_command_issued_scheme.status IS '状态';
COMMENT ON COLUMN public.blade_command_issued_scheme.is_deleted IS '是否已删除';

-- public.blade_conf_authorities definition
DROP TABLE IF EXISTS public."blade_conf_authorities";
CREATE TABLE public.blade_conf_authorities (
  id int8 NOT NULL, -- 主键
  point_id int8 NULL, -- 测点ID
  create_user int8 NULL, -- 创建人
  create_time timestamp(6) NULL, -- 创建时间
  update_user int8 NULL, -- 更新人
  update_time timestamp(6) NULL, -- 更新时间
  status int4 NULL, -- 状态
  is_deleted int4 NULL, -- 是否删除
  user_ids varchar(500) NULL, -- 用户id，逗号隔开
  create_dept int8 NULL, -- 创建部门
  CONSTRAINT blade_conf_authorities_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN public.blade_conf_authorities.id IS '主键';
COMMENT ON COLUMN public.blade_conf_authorities.point_id IS '测点ID';
COMMENT ON COLUMN public.blade_conf_authorities.create_user IS '创建人';
COMMENT ON COLUMN public.blade_conf_authorities.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_conf_authorities.update_user IS '更新人';
COMMENT ON COLUMN public.blade_conf_authorities.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_conf_authorities.status IS '状态';
COMMENT ON COLUMN public.blade_conf_authorities.is_deleted IS '是否删除';
COMMENT ON COLUMN public.blade_conf_authorities.user_ids IS '用户id，逗号隔开';
COMMENT ON COLUMN public.blade_conf_authorities.create_dept IS '创建部门';

-- public.blade_conf_black_list definition
DROP TABLE IF EXISTS public."blade_conf_black_list";
CREATE TABLE public.blade_conf_black_list (
  id int8 NOT NULL, -- 主键
  point_id int8 NULL, -- 测点ID
  create_user int8 NULL, -- 创建人
  create_time timestamp(6) NULL, -- 创建时间
  update_user int8 NULL, -- 更新人
  update_time timestamp(6) NULL, -- 更新时间
  status int4 NULL, -- 状态
  is_deleted int4 NULL, -- 是否删除
  create_dept int8 NULL, -- 创建部门
  CONSTRAINT blade_conf_black_list_pkey PRIMARY KEY (id)
);
COMMENT ON COLUMN public.blade_conf_black_list.id IS '主键';
COMMENT ON COLUMN public.blade_conf_black_list.point_id IS '测点ID';
COMMENT ON COLUMN public.blade_conf_black_list.create_user IS '创建人';
COMMENT ON COLUMN public.blade_conf_black_list.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_conf_black_list.update_user IS '更新人';
COMMENT ON COLUMN public.blade_conf_black_list.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_conf_black_list.status IS '状态';
COMMENT ON COLUMN public.blade_conf_black_list.is_deleted IS '是否删除';
COMMENT ON COLUMN public.blade_conf_black_list.create_dept IS '创建部门';

-- public.blade_conf_distribution_log definition
DROP TABLE IF EXISTS public."blade_conf_distribution_log";
CREATE TABLE public.blade_conf_distribution_log (
  id int8 NOT NULL, -- 主键
  tenant_id varchar(12) NULL, -- 租户ID
  create_user int8 NULL, -- 创建人（动作的发起人）
  create_dept int8 NULL, -- 创建部门
  create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
  update_user int8 NULL, -- 修改人
  update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
  status int4 NULL, -- 状态
  is_deleted int4 NULL, -- 是否已删除
  start_time_stamp timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 开始点击的时间
  end_time_stamp timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 调用API结束的完成时间
  elapsed_time int8 NULL, -- 总耗时（微妙数）
  action_result int4 NULL, -- 执行结果（0 失败， 1 成功  ......）
  ip varchar(255) NULL, -- ip
  port int4 NULL, -- 端口号
  address int4 NULL, -- 原始地址
  station_num int4 NULL, -- 站号
  scheme_id int8 NULL, -- 下发方案id
  scheme_code varchar(255) NULL, -- 下发方案code
  client_id int8 NULL, -- 客户端id
  client_name varchar(255) NULL, -- 客户端名称
  scheme_name varchar(255) NULL, -- 下发方案名称
  click_reach_elapsed int8 NULL, -- 请求点击-请求到达延迟（微妙数）
  business_elapsed int8 NULL, -- 业务处理延时（微妙数）
  invoke_issue_elapsed int8 NULL, -- 调用下发-下发执行完毕延迟（微妙数）
  point_id varchar(512) NULL, -- 点位id
  value varchar(255) NULL, -- 要修改为的值
  frequency varchar(512) NULL, -- 运行频率可为空
  CONSTRAINT conf_distribution_log_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_conf_distribution_log IS '组态下发日志';
COMMENT ON COLUMN public.blade_conf_distribution_log.id IS '主键';
COMMENT ON COLUMN public.blade_conf_distribution_log.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_conf_distribution_log.create_user IS '创建人（动作的发起人）';
COMMENT ON COLUMN public.blade_conf_distribution_log.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_conf_distribution_log.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_conf_distribution_log.update_user IS '修改人';
COMMENT ON COLUMN public.blade_conf_distribution_log.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_conf_distribution_log.status IS '状态';
COMMENT ON COLUMN public.blade_conf_distribution_log.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_conf_distribution_log.start_time_stamp IS '开始点击的时间';
COMMENT ON COLUMN public.blade_conf_distribution_log.end_time_stamp IS '调用API结束的完成时间';
COMMENT ON COLUMN public.blade_conf_distribution_log.elapsed_time IS '总耗时（微妙数）';
COMMENT ON COLUMN public.blade_conf_distribution_log.action_result IS '执行结果（0 失败， 1 成功  ......）';
COMMENT ON COLUMN public.blade_conf_distribution_log.ip IS 'ip';
COMMENT ON COLUMN public.blade_conf_distribution_log.port IS '端口号';
COMMENT ON COLUMN public.blade_conf_distribution_log.address IS '原始地址';
COMMENT ON COLUMN public.blade_conf_distribution_log.station_num IS '站号';
COMMENT ON COLUMN public.blade_conf_distribution_log.scheme_id IS '下发方案id';
COMMENT ON COLUMN public.blade_conf_distribution_log.scheme_code IS '下发方案code';
COMMENT ON COLUMN public.blade_conf_distribution_log.client_id IS '客户端id';
COMMENT ON COLUMN public.blade_conf_distribution_log.client_name IS '客户端名称';
COMMENT ON COLUMN public.blade_conf_distribution_log.scheme_name IS '下发方案名称';
COMMENT ON COLUMN public.blade_conf_distribution_log.click_reach_elapsed IS '请求点击-请求到达延迟（微妙数）';
COMMENT ON COLUMN public.blade_conf_distribution_log.business_elapsed IS '业务处理延时（微妙数）';
COMMENT ON COLUMN public.blade_conf_distribution_log.invoke_issue_elapsed IS '调用下发-下发执行完毕延迟（微妙数）';
COMMENT ON COLUMN public.blade_conf_distribution_log.point_id IS '点位id';
COMMENT ON COLUMN public.blade_conf_distribution_log.value IS '要修改为的值';
COMMENT ON COLUMN public.blade_conf_distribution_log.frequency IS '运行频率可为空';

-- topo_menu
DROP TABLE IF EXISTS public."topo_menu";
CREATE TABLE public.topo_menu (
  menu_id serial4 NOT NULL PRIMARY KEY,
  parent_id int4 NOT NULL DEFAULT 0,
  "type" char(1) NOT NULL DEFAULT 0,
  title varchar(100) NOT NULL,
  description text NULL,
  icon varchar(500) NULL,
  url varchar(500) NULL,
  topo_code varchar(100) NULL,
  sort int4 NOT NULL DEFAULT 1,
  created_at timestamp NOT NULL DEFAULT now(),
  updated_at timestamp NULL
);

COMMENT ON COLUMN public.topo_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN public.topo_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN public.topo_menu."type" IS '菜单类型，0目标，1菜单项';
COMMENT ON COLUMN public.topo_menu.title IS '显示标题';
COMMENT ON COLUMN public.topo_menu.description IS '描述';
COMMENT ON COLUMN public.topo_menu.icon IS '图标';
COMMENT ON COLUMN public.topo_menu.url IS '访问地址';
COMMENT ON COLUMN public.topo_menu.topo_code IS '拓扑图编码';
COMMENT ON COLUMN public.topo_menu.sort IS '排序';
COMMENT ON COLUMN public.topo_menu.created_at IS '新增时间';
COMMENT ON COLUMN public.topo_menu.updated_at IS '更新时间';
COMMENT ON TABLE public.topo_menu IS '拓扑菜单';

-- 辅助运输 柠条塔 南京北路
-- public.auxiliary_transportation_access_record definition
DROP TABLE IF EXISTS public."auxiliary_transportation_access_record";
CREATE TABLE public.auxiliary_transportation_access_record (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  sender_id varchar(100) NULL, -- 射频卡
  in_mine_time timestamp(6) NULL, -- 下井时间
  out_mine_time timestamp(6) NULL, -- 出井时间
  class_set_code varchar(100) NULL, -- 班次编号
  in_station_code varchar(100) NULL, -- 下井分站编码
  out_station_code varchar(100) NULL, -- 上井分站编码
  class_set_name varchar(100) NULL, -- 班次名称
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now() -- 数据库更新时间,自动
);
COMMENT ON TABLE public.auxiliary_transportation_access_record IS '车辆出入井记录信息表';

COMMENT ON COLUMN public.auxiliary_transportation_access_record.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.sender_id IS '射频卡';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.in_mine_time IS '下井时间';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.out_mine_time IS '出井时间';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.class_set_code IS '班次编号';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.in_station_code IS '下井分站编码';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.out_station_code IS '上井分站编码';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.class_set_name IS '班次名称';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_access_record.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_alarm_realtime definition
DROP TABLE IF EXISTS public."auxiliary_transportation_alarm_realtime";
CREATE TABLE public.auxiliary_transportation_alarm_realtime (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  sender_id varchar(100) NULL, -- 射频卡
  begin_time timestamp(6) NOT NULL, -- 异常开始时间,日期时间格式yyyy-MM-dd HH:mm:ss
  end_time timestamp(6) NULL, -- 异常结束时间,日期时间格式yyyy-MM-dd HH:mm:ss
  alarm_type_code varchar(5) NOT NULL, -- 异常类型code,字典值
  alarm_type_name varchar(20) NULL, -- 异常类型名称,字典值
  alarm_sense varchar(255) NULL, -- 多个传感器报警时，生成多条记录
  alarm_value float8 NULL, -- 报警值
  is_handle varchar(255) NULL, -- 是否处理
  handled_by varchar(255) NULL, -- 处理人
  processing_results varchar(255) NULL, -- 处理结果
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now() -- 数据库更新时间,自动
);
COMMENT ON TABLE public.auxiliary_transportation_alarm_realtime IS '车辆报警实时信息表';

COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.sender_id IS '射频卡';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.begin_time IS '异常开始时间,日期时间格式yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.end_time IS '异常结束时间,日期时间格式yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.alarm_type_code IS '异常类型code,字典值';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.alarm_type_name IS '异常类型名称,字典值';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.alarm_sense IS '多个传感器报警时，生成多条记录';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.alarm_value IS '报警值';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.is_handle IS '是否处理';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.handled_by IS '处理人';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.processing_results IS '处理结果';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_alarm_realtime.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_area_definition definition
DROP TABLE IF EXISTS public."auxiliary_transportation_area_definition";
CREATE TABLE public.auxiliary_transportation_area_definition (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(20) NULL, -- 集团名称
  area_type varchar(4) NOT NULL, -- 区域类型 4 个汉字长,井口区域、重点区域、限制区域、其它区域
  limit_person int4 NOT NULL, -- 区域核定人数,
  area_code varchar(100) NOT NULL, -- 区域编码,
  area_name varchar(100) NOT NULL, -- 区域名称,
  data_time timestamp(6) NOT NULL, -- 文件时间,格式为yyyy-MM-dd HH:mm:ss
  insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库更新时间,自动
  station_count varchar(255) NULL, -- 分站个数
  CONSTRAINT position_well_area_pkey_1 PRIMARY KEY (area_code)
);
COMMENT ON TABLE public.auxiliary_transportation_area_definition IS '辅助运输区域信息';

COMMENT ON COLUMN public.auxiliary_transportation_area_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.area_type IS '区域类型 4 个汉字长,井口区域、重点区域、限制区域、其它区域';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.limit_person IS '区域核定人数,';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.area_code IS '区域编码,';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.area_name IS '区域名称,';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.data_time IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.update_db_time IS '数据库更新时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_area_definition.station_count IS '分站个数';

-- public.auxiliary_transportation_car_definition definition
DROP TABLE IF EXISTS public."auxiliary_transportation_car_definition";
CREATE TABLE public.auxiliary_transportation_car_definition (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  sender_id varchar(100) NOT NULL, -- 射频卡
  car_number varchar(100) NULL, -- 车牌号
  car_type varchar(100) NULL, -- 车辆类型
  car_model varchar(100) NULL, -- 车牌型号
  car_department varchar(100) NULL, -- 车辆所属部门
  car_internal int4 NULL, -- 内外部车辆
  speed_limit_value float8 NULL, -- 限速值
  speed_limit_unit varchar(100) NULL, -- 限速值单位
  car_state varchar(100) NULL, -- 车辆状态
  car_explosion_proof_code varchar(100) NULL, -- 车辆防爆证编码
  car_manufacturer varchar(100) NULL, -- 车辆生产厂家
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  CONSTRAINT auxiliary_transportation_car_definition_pk PRIMARY KEY (sender_id)
);
COMMENT ON TABLE public.auxiliary_transportation_car_definition IS '车辆基础信息表';

COMMENT ON COLUMN public.auxiliary_transportation_car_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.sender_id IS '射频卡';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_number IS '车牌号';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_type IS '车辆类型';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_model IS '车牌型号';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_department IS '车辆所属部门';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_internal IS '内外部车辆';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.speed_limit_value IS '限速值';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.speed_limit_unit IS '限速值单位';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_state IS '车辆状态';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_explosion_proof_code IS '车辆防爆证编码';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.car_manufacturer IS '车辆生产厂家';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_car_definition.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_employee_definition definition
DROP TABLE IF EXISTS public."auxiliary_transportation_employee_definition";
CREATE TABLE public.auxiliary_transportation_employee_definition (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  sender_id varchar(100) NULL, -- 射频卡
  employee_name varchar(100) NULL, -- 人员姓名
  employee_code varchar(100) NULL, -- 人员工号
  work_type varchar(100) NULL, -- 职务/工种
  department varchar(100) NULL, -- 部门
  linger_max varchar(100) NULL, -- 井下作业标准时长
  postion_name varchar(100) NULL, -- 主要工作地点
  blood_type varchar(100) NULL, -- 血型
  gender varchar(100) NULL, -- 性别
  educational varchar(100) NULL, -- 学历
  identity_card varchar(100) NULL, -- 身份证号
  marry varchar(100) NULL, -- 婚姻状况
  telephone varchar(100) NULL, -- 联系电话
  mobilephone varchar(100) NULL, -- 手机号码
  address varchar(100) NULL, -- 家庭地址
  work_catalog varchar(100) NULL, -- 是否特殊工种
  certificate_name varchar(100) NULL, -- 工种证件名称
  certificate_valid_time timestamp(6) NULL, -- 工种证件有效日期
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now() -- 数据库更新时间,自动
);
COMMENT ON TABLE public.auxiliary_transportation_employee_definition IS '司机基础信息表';

COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.sender_id IS '射频卡';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.employee_name IS '人员姓名';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.employee_code IS '人员工号';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.work_type IS '职务/工种';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.department IS '部门';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.linger_max IS '井下作业标准时长';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.postion_name IS '主要工作地点';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.blood_type IS '血型';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.gender IS '性别';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.educational IS '学历';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.identity_card IS '身份证号';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.marry IS '婚姻状况';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.telephone IS '联系电话';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.mobilephone IS '手机号码';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.address IS '家庭地址';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.work_catalog IS '是否特殊工种';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.certificate_name IS '工种证件名称';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.certificate_valid_time IS '工种证件有效日期';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_employee_definition.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_realtime definition
DROP TABLE IF EXISTS public."auxiliary_transportation_realtime";
CREATE TABLE public.auxiliary_transportation_realtime (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  sender_id varchar(100) NOT NULL, -- 射频卡
  area_code varchar(100) NULL, -- 所在区域编码
  in_area_time timestamp(6) NULL, -- 进入区域时间
  station_code varchar(100) NULL, -- 所在分站编码
  in_station_time timestamp(6) NULL, -- 进入分站时间
  in_mine_time timestamp(6) NULL, -- 下井时间
  distance float8 NULL, -- 分站距离
  direction varchar(100) NULL, -- 行走方向
  real_speed float8 NULL, -- 速度
  x varchar(100) NULL, -- 坐标信息X
  y varchar(100) NULL, -- 坐标信息Y
  z varchar(100) NULL, -- 坐标信息Z
  water_temperature varchar(100) NULL, -- 水温
  oil_temperature varchar(100) NULL, -- 油温
  bearing_temperature varchar(100) NULL, -- 轴温
  revolution_speed varchar(100) NULL, -- 发动机转速
  mileage varchar(100) NULL, -- 累计里程
  run_time varchar(100) NULL, -- 启动以来运行时间
  driver_id varchar(100) NULL, -- 司机身份编码
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  station_name varchar(100) NULL, -- 所在分站名称
  area_name varchar(100) NULL, -- 所在区域名称
  data_time timestamp(6) NOT NULL, -- 文件时间,格式为yyyy-MM-dd HH:mm:ss
  CONSTRAINT auxiliary_transportation_realtime_pk PRIMARY KEY (sender_id)
);
COMMENT ON TABLE public.auxiliary_transportation_realtime IS '辅助运输实时轨迹表';

COMMENT ON COLUMN public.auxiliary_transportation_realtime.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.sender_id IS '射频卡';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.area_code IS '所在区域编码';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.in_area_time IS '进入区域时间';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.station_code IS '所在分站编码';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.in_station_time IS '进入分站时间';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.in_mine_time IS '下井时间';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.distance IS '分站距离';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.direction IS '行走方向';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.real_speed IS '速度';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.x IS '坐标信息X';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.y IS '坐标信息Y';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.z IS '坐标信息Z';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.water_temperature IS '水温';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.oil_temperature IS '油温';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.bearing_temperature IS '轴温';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.revolution_speed IS '发动机转速';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.mileage IS '累计里程';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.run_time IS '启动以来运行时间';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.driver_id IS '司机身份编码';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.update_db_time IS '数据库更新时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.station_name IS '所在分站名称';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.area_name IS '所在区域名称';
COMMENT ON COLUMN public.auxiliary_transportation_realtime.data_time IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';

-- public.auxiliary_transportation_station_definition definition
DROP TABLE IF EXISTS public."auxiliary_transportation_station_definition";
CREATE TABLE public.auxiliary_transportation_station_definition (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(100) NULL, -- 集团编码
  group_name varchar(100) NULL,
  station_code varchar(32) NOT NULL, -- 基站编码,关联分站表
  station_name varchar(100) NULL, -- 基站名称
  station_type varchar(100) NULL, -- 基站类型
  area_code varchar(100) NULL, -- 所属区域编码
  area_name varchar(100) NULL, -- 所属区域名称
  "location" varchar(100) NULL, -- 安装地点
  location_code varchar(100) NULL, -- 安装地点编号
  x varchar(100) NULL, -- X坐标
  y varchar(100) NULL, -- Y坐标
  z varchar(100) NULL, -- Z坐标
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  CONSTRAINT auxiliary_transportation_station_definition_pk PRIMARY KEY (station_code)
);
COMMENT ON TABLE public.auxiliary_transportation_station_definition IS '辅助运输分站信息表';

COMMENT ON COLUMN public.auxiliary_transportation_station_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.station_code IS '基站编码,关联分站表';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.station_name IS '基站名称';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.station_type IS '基站类型';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.area_code IS '所属区域编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.area_name IS '所属区域名称';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition."location" IS '安装地点';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.location_code IS '安装地点编号';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.x IS 'X坐标';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.y IS 'Y坐标';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.z IS 'Z坐标';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_station_definition.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_station_status definition
DROP TABLE IF EXISTS public."auxiliary_transportation_station_status";
CREATE TABLE public.auxiliary_transportation_station_status (
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  station_code varchar(100) NOT NULL, -- 基站编码
  state int4 NULL, -- 基站状态
  data_time timestamp(6) NOT NULL, -- 文件时间,格式为yyyy-MM-dd HH:mm:ss
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  CONSTRAINT auxiliary_transportation_station_status_pk PRIMARY KEY (station_code)
);
COMMENT ON TABLE public.auxiliary_transportation_station_status IS '分站状态信息表';

COMMENT ON COLUMN public.auxiliary_transportation_station_status.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.station_code IS '基站编码';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.state IS '基站状态';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.data_time IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_station_status.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_traffic_lights_definition definition
DROP TABLE IF EXISTS public."auxiliary_transportation_traffic_lights_definition";
CREATE TABLE public.auxiliary_transportation_traffic_lights_definition (
  mine_code varchar(24) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  device_code varchar(100) NOT NULL, -- 信号灯编号
  device_name varchar(100) NULL, -- 信号灯名称
  "position" varchar(100) NULL, -- 信号灯安装位置
  x varchar(100) NULL, -- X坐标
  y varchar(100) NULL, -- Y坐标
  z varchar(100) NULL, -- Z坐标
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  CONSTRAINT auxiliary_transportation_traffic_lights_definition_pk PRIMARY KEY (device_code)
);
COMMENT ON TABLE public.auxiliary_transportation_traffic_lights_definition IS '红绿灯基础信息表';

COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.device_code IS '信号灯编号';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.device_name IS '信号灯名称';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition."position" IS '信号灯安装位置';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.x IS 'X坐标';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.y IS 'Y坐标';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.z IS 'Z坐标';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_definition.update_db_time IS '数据库更新时间,自动';

-- public.auxiliary_transportation_traffic_lights_status definition
DROP TABLE IF EXISTS public."auxiliary_transportation_traffic_lights_status";
CREATE TABLE public.auxiliary_transportation_traffic_lights_status (
  mine_code varchar(24) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(100) NULL, -- 集团名称
  device_code varchar(100) NOT NULL, -- 信号灯编号
  state int4 NULL, -- 基站状态
  data_time timestamp(6) NOT NULL, -- 文件时间,格式为yyyy-MM-dd HH:mm:ss
  insert_db_time timestamp(6) NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT now(), -- 数据库更新时间,自动
  CONSTRAINT auxiliary_transportation_traffic_lights_status_pk PRIMARY KEY (device_code)
);
COMMENT ON TABLE public.auxiliary_transportation_traffic_lights_status IS '红绿灯状态信息表';

COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.group_code IS '集团编码';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.group_name IS '集团名称';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.device_code IS '信号灯编号';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.state IS '基站状态';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.data_time IS '文件时间,格式为yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.auxiliary_transportation_traffic_lights_status.update_db_time IS '数据库更新时间,自动';

-- 子系统在线监测记录

-- public.data_system_monitor definition
DROP TABLE IF EXISTS public.data_system_monitor;
CREATE TABLE public.data_system_monitor (
  id SERIAL NOT NULL PRIMARY KEY, -- 主键自增
  system_name varchar(255) NULL, -- 系统名称
  status int4 null, -- 状态,-- 0 正常 1 异常 2用来作删除
  last_upload_date timestamp(6) NULL, -- 最后上传时间
  system_code varchar(32) NULL, -- 系统名称_代码
  next_upload_date timestamp(6) NULL, -- 下次上传时间
  upload_status int4 NULL -- 是否上传文件的状态 0--未上传 1--已上传
);

COMMENT ON COLUMN public.data_system_monitor.id IS '主键自增';
COMMENT ON COLUMN public.data_system_monitor.system_name IS '系统名称';
COMMENT ON COLUMN public.data_system_monitor.status IS '状态, 0正常 1异常 2用来作删除';
COMMENT ON COLUMN public.data_system_monitor.last_upload_date IS '最后上传时间';
COMMENT ON COLUMN public.data_system_monitor.system_code IS '系统名称_代码';
COMMENT ON COLUMN public.data_system_monitor.next_upload_date IS '下次上传时间';
COMMENT ON COLUMN public.data_system_monitor.upload_status IS '是否上传文件的状态, 0未上传 1已上传';

ALTER TABLE public.data_system_monitor ADD CONSTRAINT data_system_monitor_uni_system_code UNIQUE (system_code);

-- 控制源管理

DROP TABLE IF EXISTS "public"."control_source";
CREATE TABLE "public"."control_source" (
  "id" serial4 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "pass_word" varchar(255) COLLATE "pg_catalog"."default",
  "ip" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "port" int8 NOT NULL,
  "security_policy" varchar(255) COLLATE "pg_catalog"."default",
  "business_type" varchar(255) COLLATE "pg_catalog"."default",
  "troubleshooting_suggestions" varchar(255) COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz(6) DEFAULT CURRENT_TIMESTAMP
)
;
COMMENT ON COLUMN "public"."control_source"."id" IS 'id';
COMMENT ON COLUMN "public"."control_source"."user_name" IS '用户名';
COMMENT ON COLUMN "public"."control_source"."pass_word" IS '密码';
COMMENT ON COLUMN "public"."control_source"."ip" IS 'IP';
COMMENT ON COLUMN "public"."control_source"."port" IS '端口';
COMMENT ON COLUMN "public"."control_source"."security_policy" IS '安全策略';
COMMENT ON COLUMN "public"."control_source"."business_type" IS '业务类型/消息中间件';
COMMENT ON COLUMN "public"."control_source"."troubleshooting_suggestions" IS '排查建议';
COMMENT ON COLUMN "public"."control_source"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."control_source"."updated_at" IS '更新时间';

CREATE TRIGGER "upd_timestamp_control_source" BEFORE UPDATE ON "public"."control_source"
FOR EACH ROW
EXECUTE PROCEDURE "public"."updated_timestamp"();

ALTER TABLE "public"."control_source" ADD CONSTRAINT "control_source_pk" PRIMARY KEY ("id");

-- 智能通风新增

-- public.anemometer_definition definition

DROP TABLE IF EXISTS public.anemometer_definition;
CREATE TABLE public.anemometer_definition (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  device_code varchar(32) NOT NULL, -- 装置编码
  device_name varchar(128) NOT NULL, -- 装置名称
  install_pos varchar(255) NULL, -- 安装位置
  wind_speed_unit varchar(8) NULL, -- 风速单位：m/s(-99.99时，异常)
  temperature_unit varchar(8) NULL, -- 温度单位：℃(大于80°时，异常)
  transverse_area_unit varchar(8) NULL, -- 横截面积单位：平方米
  wind_amount_unit varchar(8) NULL, -- 风量单位：立方米/分钟
  humidity_unit varchar(8) NULL, -- 湿度单位 %
  barometric_unit varchar(8) NULL, -- 气压单位 Pa
  memo text NULL, -- 设备描述
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at timestamp NULL, -- 删除时间
  CONSTRAINT pk_anemometer_definition PRIMARY KEY (device_code)
);
COMMENT ON TABLE public.anemometer_definition IS '精准测风系统定义表';

COMMENT ON COLUMN public.anemometer_definition.mine_code IS '矿编码';
COMMENT ON COLUMN public.anemometer_definition.mine_name IS '矿名称';
COMMENT ON COLUMN public.anemometer_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.anemometer_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.anemometer_definition.system_code IS '系统编码';
COMMENT ON COLUMN public.anemometer_definition.system_name IS '系统名称';
COMMENT ON COLUMN public.anemometer_definition.device_code IS '装置编码';
COMMENT ON COLUMN public.anemometer_definition.device_name IS '装置名称';
COMMENT ON COLUMN public.anemometer_definition.install_pos IS '安装位置';
COMMENT ON COLUMN public.anemometer_definition.wind_speed_unit IS '风速单位：m/s(-99.99时，异常)';
COMMENT ON COLUMN public.anemometer_definition.temperature_unit IS '温度单位：℃(大于80°时，异常)';
COMMENT ON COLUMN public.anemometer_definition.transverse_area_unit IS '横截面积单位：平方米';
COMMENT ON COLUMN public.anemometer_definition.wind_amount_unit IS '风量单位：立方米/分钟';
COMMENT ON COLUMN public.anemometer_definition.humidity_unit IS '湿度单位 %';
COMMENT ON COLUMN public.anemometer_definition.barometric_unit IS '气压单位 Pa';
COMMENT ON COLUMN public.anemometer_definition.memo IS '设备描述';
COMMENT ON COLUMN public.anemometer_definition.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.anemometer_definition.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.anemometer_definition.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.anemometer_definition.created_at IS '插入时间';
COMMENT ON COLUMN public.anemometer_definition.updated_at IS '更新时间';
COMMENT ON COLUMN public.anemometer_definition.deleted_at IS '删除时间';

-- public.anemometer_realtime definition

DROP TABLE IF EXISTS public.anemometer_realtime;
CREATE TABLE public.anemometer_realtime (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  device_code varchar(32) NOT NULL, -- 装置编码
  wind_speed numeric(10, 2) NULL, -- 风速
  temperature numeric(10, 2) NULL, -- 温度
  transverse_area numeric(10, 2) NULL, -- 横截面积
  wind_amount numeric(10, 2) NULL, -- 风量
  humidity numeric(10, 2) NULL, -- 湿度
  barometric numeric(10, 2) NULL, -- 气压
  online_status varchar(8) NULL, -- 在线状态
  online_status_text varchar(64) NULL, -- 在线状态描述
  memo text NULL, -- 设备描述
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  CONSTRAINT pk_anemometer_realtime PRIMARY KEY (device_code)
);
COMMENT ON TABLE public.anemometer_realtime IS '精准测风系统定义表';

COMMENT ON COLUMN public.anemometer_realtime.mine_code IS '矿编码';
COMMENT ON COLUMN public.anemometer_realtime.mine_name IS '矿名称';
COMMENT ON COLUMN public.anemometer_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.anemometer_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.anemometer_realtime.system_code IS '系统编码';
COMMENT ON COLUMN public.anemometer_realtime.system_name IS '系统名称';
COMMENT ON COLUMN public.anemometer_realtime.device_code IS '装置编码';
COMMENT ON COLUMN public.anemometer_realtime.wind_speed IS '风速';
COMMENT ON COLUMN public.anemometer_realtime.temperature IS '温度';
COMMENT ON COLUMN public.anemometer_realtime.transverse_area IS '横截面积';
COMMENT ON COLUMN public.anemometer_realtime.wind_amount IS '风量';
COMMENT ON COLUMN public.anemometer_realtime.humidity IS '湿度';
COMMENT ON COLUMN public.anemometer_realtime.barometric IS '气压';
COMMENT ON COLUMN public.anemometer_realtime.online_status IS '在线状态';
COMMENT ON COLUMN public.anemometer_realtime.online_status_text IS '在线状态描述';
COMMENT ON COLUMN public.anemometer_realtime.memo IS '设备描述';
COMMENT ON COLUMN public.anemometer_realtime.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.anemometer_realtime.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.anemometer_realtime.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.anemometer_realtime.created_at IS '插入时间';
COMMENT ON COLUMN public.anemometer_realtime.updated_at IS '更新时间';

-- public.charging_cabinet_definition definition

DROP TABLE IF EXISTS public.charging_cabinet_definition;
CREATE TABLE public.charging_cabinet_definition (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  point_id varchar(128) NOT NULL, -- 充电柜称号.柜门号
  stand_code varchar(64) NOT NULL, -- 智能充电柜系统号
  door_code varchar(32) NOT NULL, -- 柜门号
  person_card varchar(32) NULL, -- 员工号
  key_card_code varchar(32) NULL, -- 钥匙卡号
  lamp_equip_time timestamp NULL, -- 矿灯配备时间
  lamp_charge_times int8 NULL, -- 矿灯充电次数
  locate_card varchar(32) NULL, -- 定位号
  "name" varchar(255) NULL, -- 名字
  department varchar(255) NULL, -- 部门
  work_kind varchar(255) NULL, -- 工种
  id_card varchar(32) NULL, -- 身份证号
  profile_photo text NULL, -- 个人照片
  bg_photo text NULL, -- 背景照片
  memo text NULL, -- 设备描述
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at timestamp NULL, -- 删除时间
  CONSTRAINT pk_charging_cabinet_definition PRIMARY KEY (point_id)
);
COMMENT ON TABLE public.charging_cabinet_definition IS '智能充电柜系统定义表';

COMMENT ON COLUMN public.charging_cabinet_definition.mine_code IS '矿编码';
COMMENT ON COLUMN public.charging_cabinet_definition.mine_name IS '矿名称';
COMMENT ON COLUMN public.charging_cabinet_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.charging_cabinet_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.charging_cabinet_definition.system_code IS '系统编码';
COMMENT ON COLUMN public.charging_cabinet_definition.system_name IS '系统名称';
COMMENT ON COLUMN public.charging_cabinet_definition.point_id IS '充电柜称号.柜门号';
COMMENT ON COLUMN public.charging_cabinet_definition.stand_code IS '智能充电柜系统号';
COMMENT ON COLUMN public.charging_cabinet_definition.door_code IS '柜门号';
COMMENT ON COLUMN public.charging_cabinet_definition.person_card IS '员工号';
COMMENT ON COLUMN public.charging_cabinet_definition.key_card_code IS '钥匙卡号';
COMMENT ON COLUMN public.charging_cabinet_definition.lamp_equip_time IS '矿灯配备时间';
COMMENT ON COLUMN public.charging_cabinet_definition.lamp_charge_times IS '矿灯充电次数';
COMMENT ON COLUMN public.charging_cabinet_definition.locate_card IS '定位号';
COMMENT ON COLUMN public.charging_cabinet_definition."name" IS '名字';
COMMENT ON COLUMN public.charging_cabinet_definition.department IS '部门';
COMMENT ON COLUMN public.charging_cabinet_definition.work_kind IS '工种';
COMMENT ON COLUMN public.charging_cabinet_definition.id_card IS '身份证号';
COMMENT ON COLUMN public.charging_cabinet_definition.profile_photo IS '个人照片';
COMMENT ON COLUMN public.charging_cabinet_definition.bg_photo IS '背景照片';
COMMENT ON COLUMN public.charging_cabinet_definition.memo IS '设备描述';
COMMENT ON COLUMN public.charging_cabinet_definition.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.charging_cabinet_definition.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.charging_cabinet_definition.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.charging_cabinet_definition.created_at IS '插入时间';
COMMENT ON COLUMN public.charging_cabinet_definition.updated_at IS '更新时间';
COMMENT ON COLUMN public.charging_cabinet_definition.deleted_at IS '删除时间';

-- public.charging_cabinet_realtime definition

DROP TABLE IF EXISTS public.charging_cabinet_realtime;
CREATE TABLE public.charging_cabinet_realtime (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  point_id varchar(128) NOT NULL, -- 充电柜称号.柜门号
  stand_code varchar(64) NOT NULL, -- 充电柜称号
  door_code varchar(32) NOT NULL, -- 柜门号
  person_card varchar(32) NULL, -- 员工号
  door_state varchar(8) NULL, -- 门的状态 0：门关闭，1：门打开，2：通信故障
  door_state_text varchar(64) NULL, -- 门的状态描述
  lamp_state varchar(8) NULL, -- 灯的状态 0：下架，1：充电中，2：充满，3：充电故障
  lamp_state_text varchar(64) NULL, -- 灯的状态描述
  lamp_on_time timestamp NULL, -- 矿灯上架时间
  lamp_off_time timestamp NULL, -- 矿灯下架时间
  lamp_change_time timestamp NULL, -- 状态变化时间   0、1、2、3状态变化的时间
  self_rescuer_state varchar(8) NULL, -- 自救器状态  0：离架，1：在架
  self_rescuer_state_text varchar(64) NULL, -- 自救器状态描述
  charge_count int8 NOT NULL DEFAULT 0, -- 充电次数
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  CONSTRAINT pk_charging_cabinet_realtime PRIMARY KEY (point_id)
);
COMMENT ON TABLE public.charging_cabinet_realtime IS '智能充电柜系统定义表';

COMMENT ON COLUMN public.charging_cabinet_realtime.mine_code IS '矿编码';
COMMENT ON COLUMN public.charging_cabinet_realtime.mine_name IS '矿名称';
COMMENT ON COLUMN public.charging_cabinet_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.charging_cabinet_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.charging_cabinet_realtime.system_code IS '系统编码';
COMMENT ON COLUMN public.charging_cabinet_realtime.system_name IS '系统名称';
COMMENT ON COLUMN public.charging_cabinet_realtime.point_id IS '充电柜称号.柜门号';
COMMENT ON COLUMN public.charging_cabinet_realtime.stand_code IS '充电柜称号';
COMMENT ON COLUMN public.charging_cabinet_realtime.door_code IS '柜门号';
COMMENT ON COLUMN public.charging_cabinet_realtime.person_card IS '员工号';
COMMENT ON COLUMN public.charging_cabinet_realtime.door_state IS '门的状态 0：门关闭，1：门打开，2：通信故障';
COMMENT ON COLUMN public.charging_cabinet_realtime.door_state_text IS '门的状态描述';
COMMENT ON COLUMN public.charging_cabinet_realtime.lamp_state IS '灯的状态 0：下架，1：充电中，2：充满，3：充电故障';
COMMENT ON COLUMN public.charging_cabinet_realtime.lamp_state_text IS '灯的状态描述';
COMMENT ON COLUMN public.charging_cabinet_realtime.lamp_on_time IS '矿灯上架时间';
COMMENT ON COLUMN public.charging_cabinet_realtime.lamp_off_time IS '矿灯下架时间';
COMMENT ON COLUMN public.charging_cabinet_realtime.lamp_change_time IS '状态变化时间   0、1、2、3状态变化的时间';
COMMENT ON COLUMN public.charging_cabinet_realtime.self_rescuer_state IS '自救器状态  0：离架，1：在架';
COMMENT ON COLUMN public.charging_cabinet_realtime.self_rescuer_state_text IS '自救器状态描述';
COMMENT ON COLUMN public.charging_cabinet_realtime.charge_count IS '充电次数';
COMMENT ON COLUMN public.charging_cabinet_realtime.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.charging_cabinet_realtime.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.charging_cabinet_realtime.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.charging_cabinet_realtime.created_at IS '插入时间';
COMMENT ON COLUMN public.charging_cabinet_realtime.updated_at IS '更新时间';

-- public.workface_spray_definition definition

DROP TABLE IF EXISTS public.workface_spray_definition;
CREATE TABLE public.workface_spray_definition (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  point_id varchar(64) NOT NULL, -- 设备编码
  device_id varchar(64) NULL, -- 设备ID
  device_name varchar(128) NOT NULL, -- 设备名称
  device_ip varchar(128) NULL, -- 设备IP
  data_type varchar(100) NULL, -- 数据类型, int float
  memo text NULL, -- 设备描述
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at timestamp NULL, -- 删除时间
  CONSTRAINT workface_spray_definition_pkey_1 PRIMARY KEY (point_id)
);
COMMENT ON TABLE public.workface_spray_definition IS '工作面喷雾定义表';

COMMENT ON COLUMN public.workface_spray_definition.mine_code IS '矿编码';
COMMENT ON COLUMN public.workface_spray_definition.mine_name IS '矿名称';
COMMENT ON COLUMN public.workface_spray_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.workface_spray_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.workface_spray_definition.system_code IS '系统编码';
COMMENT ON COLUMN public.workface_spray_definition.system_name IS '系统名称';
COMMENT ON COLUMN public.workface_spray_definition.point_id IS '设备编码';
COMMENT ON COLUMN public.workface_spray_definition.device_id IS '设备ID';
COMMENT ON COLUMN public.workface_spray_definition.device_name IS '设备名称';
COMMENT ON COLUMN public.workface_spray_definition.device_ip IS '设备IP';
COMMENT ON COLUMN public.workface_spray_definition.data_type IS '数据类型, int float';
COMMENT ON COLUMN public.workface_spray_definition.memo IS '设备描述';
COMMENT ON COLUMN public.workface_spray_definition.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.workface_spray_definition.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.workface_spray_definition.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.workface_spray_definition.created_at IS '插入时间';
COMMENT ON COLUMN public.workface_spray_definition.updated_at IS '更新时间';
COMMENT ON COLUMN public.workface_spray_definition.deleted_at IS '删除时间';

-- public.workface_spray_realtime definition

DROP TABLE IF EXISTS public.workface_spray_realtime;
CREATE TABLE public.workface_spray_realtime (
  mine_code varchar(32) NULL, -- 矿编码
  mine_name varchar(100) NULL, -- 矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(50) NULL, -- 集团名称
  system_code varchar(6) NULL, -- 系统编码
  system_name varchar(20) NULL, -- 系统名称
  point_id varchar(64) NOT NULL, -- 设备编码
  work_mode varchar(8) NULL, -- 工作模式 0000:自动, 0001:闭锁, 0002:手动
  work_mode_text varchar(16) NULL, -- 工作模式描述
  connect_mode varchar(8) NULL, -- 通讯模式, 0000:RS485通讯, 0001:WIFI通讯, 0002:以太网通讯
  connect_mode_text varchar(16) NULL, -- 通讯模式描述
  connect_status varchar(8) NULL, -- 设备连接状态, 0000:未连接, 0001:已连接
  connect_status_text varchar(16) NULL, -- 设备连接状态描述
  water_status varchar(8) NULL, -- 喷雾状态, FF00:开启, 0000:关闭
  water_status_text varchar(16) NULL, -- 喷雾状态描述
  air_status varchar(8) NULL, -- 排污状态, FF00:开启, 0000:关闭
  air_status_text varchar(16) NULL, -- 排污状态描述
  temperature numeric(12, 4) NULL, -- 温度值
  humidity numeric(12, 4) NULL, -- 湿度值
  dust_concentration numeric(12, 4) NULL, -- 粉尘浓度
  water_pressure numeric(12, 4) NULL, -- 水压值
  air_pressure numeric(12, 4) NULL, -- 气压值
  holder_count int8 NULL, -- 支架主机数量
  holder_status varchar(256) NULL, -- 支架状态-二进制, 0:关闭, 1:开启
  holder_delayed int8 NULL, -- 支架延时
  holder_link_count int8 NULL, -- 支架联动数量
  holder_location varchar(8) NULL, -- 主机所在支架的位置
  app_scene varchar(8) NULL, -- 应用场景, 0000:巷道喷雾, 0001:综采喷雾, 0002:放炮喷雾, 0003:风水联动; 0004:水汽分配
  app_scene_text varchar(32) NULL, -- 应用场景描述
  data_time timestamp(6) NOT NULL, -- 数据时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_time timestamp(6) NOT NULL, -- 采集时间, 推送格式 yyyy-MM-dd HH:mm:ss
  collect_status int2 NULL DEFAULT 100, -- 采集状态, 100正常, 90自动结束
  created_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 插入时间
  updated_at timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  CONSTRAINT workface_spray_realtime_pkey_1 PRIMARY KEY (point_id)
);
COMMENT ON TABLE public.workface_spray_realtime IS '工作面喷雾实时表';

COMMENT ON COLUMN public.workface_spray_realtime.mine_code IS '矿编码';
COMMENT ON COLUMN public.workface_spray_realtime.mine_name IS '矿名称';
COMMENT ON COLUMN public.workface_spray_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.workface_spray_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.workface_spray_realtime.system_code IS '系统编码';
COMMENT ON COLUMN public.workface_spray_realtime.system_name IS '系统名称';
COMMENT ON COLUMN public.workface_spray_realtime.point_id IS '设备编码';
COMMENT ON COLUMN public.workface_spray_realtime.work_mode IS '工作模式 0000:自动, 0001:闭锁, 0002:手动';
COMMENT ON COLUMN public.workface_spray_realtime.work_mode_text IS '工作模式描述';
COMMENT ON COLUMN public.workface_spray_realtime.connect_mode IS '通讯模式, 0000:RS485通讯, 0001:WIFI通讯, 0002:以太网通讯';
COMMENT ON COLUMN public.workface_spray_realtime.connect_mode_text IS '通讯模式描述';
COMMENT ON COLUMN public.workface_spray_realtime.connect_status IS '设备连接状态, 0000:未连接, 0001:已连接';
COMMENT ON COLUMN public.workface_spray_realtime.connect_status_text IS '设备连接状态描述';
COMMENT ON COLUMN public.workface_spray_realtime.water_status IS '喷雾状态, FF00:开启, 0000:关闭';
COMMENT ON COLUMN public.workface_spray_realtime.water_status_text IS '喷雾状态描述';
COMMENT ON COLUMN public.workface_spray_realtime.air_status IS '排污状态, FF00:开启, 0000:关闭';
COMMENT ON COLUMN public.workface_spray_realtime.air_status_text IS '排污状态描述';
COMMENT ON COLUMN public.workface_spray_realtime.temperature IS '温度值';
COMMENT ON COLUMN public.workface_spray_realtime.humidity IS '湿度值';
COMMENT ON COLUMN public.workface_spray_realtime.dust_concentration IS '粉尘浓度';
COMMENT ON COLUMN public.workface_spray_realtime.water_pressure IS '水压值';
COMMENT ON COLUMN public.workface_spray_realtime.air_pressure IS '气压值';
COMMENT ON COLUMN public.workface_spray_realtime.holder_count IS '支架主机数量';
COMMENT ON COLUMN public.workface_spray_realtime.holder_status IS '支架状态-二进制, 0:关闭, 1:开启';
COMMENT ON COLUMN public.workface_spray_realtime.holder_delayed IS '支架延时';
COMMENT ON COLUMN public.workface_spray_realtime.holder_link_count IS '支架联动数量';
COMMENT ON COLUMN public.workface_spray_realtime.holder_location IS '主机所在支架的位置';
COMMENT ON COLUMN public.workface_spray_realtime.app_scene IS '应用场景, 0000:巷道喷雾, 0001:综采喷雾, 0002:放炮喷雾, 0003:风水联动; 0004:水汽分配';
COMMENT ON COLUMN public.workface_spray_realtime.app_scene_text IS '应用场景描述';
COMMENT ON COLUMN public.workface_spray_realtime.data_time IS '数据时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.workface_spray_realtime.collect_time IS '采集时间, 推送格式 yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN public.workface_spray_realtime.collect_status IS '采集状态, 100正常, 90自动结束';
COMMENT ON COLUMN public.workface_spray_realtime.created_at IS '插入时间';
COMMENT ON COLUMN public.workface_spray_realtime.updated_at IS '更新时间';

-- public.fiber_temperature_definition definition
DROP TABLE IF EXISTS public.fiber_temperature_definition;
CREATE TABLE public.fiber_temperature_definition (
    mine_code varchar(32) NULL, -- 矿编码,矿编码
    mine_name varchar(100) NULL, -- 矿名称,矿名称
    group_code varchar(20) NULL, -- 集团编码
    group_name varchar(20) NULL, -- 集团名称
    section_code varchar(32) NOT NULL, -- 分区编码
    section_name varchar(255) NOT NULL, -- 分区名称
    fiber_start int4 NULL, -- 光缆起点
    fiber_end int4 NULL, -- 光缆终点
    fiber_length int4 NULL, -- 光缆长度
    change_length int4 NULL, -- 通道长度变化，配合channel_upflag使用
    orange_warn_limit float8 NULL, -- 橙色警报线
    red_warn_limit float8 NULL, -- 红色警报线
    substation_code varchar(32) NULL, -- 分站编码
    substation_name varchar(100) NOT NULL, -- 分站名称
    substation_ip varchar(100) NULL, -- 分站ip地址
    substation_location varchar(100) NULL, -- 分站安装位置
    substation_state varchar(8) NULL, -- 分站连接状态，0正常，-1断开
    channel_code varchar(32) NULL, -- 通道编码
    channel_name varchar(100) NOT NULL, -- 通道名称
    channel_upflag varchar(8) NULL, -- 通道更新状态；0 长度正常，-1长度减少，1长度增加
    data_time timestamp(6) NOT NULL, -- 数据变化时间
    collect_time timestamp(6) NOT NULL, -- 数据采集时间
    insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
    update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP -- 数据库更新时间,自动
);
COMMENT ON TABLE public.fiber_temperature_definition IS '测温分区定义表';
COMMENT ON COLUMN public.fiber_temperature_definition.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.fiber_temperature_definition.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.fiber_temperature_definition.group_code IS '集团编码';
COMMENT ON COLUMN public.fiber_temperature_definition.group_name IS '集团名称';
COMMENT ON COLUMN public.fiber_temperature_definition.section_code IS '分区编码';
COMMENT ON COLUMN public.fiber_temperature_definition.section_name IS '分区名称';
COMMENT ON COLUMN public.fiber_temperature_definition.fiber_start IS '光缆起点';
COMMENT ON COLUMN public.fiber_temperature_definition.fiber_end IS '光缆终点';
COMMENT ON COLUMN public.fiber_temperature_definition.fiber_length IS '光缆长度';
COMMENT ON COLUMN public.fiber_temperature_definition.change_length IS '通道长度变化，配合channel_upflag使用';
COMMENT ON COLUMN public.fiber_temperature_definition.orange_warn_limit IS '橙色警报线';
COMMENT ON COLUMN public.fiber_temperature_definition.red_warn_limit IS '红色警报线';
COMMENT ON COLUMN public.fiber_temperature_definition.substation_code IS '分站编码';
COMMENT ON COLUMN public.fiber_temperature_definition.substation_name IS '分站名称';
COMMENT ON COLUMN public.fiber_temperature_definition.substation_ip IS '分站ip地址';
COMMENT ON COLUMN public.fiber_temperature_definition.substation_location IS '分站安装位置';
COMMENT ON COLUMN public.fiber_temperature_definition.substation_state IS '分站连接状态，0正常，-1断开';
COMMENT ON COLUMN public.fiber_temperature_definition.channel_code IS '通道编码';
COMMENT ON COLUMN public.fiber_temperature_definition.channel_name IS '通道名称';
COMMENT ON COLUMN public.fiber_temperature_definition.channel_upflag IS '通道更新状态；0 长度正常，-1长度减少，1长度增加';
COMMENT ON COLUMN public.fiber_temperature_definition.data_time IS '数据变化时间';
COMMENT ON COLUMN public.fiber_temperature_definition.collect_time IS '数据采集时间';
COMMENT ON COLUMN public.fiber_temperature_definition.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.fiber_temperature_definition.update_db_time IS '数据库更新时间,自动';

-- public.fiber_temperature_realtime definition
DROP TABLE IF EXISTS public.fiber_temperature_realtime;
CREATE TABLE public.fiber_temperature_realtime (
    mine_code varchar(32) NULL, -- 矿编码
    mine_name varchar(100) NULL, -- 矿名称
    group_code varchar(20) NULL, -- 集团编码
    group_name varchar(20) NULL, -- 集团名称
    section_code varchar(32) NOT NULL, -- 分区编码
    temp_str text NULL, -- 温度字符串
    temp_max float8 NULL, -- 分区最高温度
    temp_min float8 NULL, -- 分区最低温度
    temp_avg float8 NULL, -- 分区平均温度
    temp_max_position float8 NULL, -- 分区最高温度位置
    temp_min_position float8 NULL, -- 分区最低温度位置
    alarm_flag varchar(8) NULL, -- 报警状态;0 正常，1 橙色报警，2 红色报警
    alarm_flag_text varchar(100) NULL, -- 报警状态解释字段
    data_time timestamp(6) NOT NULL DEFAULT now(), -- 数据变化时间
    collect_time timestamp(6) NULL, -- 数据采集时间
    insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
    update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP -- 数据库更新时间,自动
);
COMMENT ON TABLE public.fiber_temperature_realtime IS '分区温度表';
COMMENT ON COLUMN public.fiber_temperature_realtime.mine_code IS '矿编码';
COMMENT ON COLUMN public.fiber_temperature_realtime.mine_name IS '矿名称';
COMMENT ON COLUMN public.fiber_temperature_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.fiber_temperature_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.fiber_temperature_realtime.section_code IS '分区编码';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_str IS '温度字符串';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_max IS '分区最高温度';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_min IS '分区最低温度';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_avg IS '分区平均温度';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_max_position IS '分区最高温度位置';
COMMENT ON COLUMN public.fiber_temperature_realtime.temp_min_position IS '分区最低温度位置';
COMMENT ON COLUMN public.fiber_temperature_realtime.alarm_flag IS '报警状态;0 正常，1 橙色报警，2 红色报警';
COMMENT ON COLUMN public.fiber_temperature_realtime.alarm_flag_text IS '报警状态解释字段';
COMMENT ON COLUMN public.fiber_temperature_realtime.data_time IS '数据变化时间';
COMMENT ON COLUMN public.fiber_temperature_realtime.collect_time IS '数据采集时间';
COMMENT ON COLUMN public.fiber_temperature_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.fiber_temperature_realtime.update_db_time IS '数据库更新时间,自动';

-- public.fiber_temperature_alarm_realtime definition
DROP TABLE IF EXISTS public.fiber_temperature_alarm_realtime;
CREATE TABLE public.fiber_temperature_alarm_realtime (
    id serial4 NOT NULL,
    mine_code varchar(32) NULL, -- 矿编码,矿编码
    mine_name varchar(100) NULL, -- 矿名称,矿名称
    group_code varchar(20) NULL, -- 集团编码
    group_name varchar(20) NULL, -- 集团名称
    section_code varchar(32) NULL, -- 分区编码
    alarm_point varchar(32) NULL, -- 报警点在分区内的位置
    fiber_point int4 NULL, -- 报警点在通道内的位置
    start_time timestamp NULL, -- 报警开始时间
    end_time timestamp NULL, -- 报警结束时间
    alarm_temp float8 NULL, -- 报警期间实时温度
    alarm_max float8 NULL, -- 报警期间最大值
    max_time timestamp NULL, -- 最大值时间
    alarm_min float8 NULL, -- 报警期间最小值
    min_time timestamp NULL, -- 最小值时间
    alarm_avg float8 NULL, -- 报警期间平均值
    alarm_type varchar(20) NULL, -- 报警级别；1 橙色报警，2 红色报警
    alarm_type_text varchar(50) NULL, -- 报警级别解释字段
    sample int4 NULL, -- 采样次数(多次报警为有效数据)
    data_time timestamp(6) NOT NULL, -- 数据变化时间
    collect_time timestamp(6) NOT NULL, -- 数据采集时间
    insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
    update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库更新时间,自动
    CONSTRAINT fiber_temp_alarm_realtime_uniq_section_code_alarm_point UNIQUE (section_code,alarm_point)
);
COMMENT ON TABLE public.fiber_temperature_alarm_realtime IS '测温分区温度报警表';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.section_code IS '分区编码';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_point IS '报警点在分区内的位置';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.fiber_point IS '报警点在通道内的位置';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.start_time IS '报警开始时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.end_time IS '报警结束时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_temp IS '报警期间实时温度';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_max IS '报警期间最大值';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.max_time IS '最大值时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_min IS '报警期间最小值';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.min_time IS '最小值时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_avg IS '报警期间平均值';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_type IS '报警级别；1 橙色报警，2 红色报警';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.alarm_type_text IS '报警级别解释字段';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.sample IS '采样次数(多次报警为有效数据)';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.data_time IS '数据变化时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.collect_time IS '数据采集时间';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.fiber_temperature_alarm_realtime.update_db_time IS '数据库更新时间,自动';

-- public.parking_lot_access_realtime definition
DROP TABLE IF EXISTS public.parking_lot_access_realtime;
CREATE TABLE public.parking_lot_access_realtime (
  id serial4 NOT NULL, -- 自增id
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(20) NULL, -- 集团名称
  card_id varchar(30) NOT NULL, -- 卡片号码
  license_plate_number varchar NULL, -- 车牌号
  card_type varchar(20) NULL, -- 卡片种类
  in_time timestamp NULL, -- 入场时间
  out_time timestamp NULL, -- 出场时间
  in_gate_name varchar(100) NULL, -- 入场名称
  out_gate_name varchar(100) NULL, -- 出场名称
  in_operator_card varchar(30) NULL, -- 入场操作卡号
  out_operator_card varchar(30) NULL, -- 出场操作卡号
  in_operator varchar(50) NULL, -- 入场操作员
  out_operator varchar(50) NULL, -- 出场操作员
  in_picture varchar(255) NULL, -- 入场图片
  in_user varchar(255) NULL, -- 入场人员
  out_picture varchar(255) NULL, -- 出场人员
  out_user varchar(255) NULL, -- 出场人员
  certificates_picture varchar NULL, -- 证件图片
  charge_amount float8 NULL, -- 收费金额
  balance float8 NULL, -- 卡上金额
  amount_receivable float8 NULL, -- 应收金额
  chargeable_time timestamp NULL, -- 收费时间
  charge_operator varchar(50) NULL, -- 收费人员
  charge_operator_card varchar(50) NULL, -- 收费人员卡号
  charge_gate varchar(100) NULL, -- 收费口名
  over_time_symbol varchar(10) NULL, -- 超时标志
  ovet_time_charge_time timestamp NULL, -- 超时收费时间
  over_time_charge_amount float8 NULL, -- 超时收费金额
  car_park_num int4 NULL, -- 车场编号
  big_small_flag int4 NULL, -- 大小标识
  free_reason varchar(100) NULL, -- 免费原因
  data_time timestamp(6) NOT NULL DEFAULT now(), -- 数据变化时间
  collect_time timestamp(6) NULL, -- 数据采集时间
  insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库更新时间,自动
  CONSTRAINT parking_lot_access_realtime_un UNIQUE (card_id, in_time)
);
COMMENT ON TABLE public.parking_lot_access_realtime IS '车场进出管理系统实时数据表';
COMMENT ON COLUMN public.parking_lot_access_realtime.id IS '自增id';
COMMENT ON COLUMN public.parking_lot_access_realtime.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.parking_lot_access_realtime.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.parking_lot_access_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.parking_lot_access_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.parking_lot_access_realtime.card_id IS '卡片号码';
COMMENT ON COLUMN public.parking_lot_access_realtime.license_plate_number IS '车牌号';
COMMENT ON COLUMN public.parking_lot_access_realtime.card_type IS '卡片种类';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_time IS '入场时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_time IS '出场时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_gate_name IS '入场名称';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_gate_name IS '出场名称';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_operator_card IS '入场操作卡号';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_operator_card IS '出场操作卡号';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_operator IS '入场操作员';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_operator IS '出场操作员';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_picture IS '入场图片';
COMMENT ON COLUMN public.parking_lot_access_realtime.in_user IS '入场人员';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_picture IS '出场人员';
COMMENT ON COLUMN public.parking_lot_access_realtime.out_user IS '出场人员';
COMMENT ON COLUMN public.parking_lot_access_realtime.certificates_picture IS '证件图片';
COMMENT ON COLUMN public.parking_lot_access_realtime.charge_amount IS '收费金额';
COMMENT ON COLUMN public.parking_lot_access_realtime.balance IS '卡上金额';
COMMENT ON COLUMN public.parking_lot_access_realtime.amount_receivable IS '应收金额';
COMMENT ON COLUMN public.parking_lot_access_realtime.chargeable_time IS '收费时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.charge_operator IS '收费人员';
COMMENT ON COLUMN public.parking_lot_access_realtime.charge_operator_card IS '收费人员卡号';
COMMENT ON COLUMN public.parking_lot_access_realtime.charge_gate IS '收费口名';
COMMENT ON COLUMN public.parking_lot_access_realtime.over_time_symbol IS '超时标志';
COMMENT ON COLUMN public.parking_lot_access_realtime.ovet_time_charge_time IS '超时收费时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.over_time_charge_amount IS '超时收费金额';
COMMENT ON COLUMN public.parking_lot_access_realtime.car_park_num IS '车场编号';
COMMENT ON COLUMN public.parking_lot_access_realtime.big_small_flag IS '大小标识';
COMMENT ON COLUMN public.parking_lot_access_realtime.free_reason IS '免费原因';
COMMENT ON COLUMN public.parking_lot_access_realtime.data_time IS '数据变化时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.collect_time IS '数据采集时间';
COMMENT ON COLUMN public.parking_lot_access_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.parking_lot_access_realtime.update_db_time IS '数据库更新时间,自动';

-- public.facial_recognition_gate_realtime definition
DROP TABLE IF EXISTS public.facial_recognition_gate_realtime;
CREATE TABLE public.facial_recognition_gate_realtime (
  id serial4 NOT NULL,
  mine_code varchar(32) NULL, -- 矿编码,矿编码
  mine_name varchar(100) NULL, -- 矿名称,矿名称
  group_code varchar(20) NULL, -- 集团编码
  group_name varchar(20) NULL, -- 集团名称
  "type" varchar(10) NULL, -- 对比类型
  "name" varchar(100) NULL,
  sex varchar(20) NULL, -- 性别
  nation varchar(50) NULL, -- 民族
  identity_type varchar(10) NULL, -- 证件类型
    identity_type_text varchar(50) NULL, -- 证件类型解释字段
  identity_number varchar(50) NULL, -- 身份证号
  identity_start_date varchar(100) NULL, -- 身份证有效期起
  identity_end_date varchar(100) NULL, -- 身份证有效期止
  identity_issue varchar(100) NULL, -- 签发机关
  identity_address varchar(150) NULL, -- 地址
  identity_name varchar(50) NULL, -- 证件照片名
  identity_img text NULL, -- 证件照片
    identity_img_path varchar(100) NULL, -- 证件照片路径
  shoot_name varchar(50) NULL, -- 全景照片名
  shoot_img text NULL, -- 全景照片
  shoot_img_path varchar(100) NULL, -- 全景照片路径
  head_name varchar(50) NULL, -- 抓拍照片名
  head_img text NULL, -- 抓拍照片
  head_img_path varchar(100) NULL, -- 抓拍照片路径
  device_mac varchar(50) NULL, -- 设备mac地址
  device_no varchar(50) NULL, -- 设备号码
  device_name varchar(50) NULL, -- 设备名称
  device_in_out_flag varchar(10) NULL, -- 设备进出标识
  device_location varchar(100) NULL, -- 设备安装位置
  auth_time timestamp NULL, -- 比对时间
  auth_model varchar(10) NULL, -- 识别模式：0普通模式，1门禁模式
  auth_score varchar(10) NULL, -- 比对分值
  auth_result varchar(100) NULL, -- 比对结果
  record_creatime timestamp NULL, -- 创建时间
  visitor_id varchar(50) NULL, -- 访客id
  ic_img_name varchar(50) NULL, -- IC卡图片名称
  ic_img_path varchar(100) NULL, -- IC卡图片路径
  temperature numeric NULL, -- 体温
  temperature_state varchar(10) NULL, -- 体温状态
  data_time timestamp(6) NOT NULL DEFAULT now(), -- 数据变化时间
  collect_time timestamp(6) NULL, -- 数据采集时间
  insert_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 数据库插入时间,自动
  update_db_time timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP -- 数据库更新时间,自动
);
COMMENT ON TABLE public.facial_recognition_gate_realtime IS '人脸识别闸机管理系统实时数据表';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.mine_code IS '矿编码,矿编码';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.mine_name IS '矿名称,矿名称';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.group_code IS '集团编码';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.group_name IS '集团名称';
COMMENT ON COLUMN public.facial_recognition_gate_realtime."type" IS '对比类型';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.sex IS '性别';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.nation IS '民族';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_type IS '证件类型';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_type_text IS '证件类型解释字段';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_number IS '身份证号';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_start_date IS '身份证有效期起';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_end_date IS '身份证有效期止';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_issue IS '签发机关';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_address IS '地址';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_name IS '证件照片名';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_img IS '证件照片';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.identity_img_path IS '证件照片路径';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.shoot_name IS '全景照片名';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.shoot_img IS '全景照片';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.shoot_img_path IS '全景照片路径';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.head_name IS '抓拍照片名';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.head_img IS '抓拍照片';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.head_img_path IS '抓拍照片路径';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.device_mac IS '设备mac地址';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.device_no IS '设备号码';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.device_name IS '设备名称';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.device_in_out_flag IS '设备进出标识';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.device_location IS '设备安装位置';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.auth_time IS '比对时间';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.auth_model IS '识别模式：0普通模式，1门禁模式';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.auth_score IS '比对分值';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.auth_result IS '比对结果';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.record_creatime IS '创建时间';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.visitor_id IS '访客id';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.ic_img_name IS 'IC卡图片名称';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.ic_img_path IS 'IC卡图片路径';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.temperature IS '体温';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.temperature_state IS '体温状态';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.data_time IS '数据变化时间';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.collect_time IS '数据采集时间';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.insert_db_time IS '数据库插入时间,自动';
COMMENT ON COLUMN public.facial_recognition_gate_realtime.update_db_time IS '数据库更新时间,自动';

ALTER TABLE public.facial_recognition_gate_realtime
    ADD CONSTRAINT facial_recognition_gate_realtime_uniq_name_identity_number_auth_time UNIQUE ("name",identity_number,auth_time);

-- 流程处理模块
DROP TABLE IF EXISTS "public"."data_stream_model";
create table public.data_stream_model
(
    id serial constraint data_stream_model_pk primary key,
    name varchar(255) not null,
    content jsonb
);
comment on table public.data_stream_model is '流程处理模块';
comment on column public.data_stream_model.name is '流程处理模块名称';
comment on column public.data_stream_model.content is '处理器内容';
alter table public.data_stream_model owner to postgres;

drop table if exists position_station_realtime;
create table position_station_realtime
(
    mine_code      varchar(32),
    mine_name      varchar(100),
    group_code     varchar(32),
    group_name     varchar(100),
    station_code   varchar(64)  not null constraint position_station_realtime_pkey primary key,
    run_status     integer      not null,
    power_status   integer      not null,
    data_time      timestamp(6) not null,
    created_at      timestamp(6) default CURRENT_TIMESTAMP,
    updated_at      timestamp(6) default CURRENT_TIMESTAMP,
    collect_time   timestamp(6) ,
    collect_status int2 NULL DEFAULT 100
);
comment on table position_station_realtime is '分站实时状态表 ';
comment on column position_station_realtime.mine_code is '矿编码,矿编码';
comment on column position_station_realtime.mine_name is '矿名称';
comment on column position_station_realtime.group_code is '集团编码';
comment on column position_station_realtime.group_name is '集团名称';
comment on column position_station_realtime.station_code is '分站编码,关联分站表';
comment on column position_station_realtime.run_status is '分站运行状态,字典';
comment on column position_station_realtime.power_status is '分站供电状态,字典';
comment on column position_station_realtime.data_time is '数据生成时间,格式为yyyy-MM-dd HH:mm:ss';
comment on column position_station_realtime.created_at is '数据库插入时间,自动';
comment on column position_station_realtime.updated_at is '数据库更新时间,自动';
comment on column position_station_realtime.collect_time is '采集时间';
comment on column position_station_realtime.collect_status is '采集状态100正常';

alter table position_station_realtime owner to postgres;

-- 表格新增位置 HERE

-- ----------------------------------------------------------
-- 视图
-- ----------------------------------------------------------

-- definition_view
CREATE OR REPLACE VIEW definition_view AS
  SELECT data_automation_definition.point_id,
      data_automation_definition.name,
      data_automation_definition.data_type,
      data_automation_definition.system_id,
      data_automation_definition.created_at,
      data_automation_definition.updated_at,
      data_automation_definition.point_type,
      data_automation_definition.type,
      NULL::character varying AS extra_type,
      NULL::character varying AS location,
      NULL::character varying AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM data_automation_definition
  UNION ALL
   SELECT safety_definition.point_id,
      safety_definition.location AS name,
      safety_definition.data_type,
      safety_definition.system_code AS system_id,
      safety_definition.insert_db_time AS created_at,
      safety_definition.update_db_time AS updated_at,
      safety_definition.point_value_type_code AS point_type,
      safety_definition.sensor_type_name AS type,
      NULL::character varying AS extra_type,
      safety_definition.location AS location,
      safety_definition.unit_name AS def_unit,
      safety_definition.translate,
      NULL::character varying AS parent_system_id
     FROM safety_definition
  UNION ALL
   SELECT data_derive_definition.point_id,
      data_derive_definition.name,
      data_derive_definition.data_type,
      data_derive_definition.system_id,
      data_derive_definition.created_at,
      data_derive_definition.updated_at,
      data_derive_definition.point_type,
      data_derive_definition.type,
      NULL::character varying AS extra_type,
      NULL::character varying AS location,
      NULL::character varying AS def_unit,
      NULL::character varying AS translate,
      data_derive_definition.parent_system_id
     FROM data_derive_definition
  UNION ALL
   SELECT vibration_definition.point_id,
      vibration_definition.name,
      vibration_definition.data_type,
      vibration_definition.system_id,
      vibration_definition.created_at,
      vibration_definition.updated_at,
      vibration_definition.point_type,
      vibration_definition.type,
      NULL::character varying AS extra_type,
      NULL::character varying AS location,
      vibration_definition.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM vibration_definition
  UNION ALL
   SELECT water_observation_definition.point_id,
      water_observation_definition.name,
      water_observation_definition.data_type,
      water_observation_definition.system_id,
      water_observation_definition.created_at,
      water_observation_definition.updated_at,
      water_observation_definition.point_type,
      water_observation_definition.type,
      water_observation_definition.location_type AS extra_type,
      water_observation_definition.area_name AS location,
      NULL::character varying AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM water_observation_definition
  UNION ALL
   SELECT gas_drainage.point_id,
      gas_drainage.name,
      gas_drainage.data_type,
      gas_drainage.system_id,
      gas_drainage.created_at,
      gas_drainage.updated_at,
      gas_drainage.point_type,
      gas_drainage.type,
      NULL::character varying AS extra_type,
      gas_drainage.location AS location,
      gas_drainage.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM gas_drainage
  UNION ALL
   SELECT stress_monitor_definition.point_id,
      stress_monitor_definition.name,
      stress_monitor_definition.data_type,
      stress_monitor_definition.system_id,
      stress_monitor_definition.created_at,
      stress_monitor_definition.updated_at,
      stress_monitor_definition.point_type,
      stress_monitor_definition.type,
      stress_monitor_definition.sensor_type AS extra_type,
      stress_monitor_definition.location AS location,
      stress_monitor_definition.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM stress_monitor_definition
  UNION ALL
   SELECT hole_stress_definition.point_id,
      hole_stress_definition.monitoring_area_name || ' ' || replace(hole_stress_definition.point_id, hole_stress_definition.system_id || '.', '') AS name,
      'float'::character varying AS data_type,
      hole_stress_definition.system_id,
      hole_stress_definition.insert_db_time AS created_at,
      hole_stress_definition.update_db_time AS updated_at,
      0 AS point_type,
      hole_stress_definition.sensor_type AS type,
      hole_stress_definition.monitoring_area_type AS extra_type,
      hole_stress_definition.location AS location,
      hole_stress_definition.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM hole_stress_definition
  UNION ALL
   SELECT roof_stress_definition.point_id,
      roof_stress_definition.monitoring_area_name || ' ' || replace(roof_stress_definition.point_id, roof_stress_definition.system_id || '.', '') AS name,
      'float'::character varying AS data_type,
      roof_stress_definition.system_id,
      now() AS created_at,
      now() AS updated_at,
      0 AS point_type,
      roof_stress_definition.sensor_type AS type,
      roof_stress_definition.point_monitor_type AS extra_type,
      roof_stress_definition.location AS location,
      roof_stress_definition.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM roof_stress_definition
  UNION ALL
   SELECT anchor_stress_definition.point_id,
      anchor_stress_definition.monitoring_area_name || ' ' || replace(anchor_stress_definition.point_id, anchor_stress_definition.system_id || '.', '') AS name,
      'float'::character varying AS data_type,
      anchor_stress_definition.system_id,
      now() AS created_at,
      now() AS updated_at,
      0 AS point_type,
      anchor_stress_definition.sensor_type AS type,
      anchor_stress_definition.point_monitor_type AS extra_type,
      anchor_stress_definition.location AS location,
      anchor_stress_definition.unit AS def_unit,
      ''::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM anchor_stress_definition
  UNION ALL
   SELECT tube_monitor_definition.point_id,
      tube_monitor_definition.point_name AS name,
      'float'::character varying AS data_type,
      substring(tube_monitor_definition.point_id, 0, 7) as system_id,
      tube_monitor_definition.created_at AS created_at,
      tube_monitor_definition.updated_at AS updated_at,
      0 AS point_type,
      tube_monitor_definition.sensor_type AS type,
      NULL::character varying AS extra_type,
      tube_monitor_definition.area_name AS location,
      tube_monitor_definition.unit AS def_unit,
      NULL::character varying AS translate,
      NULL::character varying AS parent_system_id
     FROM tube_monitor_definition
  ;

-- point_view
CREATE OR REPLACE VIEW public.point_view
AS SELECT
       CASE
           WHEN c.other_name IS NOT NULL THEN c.other_name
           ELSE d.name
           END AS name,
       d.system_id,
       NULL::text AS device_id,
           CASE
               WHEN c.point_type IS NOT NULL THEN c.point_type
               ELSE d.point_type
               END AS point_type,
       r.point_id,
       r.value,
       r.state AS status,
       CASE
           WHEN a.point_id IS NOT NULL THEN '1'::text
           ELSE '0'::text
           END AS alarm,
       c.translate,
       c.unit,
       c.unit_display_symbol AS unit_ds,
       c.unit_short_name AS unit_sn,
       r."timestamp",
       r.updated_at,
       d.type
   FROM data_automation_realtime r
            LEFT JOIN data_automation_definition d ON d.point_id::text = r.point_id::text
     LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
     LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
            alarm_realtime.point_id
           FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text
  WHERE r.value::text <> ''::text
UNION
SELECT
    CASE
        WHEN c.other_name IS NOT NULL THEN c.other_name
        ELSE d.name
        END AS name,
    d.parent_system_id AS system_id,
    NULL::text AS device_id,
        CASE
            WHEN c.point_type IS NOT NULL THEN c.point_type
            ELSE d.point_type
            END AS point_type,
    r.point_id,
    r.value,
    r.state AS status,
    CASE
        WHEN a.point_id IS NOT NULL THEN '1'::text
        ELSE '0'::text
        END AS alarm,
    c.translate,
    c.unit,
    c.unit_display_symbol AS unit_ds,
    c.unit_short_name AS unit_sn,
    r."timestamp",
    r.updated_at,
    d.type
FROM data_derive_realtime r
         LEFT JOIN data_derive_definition d ON d.point_id::text = r.point_id::text
     LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
    LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
    alarm_realtime.point_id
    FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text
UNION
SELECT
    CASE
        WHEN c.other_name IS NOT NULL THEN c.other_name
        ELSE d.name
        END AS name,
    d.system_id,
    NULL::text AS device_id,
        CASE
            WHEN c.point_type IS NOT NULL THEN c.point_type
            ELSE d.point_type
            END AS point_type,
    r.point_id,
    r.value,
    CASE
        WHEN r.state::text = '断线'::text THEN 'bad'::text
        ELSE 'good'::text
        END AS status,
    CASE
        WHEN a.point_id IS NOT NULL THEN '1'::text
        ELSE '0'::text
        END AS alarm,
    c.translate,
    c.unit,
    c.unit_display_symbol AS unit_ds,
    c.unit_short_name AS unit_sn,
    r."timestamp",
    r.updated_at,
    d.type
FROM gas_drainage_realtime r
         LEFT JOIN gas_drainage d ON d.point_id::text = r.point_id::text
     LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
    LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
    alarm_realtime.point_id
    FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text
UNION
SELECT
    CASE
        WHEN c.other_name IS NOT NULL THEN c.other_name
        ELSE d.name
        END AS name,
    d.system_id,
    NULL::text AS device_id,
        CASE
            WHEN c.point_type IS NOT NULL THEN c.point_type
            ELSE d.point_type
            END AS point_type,
    r.point_id,
    r.value::text AS value,
        r.state AS status,
    CASE
        WHEN a.point_id IS NOT NULL THEN '1'::text
        ELSE '0'::text
        END AS alarm,
    c.translate,
    c.unit,
    c.unit_display_symbol AS unit_ds,
    c.unit_short_name AS unit_sn,
    r."timestamp",
    r.updated_at,
    d.type
FROM stress_monitor_realtime r
         LEFT JOIN stress_monitor_definition d ON d.point_id::text = r.point_id::text
     LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
    LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
    alarm_realtime.point_id
    FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text
UNION
SELECT
    CASE
        WHEN c.other_name IS NOT NULL THEN c.other_name
        ELSE d.name
        END AS name,
    d.system_id,
    NULL::text AS device_id,
        CASE
            WHEN c.point_type IS NOT NULL THEN c.point_type
            ELSE d.point_type
            END AS point_type,
    r.point_id,
    r.value::text AS value,
        r.state AS status,
    CASE
        WHEN a.point_id IS NOT NULL THEN '1'::text
        ELSE '0'::text
        END AS alarm,
    c.translate,
    c.unit,
    c.unit_display_symbol AS unit_ds,
    c.unit_short_name AS unit_sn,
    r."timestamp",
    r.updated_at,
    d.type
FROM water_observation_realtime r
         LEFT JOIN water_observation_definition d ON d.point_id::text = r.point_id::text
     LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
    LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
    alarm_realtime.point_id
    FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text
UNION
SELECT
    CASE
        WHEN c.other_name IS NOT NULL THEN c.other_name
        ELSE d.location
        END AS name,
    d.system_code AS system_id,
    NULL::text AS device_id,
        CASE
            WHEN c.point_type IS NOT NULL THEN c.point_type
            ELSE d.point_value_type_code::integer
        END AS point_type,
    r.point_id,
    r.value,
    'good'::text AS status,
        CASE
            WHEN r.alarm_status_code IS NOT NULL THEN r.alarm_status_code::text
            WHEN a.point_id IS NOT NULL THEN '1'::text
            ELSE '0'::text
        END AS alarm,
        CASE
            WHEN d.translate IS NOT NULL THEN d.translate::text
            ELSE c.translate
        END AS translate,
        CASE
            WHEN d.unit_code IS NOT NULL THEN d.unit_code
            ELSE c.unit
        END AS unit,
    c.unit_display_symbol AS unit_ds,
    c.unit_short_name AS unit_sn,
    r.data_time AS "timestamp",
    r.update_db_time AS updated_at,
    d.sensor_type_name AS type
FROM safety_realtime r
    LEFT JOIN safety_definition d ON d.point_id::text = r.point_id::text
    LEFT JOIN data_definition_conf c ON c.point_id::text = r.point_id::text
    LEFT JOIN ( SELECT DISTINCT alarm_realtime.system_id,
    alarm_realtime.point_id
    FROM alarm_realtime) a ON d.system_id::text = a.system_id::text AND d.point_id::text = a.point_id::text;

-- device_view
CREATE OR REPLACE VIEW device_view AS
 SELECT d.device_id,
    d.name,
    d.system_id,
    d.system AS system_name,
        CASE
            WHEN pa.alarm_count > 0 THEN 1
            ELSE 0
        END AS alarm,
    pa.alarm_count
   FROM device d
     LEFT JOIN ( SELECT point_view.device_id,
            sum(
                CASE
                    WHEN point_view.alarm = '1'::text THEN 1
                    ELSE 0
                END) AS alarm_count
           FROM point_view
          GROUP BY point_view.device_id) pa ON d.device_id::text = pa.device_id;

-- geography_columns
CREATE OR REPLACE VIEW geography_columns AS
 SELECT current_database() AS f_table_catalog,
    n.nspname AS f_table_schema,
    c.relname AS f_table_name,
    a.attname AS f_geography_column,
    postgis_typmod_dims(a.atttypmod) AS coord_dimension,
    postgis_typmod_srid(a.atttypmod) AS srid,
    postgis_typmod_type(a.atttypmod) AS type
   FROM pg_class c,
    pg_attribute a,
    pg_type t,
    pg_namespace n
  WHERE t.typname = 'geography'::name AND a.attisdropped = false AND a.atttypid = t.oid AND a.attrelid = c.oid AND c.relnamespace = n.oid AND (c.relkind = ANY (ARRAY['r'::"char", 'v'::"char", 'm'::"char", 'f'::"char", 'p'::"char"])) AND NOT pg_is_other_temp_schema(c.relnamespace) AND has_table_privilege(c.oid, 'SELECT'::text);


-- geometry_columns
CREATE OR REPLACE VIEW geometry_columns AS
 SELECT current_database()::character varying(256) AS f_table_catalog,
    n.nspname AS f_table_schema,
    c.relname AS f_table_name,
    a.attname AS f_geometry_column,
    COALESCE(postgis_typmod_dims(a.atttypmod), sn.ndims, 2) AS coord_dimension,
    COALESCE(NULLIF(postgis_typmod_srid(a.atttypmod), 0), sr.srid, 0) AS srid,
    replace(replace(COALESCE(NULLIF(upper(postgis_typmod_type(a.atttypmod)), 'GEOMETRY'::text), st.type, 'GEOMETRY'::text), 'ZM'::text, ''::text), 'Z'::text, ''::text)::character varying(30) AS type
   FROM pg_class c
     JOIN pg_attribute a ON a.attrelid = c.oid AND NOT a.attisdropped
     JOIN pg_namespace n ON c.relnamespace = n.oid
     JOIN pg_type t ON a.atttypid = t.oid
     LEFT JOIN ( SELECT s.connamespace,
            s.conrelid,
            s.conkey,
            replace(split_part(s.consrc, ''''::text, 2), ')'::text, ''::text) AS type
           FROM ( SELECT pg_constraint.connamespace,
                    pg_constraint.conrelid,
                    pg_constraint.conkey,
                    pg_get_constraintdef(pg_constraint.oid) AS consrc
                   FROM pg_constraint) s
          WHERE s.consrc ~~* '%geometrytype(% = %'::text) st ON st.connamespace = n.oid AND st.conrelid = c.oid AND (a.attnum = ANY (st.conkey))
     LEFT JOIN ( SELECT s.connamespace,
            s.conrelid,
            s.conkey,
            replace(split_part(s.consrc, ' = '::text, 2), ')'::text, ''::text)::integer AS ndims
           FROM ( SELECT pg_constraint.connamespace,
                    pg_constraint.conrelid,
                    pg_constraint.conkey,
                    pg_get_constraintdef(pg_constraint.oid) AS consrc
                   FROM pg_constraint) s
          WHERE s.consrc ~~* '%ndims(% = %'::text) sn ON sn.connamespace = n.oid AND sn.conrelid = c.oid AND (a.attnum = ANY (sn.conkey))
     LEFT JOIN ( SELECT s.connamespace,
            s.conrelid,
            s.conkey,
            replace(replace(split_part(s.consrc, ' = '::text, 2), ')'::text, ''::text), '('::text, ''::text)::integer AS srid
           FROM ( SELECT pg_constraint.connamespace,
                    pg_constraint.conrelid,
                    pg_constraint.conkey,
                    pg_get_constraintdef(pg_constraint.oid) AS consrc
                   FROM pg_constraint) s
          WHERE s.consrc ~~* '%srid(% = %'::text) sr ON sr.connamespace = n.oid AND sr.conrelid = c.oid AND (a.attnum = ANY (sr.conkey))
  WHERE (c.relkind = ANY (ARRAY['r'::"char", 'v'::"char", 'm'::"char", 'f'::"char", 'p'::"char"])) AND NOT c.relname = 'raster_columns'::name AND t.typname = 'geometry'::name AND NOT pg_is_other_temp_schema(c.relnamespace) AND has_table_privilege(c.oid, 'SELECT'::text);


-- mine_stress_all
CREATE OR REPLACE VIEW mine_stress_all AS
  SELECT d.point_id,
    d.sensor_type,
    d.sensor_status,
    d.tunnel_name,
    d.point_monitor_type,
    r.monitor_value,
        CASE
            WHEN r.deep_monitor_value IS NOT NULL THEN r.deep_monitor_value
            ELSE ''::character varying
        END AS deep_monitor_value,
    r.updated_at
   FROM roof_stress_definition d
     LEFT JOIN mine_stress_realtime r ON d.point_id::text = r.point_id::text
UNION ALL
 SELECT d.point_id,
    d.sensor_type,
    d.sensor_status,
    d.tunnel_name,
    d.point_monitor_type,
    r.monitor_value,
        CASE
            WHEN r.deep_monitor_value IS NOT NULL THEN r.deep_monitor_value
            ELSE ''::character varying
        END AS deep_monitor_value,
    r.updated_at
   FROM anchor_stress_definition d
     LEFT JOIN mine_stress_realtime r ON d.point_id::text = r.point_id::text
UNION ALL
 SELECT d.point_id,
    d.sensor_type,
    d.sensor_status,
    d.tunnel_name,
    d.point_monitor_type,
    r.monitor_value,
        CASE
            WHEN r.deep_monitor_value IS NOT NULL THEN r.deep_monitor_value
            ELSE ''::character varying
        END AS deep_monitor_value,
    r.updated_at
   FROM tunnel_distance_stress_definition d
     LEFT JOIN mine_stress_realtime r ON d.point_id::text = r.point_id::text;

-- definition_relative_conf
CREATE OR REPLACE VIEW definition_relative_conf
AS SELECT
        dad.point_id,
        dad.def_unit,
        CASE
          WHEN ddc.point_type IS NULL THEN dad.point_type
          ELSE ddc.point_type
        END AS point_type,
        dad.system_id,
        ms.name AS system_name,
        dad.name,
        dad.data_type,
        dad.created_at,
        dad.updated_at,
        ddc.other_name,
        ddc.short_name,
        ddc.reserve,
        dad.parent_system_id,
        CASE
          WHEN ddc.translate IS NULL THEN dad.translate::text
          ELSE ddc.translate
        END AS translate,
        CASE
          WHEN ddc.unit IS NULL THEN dad.def_unit
          ELSE ddc.unit
        END AS unit,
        ddc.expression,
        ddc.expression_params,
        ddc.node_label,
        ca.id AS attribute_id,
        ce.id AS device_id,
        NULL::text AS business_id,
        ca.name AS attribute_name,
        ce.name AS device_name,
        NULL::text AS business_name,
        ddc.warn,
        dad.type,
        dad.extra_type,
        dad.location
    FROM definition_view dad
        LEFT JOIN data_definition_conf ddc ON dad.point_id::text = ddc.point_id::text
        LEFT JOIN model_system ms ON dad.system_id::text = ms.id::text
        LEFT JOIN mapping_equip_attr_point mpe ON dad.point_id::text = mpe.point_id::text
        LEFT JOIN cms_equipment ce ON ce.id = mpe.equip_id
        LEFT JOIN cms_attribute ca ON ca.id = mpe.attr_id
;

-- point_definition_view
CREATE OR REPLACE VIEW point_definition_view AS
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    d.type,
    c.unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_type
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM data_automation_definition d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    d.type,
    c.unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_type
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM data_derive_definition d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.parent_system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    d.type,
        CASE
            WHEN c.unit IS NOT NULL THEN c.unit
            ELSE d.unit
        END AS unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_type
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM gas_drainage d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    d.type,
    c.unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_type
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM stress_monitor_definition d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    d.type,
    c.unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_type
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM water_observation_definition d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.location
        END AS name,
    d.point_id,
    c.short_name,
    d.system_code AS system_id,
    s.name AS system_name,
    d.sensor_type_name AS type,
        CASE
            WHEN c.unit IS NOT NULL THEN c.unit
            ELSE d.unit_name
        END AS unit,
        CASE
            WHEN c.translate IS NOT NULL THEN c.translate::character varying
            ELSE d.translate
        END AS translate,
        CASE
            WHEN c.point_type IS NULL THEN d.point_value_type_code::integer
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM safety_definition d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_code::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text
UNION
 SELECT
        CASE
            WHEN c.other_name IS NOT NULL THEN c.other_name
            ELSE d.name
        END AS name,
    d.point_id,
    c.short_name,
    d.system_id,
    s.name AS system_name,
    c.unit AS type,
    ''::character varying AS unit,
    c.translate,
        CASE
            WHEN c.point_type IS NULL THEN 0
            ELSE c.point_type
        END AS point_type,
    m.device_id,
    v.name AS device_name,
    NULL::integer AS geolocation_area_id
   FROM broadcast d
     LEFT JOIN data_definition_conf c ON d.point_id::text = c.point_id::text
     LEFT JOIN model_system s ON d.system_id::text = s.id::text
     LEFT JOIN data_device_model_conf m ON d.point_id::text = m.point_id::text
     LEFT JOIN data_device v ON m.device_id::text = v.id::text;

-- raster_columns
CREATE OR REPLACE VIEW raster_columns AS
 SELECT current_database() AS r_table_catalog,
    n.nspname AS r_table_schema,
    c.relname AS r_table_name,
    a.attname AS r_raster_column,
    COALESCE(_raster_constraint_info_srid(n.nspname, c.relname, a.attname), ( SELECT st_srid('010100000000000000000000000000000000000000'::geometry) AS st_srid)) AS srid,
    _raster_constraint_info_scale(n.nspname, c.relname, a.attname, 'x'::bpchar) AS scale_x,
    _raster_constraint_info_scale(n.nspname, c.relname, a.attname, 'y'::bpchar) AS scale_y,
    _raster_constraint_info_blocksize(n.nspname, c.relname, a.attname, 'width'::text) AS blocksize_x,
    _raster_constraint_info_blocksize(n.nspname, c.relname, a.attname, 'height'::text) AS blocksize_y,
    COALESCE(_raster_constraint_info_alignment(n.nspname, c.relname, a.attname), false) AS same_alignment,
    COALESCE(_raster_constraint_info_regular_blocking(n.nspname, c.relname, a.attname), false) AS regular_blocking,
    _raster_constraint_info_num_bands(n.nspname, c.relname, a.attname) AS num_bands,
    _raster_constraint_info_pixel_types(n.nspname, c.relname, a.attname) AS pixel_types,
    _raster_constraint_info_nodata_values(n.nspname, c.relname, a.attname) AS nodata_values,
    _raster_constraint_info_out_db(n.nspname, c.relname, a.attname) AS out_db,
    _raster_constraint_info_extent(n.nspname, c.relname, a.attname) AS extent,
    COALESCE(_raster_constraint_info_index(n.nspname, c.relname, a.attname), false) AS spatial_index
   FROM pg_class c,
    pg_attribute a,
    pg_type t,
    pg_namespace n
  WHERE t.typname = 'raster'::name AND a.attisdropped = false AND a.atttypid = t.oid AND a.attrelid = c.oid AND c.relnamespace = n.oid AND (c.relkind = ANY (ARRAY['r'::"char", 'v'::"char", 'm'::"char", 'f'::"char", 'p'::"char"])) AND NOT pg_is_other_temp_schema(c.relnamespace) AND has_table_privilege(c.oid, 'SELECT'::text);


-- raster_overviews
CREATE OR REPLACE VIEW raster_overviews AS
 SELECT current_database() AS o_table_catalog,
    n.nspname AS o_table_schema,
    c.relname AS o_table_name,
    a.attname AS o_raster_column,
    current_database() AS r_table_catalog,
    split_part(split_part(s.consrc, '''::name'::text, 1), ''''::text, 2)::name AS r_table_schema,
    split_part(split_part(s.consrc, '''::name'::text, 2), ''''::text, 2)::name AS r_table_name,
    split_part(split_part(s.consrc, '''::name'::text, 3), ''''::text, 2)::name AS r_raster_column,
    btrim(split_part(s.consrc, ','::text, 2))::integer AS overview_factor
   FROM pg_class c,
    pg_attribute a,
    pg_type t,
    pg_namespace n,
    ( SELECT pg_constraint.connamespace,
            pg_constraint.conrelid,
            pg_constraint.conkey,
            pg_get_constraintdef(pg_constraint.oid) AS consrc
           FROM pg_constraint) s
  WHERE t.typname = 'raster'::name AND a.attisdropped = false AND a.atttypid = t.oid AND a.attrelid = c.oid AND c.relnamespace = n.oid AND (c.relkind::text = ANY (ARRAY['r'::character(1), 'v'::character(1), 'm'::character(1), 'f'::character(1)]::text[])) AND s.connamespace = n.oid AND s.conrelid = c.oid AND s.consrc ~~ '%_overview_constraint(%'::text AND NOT pg_is_other_temp_schema(c.relnamespace) AND has_table_privilege(c.oid, 'SELECT'::text);