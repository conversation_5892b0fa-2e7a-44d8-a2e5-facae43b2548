drop database if exists push;
create database push;
\c push;


-- public.bdata_alert_indictor_id_seq definition

DROP SEQUENCE IF EXISTS public.bdata_alert_indictor_id_seq;
CREATE SEQUENCE public.bdata_alert_indictor_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.bdata_alert_indictor_type_id_seq definition

DROP SEQUENCE IF EXISTS public.bdata_alert_indictor_type_id_seq;
CREATE SEQUENCE public.bdata_alert_indictor_type_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.bdata_alert_push_mid_id_seq definition

DROP SEQUENCE IF EXISTS public.bdata_alert_push_mid_id_seq;
CREATE SEQUENCE public.bdata_alert_push_mid_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.bdata_rule_conf_id_seq definition

DROP SEQUENCE IF EXISTS public.bdata_rule_conf_id_seq;
CREATE SEQUENCE public.bdata_rule_conf_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.bdata_rule_push_mid_id_seq definition

DROP SEQUENCE IF EXISTS public.bdata_rule_push_mid_id_seq;
CREATE SEQUENCE public.bdata_rule_push_mid_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.channel_push_mode_seq definition

DROP SEQUENCE IF EXISTS public.channel_push_mode_seq;
CREATE SEQUENCE public.channel_push_mode_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.channel_push_type_seq definition

DROP SEQUENCE IF EXISTS public.channel_push_type_seq;
CREATE SEQUENCE public.channel_push_type_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.hazard_handle_employee_id_seq definition

DROP SEQUENCE IF EXISTS public.hazard_handle_employee_id_seq;
CREATE SEQUENCE public.hazard_handle_employee_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.hazard_warning_alarm_handle_id_seq definition

DROP SEQUENCE IF EXISTS public.hazard_warning_alarm_handle_id_seq;
CREATE SEQUENCE public.hazard_warning_alarm_handle_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.hazard_warning_alarm_info_id_seq definition

DROP SEQUENCE IF EXISTS public.hazard_warning_alarm_info_id_seq;
CREATE SEQUENCE public.hazard_warning_alarm_info_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.hazard_warning_time_rule_id_seq definition

DROP SEQUENCE IF EXISTS public.hazard_warning_time_rule_id_seq;
CREATE SEQUENCE public.hazard_warning_time_rule_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;


-- public.indicator_system_relative_id_seq definition

DROP SEQUENCE IF EXISTS public.indicator_system_relative_id_seq;
CREATE SEQUENCE public.indicator_system_relative_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.push_channel_mode_employee_id_seq definition

DROP SEQUENCE IF EXISTS public.push_channel_mode_employee_id_seq;
CREATE SEQUENCE public.push_channel_mode_employee_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.push_detail_id_seq definition

DROP SEQUENCE IF EXISTS public.push_detail_id_seq;
CREATE SEQUENCE public.push_detail_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.push_history_seq definition

DROP SEQUENCE IF EXISTS public.push_history_seq;
CREATE SEQUENCE public.push_history_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.push_sms_id_seq definition

DROP SEQUENCE IF EXISTS public.push_sms_id_seq;
CREATE SEQUENCE public.push_sms_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.send_account_seq definition

DROP SEQUENCE IF EXISTS public.send_account_seq;
CREATE SEQUENCE public.send_account_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.user_account_seq definition

DROP SEQUENCE IF EXISTS public.user_account_seq;
CREATE SEQUENCE public.user_account_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.waring_rule_issue_id_seq definition

DROP SEQUENCE IF EXISTS public.waring_rule_issue_id_seq;
CREATE SEQUENCE public.waring_rule_issue_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.warning_alarm_info_id_seq definition

DROP SEQUENCE IF EXISTS public.warning_alarm_info_id_seq;
CREATE SEQUENCE public.warning_alarm_info_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.warning_channel_classify_id_seq definition

DROP SEQUENCE IF EXISTS public.warning_channel_classify_id_seq;
CREATE SEQUENCE public.warning_channel_classify_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 9223372036854775807
	START 1
	CACHE 1
	NO CYCLE;


-- public.warning_time_rule_id_seq definition

DROP SEQUENCE IF EXISTS public.warning_time_rule_id_seq;
CREATE SEQUENCE public.warning_time_rule_id_seq
	INCREMENT BY 1
	MINVALUE 1
	MAXVALUE 2147483647
	START 1
	CACHE 1
	NO CYCLE;

-------------
SELECT setval('"public"."bdata_alert_indictor_id_seq"', 258, true);
SELECT setval('"public"."bdata_alert_indictor_type_id_seq"', 89, true);


-- ----------------------------
-- Table structure for ai_model_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."ai_model_system";
CREATE TABLE "public"."ai_model_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."ai_model_system"."id" IS '系统id';
COMMENT ON COLUMN "public"."ai_model_system"."name" IS '系统名称';

-- ----------------------------
-- Table structure for bdata_alert_indictor
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor";
CREATE TABLE "public"."bdata_alert_indictor" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_indictor_id_seq'::regclass),
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "type_id" int4,
  "active" bool,
  "indictor" varchar(255) COLLATE "pg_catalog"."default",
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "oper" varchar(255) COLLATE "pg_catalog"."default",
  "reminder" varchar(255) COLLATE "pg_catalog"."default",
  "sms" bool,
  "warningcreatetime" varchar(255) COLLATE "pg_catalog"."default",
  "channel_push_mode_id" int8,
  "oid" varchar COLLATE "pg_catalog"."default",
  "alg_config_id" int4,
  "camera_id" int4,
  "type" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."bdata_alert_indictor"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."type_id" IS '指标类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."active" IS '是否开启';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."indictor" IS '指标';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."condition" IS '判断条件';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."oper" IS '处理措施';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."reminder" IS '提醒目标人';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."sms" IS '是否短信通知';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."warningcreatetime" IS '报警最后触发时间';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."channel_push_mode_id" IS '通知模板id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."alg_config_id" IS '算法配置id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."camera_id" IS 'AI--摄像头id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."type" IS '算法类型';
COMMENT ON TABLE "public"."bdata_alert_indictor" IS '报警指标库';

-- ----------------------------
-- Table structure for bdata_alert_indictor_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor_type";
CREATE TABLE "public"."bdata_alert_indictor_type" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_indictor_type_id_seq'::regclass),
  "alert_type" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pid" int4,
  "oid" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."alert_type" IS '报警类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."pid" IS '父id';
COMMENT ON TABLE "public"."bdata_alert_indictor_type" IS '报警指标类型树';

-- ----------------------------
-- Table structure for bdata_alert_push_mid
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_push_mid";
CREATE TABLE "public"."bdata_alert_push_mid" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_push_mid_id_seq'::regclass),
  "alert_id" int4,
  "push_mode_id" int8
)
;
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."id" IS '自增主键id';
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."alert_id" IS '指标库id';
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."push_mode_id" IS '通知模板id';

-- ----------------------------
-- Table structure for bdata_rule_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_rule_conf";
CREATE TABLE "public"."bdata_rule_conf" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_rule_conf_id_seq'::regclass),
  "hazard_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_type" varchar(255) COLLATE "pg_catalog"."default",
  "active" bool DEFAULT true,
  "red_warning" varchar(255) COLLATE "pg_catalog"."default",
  "orange_warning" varchar(255) COLLATE "pg_catalog"."default",
  "yellow_warning" varchar(255) COLLATE "pg_catalog"."default",
  "blue_warning" varchar(255) COLLATE "pg_catalog"."default",
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "system_id" int4
)
;
COMMENT ON COLUMN "public"."bdata_rule_conf"."id" IS '主键id';
COMMENT ON COLUMN "public"."bdata_rule_conf"."hazard_type" IS '灾害类型';
COMMENT ON COLUMN "public"."bdata_rule_conf"."point_type" IS '测点类型';
COMMENT ON COLUMN "public"."bdata_rule_conf"."active" IS '是否开启';
COMMENT ON COLUMN "public"."bdata_rule_conf"."red_warning" IS '红色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."orange_warning" IS '橙色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."yellow_warning" IS '黄色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."blue_warning" IS '蓝色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."system_name" IS '所属系统';
COMMENT ON COLUMN "public"."bdata_rule_conf"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."bdata_rule_conf"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."bdata_rule_conf"."system_id" IS '系统编码';
COMMENT ON TABLE "public"."bdata_rule_conf" IS '预警报警指标配置(同指标)';

-- ----------------------------
-- Table structure for bdata_rule_push_mid
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_rule_push_mid";
CREATE TABLE "public"."bdata_rule_push_mid" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_rule_push_mid_id_seq'::regclass),
  "bdata_rule_id" int4,
  "push_mode_id" int8
)
;
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."id" IS '自增主键id';
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."bdata_rule_id" IS '指标库id';
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."push_mode_id" IS '通知模板id';

-- ----------------------------
-- Table structure for channel_push_mode
-- ----------------------------
DROP TABLE IF EXISTS "public"."channel_push_mode";
CREATE TABLE "public"."channel_push_mode" (
  "id" int8 NOT NULL DEFAULT nextval('channel_push_mode_seq'::regclass),
  "push_types" varchar(10) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(500) COLLATE "pg_catalog"."default",
  "send_users" varchar(255) COLLATE "pg_catalog"."default",
  "send_groups" varchar(255) COLLATE "pg_catalog"."default",
  "cc_users" varchar(255) COLLATE "pg_catalog"."default",
  "cc_groups" varchar(255) COLLATE "pg_catalog"."default",
  "send_delay" varchar(255) COLLATE "pg_catalog"."default",
  "cc_delay" varchar(255) COLLATE "pg_catalog"."default",
  "send_users_name" varchar(1000) COLLATE "pg_catalog"."default",
  "send_groups_name" varchar(1000) COLLATE "pg_catalog"."default",
  "cc_users_name" varchar(1000) COLLATE "pg_catalog"."default",
  "cc_groups_name" varchar(1000) COLLATE "pg_catalog"."default",
  "send_type" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" varchar(50) COLLATE "pg_catalog"."default",
  "create_user_code" int8,
  "create_user_name" varchar(255) COLLATE "pg_catalog"."default",
  "native_phones" varchar(500) COLLATE "pg_catalog"."default",
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(10) COLLATE "pg_catalog"."default",
  "push_code" varchar(10) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."channel_push_mode"."push_types" IS '推送类型：1-通知；2-短信；3-邮件';
COMMENT ON COLUMN "public"."channel_push_mode"."send_type" IS '发送方式1--及时发送 2--定时发送 3--未处理后发送';
COMMENT ON COLUMN "public"."channel_push_mode"."create_user_code" IS '创建人员工号';
COMMENT ON COLUMN "public"."channel_push_mode"."create_user_name" IS '创建人姓名';
COMMENT ON COLUMN "public"."channel_push_mode"."native_phones" IS '本地手机号';
COMMENT ON COLUMN "public"."channel_push_mode"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."channel_push_mode"."channel_code" IS '推送 的 厂商信息表 中的代码 短信:1--联通短信；2--短信机；3--融合通信；4--阿里云短信；通知：1--app推送 极光推送；2--业务(app/pc通知)';
COMMENT ON COLUMN "public"."channel_push_mode"."push_code" IS '推送 的 推送方式 中的代码';

-- ----------------------------
-- Table structure for channel_push_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."channel_push_type";
CREATE TABLE "public"."channel_push_type" (
  "id" int8 NOT NULL DEFAULT nextval('channel_push_type_seq'::regclass),
  "chcode_a" varchar(100) COLLATE "pg_catalog"."default",
  "chcode_b" varchar(100) COLLATE "pg_catalog"."default",
  "is_push_flag" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default",
  "push_type" int8
)
;
COMMENT ON COLUMN "public"."channel_push_type"."id" IS '记录ID';
COMMENT ON COLUMN "public"."channel_push_type"."chcode_a" IS '标准模版';
COMMENT ON COLUMN "public"."channel_push_type"."chcode_b" IS '对应中文模版';
COMMENT ON COLUMN "public"."channel_push_type"."is_push_flag" IS '该渠道有无该推送方式的权限';
COMMENT ON COLUMN "public"."channel_push_type"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."channel_push_type"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."channel_push_type"."message" IS '备注';
COMMENT ON COLUMN "public"."channel_push_type"."push_type" IS '推送类型';
COMMENT ON TABLE "public"."channel_push_type" IS '厂商推送方式字典表  管理厂家的推送方式配置';

-- ----------------------------
-- Table structure for gis_warning_alarm_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_warning_alarm_info";
CREATE TABLE "public"."gis_warning_alarm_info" (
  "warning_alarm_info_id" int4 NOT NULL DEFAULT nextval('warning_alarm_info_id_seq'::regclass),
  "warning_alarm_info_param" text COLLATE "pg_catalog"."default",
  "warning_alarm_info_createtime" varchar(30) COLLATE "pg_catalog"."default",
  "warning_alarm_info_status" int2 DEFAULT 0,
  "warning_alarm_id" int4,
  "warning_alarm_info_content" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" int4,
  "warning_alarm_info_show" int2 DEFAULT 1,
  "warning_alarm_info_handle" int2 DEFAULT 0,
  "warning_alarm_info_updatetime" varchar(30) COLLATE "pg_catalog"."default",
  "warning_alarm_info_type" int2,
  "warning_alarm_info_level" varchar(255) COLLATE "pg_catalog"."default",
  "message_template_id" int4,
  "warning_alarm_info_describe" varchar(255) COLLATE "pg_catalog"."default",
  "warning_alarm_source_system_code" varchar(255) COLLATE "pg_catalog"."default",
  "warning_alarm_source_id" int4,
  "update_time" timestamp(6) DEFAULT now(),
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "point_ids" text COLLATE "pg_catalog"."default",
  "camera_id" int4,
  "camera_name" varchar(255) COLLATE "pg_catalog"."default",
  "alg_id" int4,
  "alg_name" varchar(255) COLLATE "pg_catalog"."default",
  "level" int4,
  "alarm_type" int4,
  "handle_status" int4,
  "handle_advice" text COLLATE "pg_catalog"."default",
  "location" varchar COLLATE "pg_catalog"."default",
  "image_path" text COLLATE "pg_catalog"."default",
  "system_type" varchar COLLATE "pg_catalog"."default",
  "video_path" text COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "def_unit" varchar COLLATE "pg_catalog"."default",
  "min" varchar COLLATE "pg_catalog"."default",
  "max" varchar COLLATE "pg_catalog"."default",
  "equality" varchar COLLATE "pg_catalog"."default",
  "sensor_name" varchar COLLATE "pg_catalog"."default",
  "display_name" varchar COLLATE "pg_catalog"."default",
  "alarm_distance_d_t_o_list" text COLLATE "pg_catalog"."default",
  "behavior_type" int2,
  "extra" text COLLATE "pg_catalog"."default",
  "alg_real_name" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_param" IS '信息参输';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_createtime" IS '创建时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_status" IS '是否推消息状态 0 未成功 1已成功';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_id" IS '报警预警id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_content" IS '处理结果';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_show" IS '实现显示到实时统计 默认是1 显示0 不显示';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_handle" IS '是否处理 默认是0 没处理 1已处理';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_updatetime" IS '修改时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_type" IS '报警类型 1 预警 2 报警';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_level" IS '级别';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."message_template_id" IS '消息模板id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_describe" IS '描述';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_source_system_code" IS '对应系统编码';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_source_id" IS '系统对应id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."type" IS '类型';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."point_ids" IS '站点名';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."camera_id" IS '摄像头id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."camera_name" IS '摄像机名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_id" IS '算法id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_name" IS '算法名称(分类名称)';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."level" IS 'AI隐患级别';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alarm_type" IS '报警类型 1--AI预警报警';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."handle_status" IS '处理状态 0--未处理  1--已处理';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."handle_advice" IS '处理意见';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."location" IS 'AI位置--区域名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."image_path" IS 'AI--image路径';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_type" IS '系统类型';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."video_path" IS 'AI--视频路径';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_name" IS '系统名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_id" IS '系统编码';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."value" IS '监测值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."def_unit" IS '监测值--单位';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."min" IS '阈值--最小值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."max" IS '阈值--最大值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."equality" IS '阈值--固定值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."sensor_name" IS '传感器类型名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."display_name" IS '摄像机别名';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alarm_distance_d_t_o_list" IS '附近 人或车列表';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."behavior_type" IS '行为类型(0是人 1是物 2是车)';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."extra" IS 'AI统计字段';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_real_name" IS '算法名称(中文)';
COMMENT ON TABLE "public"."gis_warning_alarm_info" IS '预警报警详情表';

-- ----------------------------
-- Table structure for hazard_handle_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_handle_employee";
CREATE TABLE "public"."hazard_handle_employee" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_handle_employee_id_seq'::regclass),
  "employee_name" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "post_name" varchar(255) COLLATE "pg_catalog"."default",
  "handle_id" int4
)
;
COMMENT ON COLUMN "public"."hazard_handle_employee"."employee_name" IS '员工姓名';
COMMENT ON COLUMN "public"."hazard_handle_employee"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."hazard_handle_employee"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."hazard_handle_employee"."handle_id" IS '预警报警处理id';

-- ----------------------------
-- Table structure for hazard_warning_alarm_handle
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_alarm_handle";
CREATE TABLE "public"."hazard_warning_alarm_handle" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_alarm_handle_id_seq'::regclass),
  "hazard_warning_alarm_info_id" int4,
  "handle_person" varchar(255) COLLATE "pg_catalog"."default",
  "handle_time" varchar(255) COLLATE "pg_catalog"."default",
  "handle_operation" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."hazard_warning_alarm_info_id" IS '报警id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_person" IS '处理人';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_time" IS '处理时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_operation" IS '采取措施';

-- ----------------------------
-- Table structure for hazard_warning_alarm_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_alarm_info";
CREATE TABLE "public"."hazard_warning_alarm_info" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_alarm_info_id_seq'::regclass),
  "hazard_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_type" varchar(255) COLLATE "pg_catalog"."default",
  "active" bool DEFAULT true,
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "warning_level" varchar COLLATE "pg_catalog"."default",
  "warning_content" text COLLATE "pg_catalog"."default",
  "handle_status" int2 DEFAULT 0,
  "warning_status" int2 DEFAULT 0,
  "bdata_rule_id" int4,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "system_id" int4,
  "handle_operation" varchar COLLATE "pg_catalog"."default",
  "handle_persons" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."id" IS '主键id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."hazard_type" IS '灾害类型';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."point_type" IS '测点类型';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."active" IS '是否开启';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."system_name" IS '所属系统';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_level" IS '预警级别';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_content" IS '预警信息';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_status" IS '处理状态 0--未处理  1--已处理';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_status" IS '状态 0--未停止  1--已停止';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."bdata_rule_id" IS '灾害配置id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."point_id" IS '点位';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."value" IS '监测值';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_operation" IS '处置措施';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_persons" IS '处置人';
COMMENT ON TABLE "public"."hazard_warning_alarm_info" IS '灾害类型预警数据表';

-- ----------------------------
-- Table structure for hazard_warning_time_rule
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_time_rule";
CREATE TABLE "public"."hazard_warning_time_rule" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_time_rule_id_seq'::regclass),
  "time_rule" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "condition_value" int8,
  "bdata_rule_id" int4,
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "unit_value" int8
)
;
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."time_rule" IS '规则';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."condition" IS '条件';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."condition_value" IS '数值  单位ms(毫秒)';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."bdata_rule_id" IS '指标id';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."unit" IS '单位';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."unit_value" IS '单位值';

-- ----------------------------
-- Table structure for indicator_system_relative
-- ----------------------------
DROP TABLE IF EXISTS "public"."indicator_system_relative";
CREATE TABLE "public"."indicator_system_relative" (
  "id" int4 NOT NULL DEFAULT nextval('indicator_system_relative_id_seq'::regclass),
  "indicator_id" int4,
  "system_id" int4,
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "type_code" int4
)
;
COMMENT ON COLUMN "public"."indicator_system_relative"."id" IS '主键';
COMMENT ON COLUMN "public"."indicator_system_relative"."indicator_id" IS '指标id';
COMMENT ON COLUMN "public"."indicator_system_relative"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."indicator_system_relative"."system_name" IS '系统名称';
COMMENT ON COLUMN "public"."indicator_system_relative"."type_code" IS '类型代码 1--data预警报警，2--AI预警报警';

-- ----------------------------
-- Table structure for push_channel_mode_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_channel_mode_employee";
CREATE TABLE "public"."push_channel_mode_employee" (
  "id" int4 NOT NULL DEFAULT nextval('push_channel_mode_employee_id_seq'::regclass),
  "employee_id" int8,
  "dept_id" int8,
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "post_code" varchar(255) COLLATE "pg_catalog"."default",
  "post_name" varchar(255) COLLATE "pg_catalog"."default",
  "employee_name" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "push_mode_id" int8,
  "employee_type" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "email_title" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."push_channel_mode_employee"."id" IS '主键自增id';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_id" IS '员工编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."dept_id" IS '部门编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."post_code" IS '岗位编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_name" IS '员工姓名';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."push_mode_id" IS '推送模板id';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_type" IS '0--接收人，1--抄送人';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."email" IS '邮箱';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."email_title" IS '邮件_标题';

-- ----------------------------
-- Table structure for push_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_detail";
CREATE TABLE "public"."push_detail" (
  "id" int8 NOT NULL DEFAULT nextval('push_detail_id_seq'::regclass),
  "user_id" int8,
  "is_send" varchar(255) COLLATE "pg_catalog"."default",
  "push_content" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "push_mode_id" int8,
  "push_reply" varchar(255) COLLATE "pg_catalog"."default",
  "push_user_id" int8,
  "push_jump_code" varchar(255) COLLATE "pg_catalog"."default",
  "push_jump_param" varchar(255) COLLATE "pg_catalog"."default",
  "message_type" varchar(255) COLLATE "pg_catalog"."default",
  "message_level" varchar(255) COLLATE "pg_catalog"."default",
  "push_title" varchar(255) COLLATE "pg_catalog"."default",
  "push_user_name" varchar(255) COLLATE "pg_catalog"."default",
  "remind_type" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "pc_url" varchar(255) COLLATE "pg_catalog"."default",
  "ph_page_type" varchar(255) COLLATE "pg_catalog"."default",
  "ph_url" varchar(255) COLLATE "pg_catalog"."default",
  "count_url" varchar(255) COLLATE "pg_catalog"."default",
  "push_type" varchar(255) COLLATE "pg_catalog"."default",
  "group_pc_url" varchar(255) COLLATE "pg_catalog"."default",
  "group_ph_url" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."push_detail"."id" IS '记录ID';
COMMENT ON COLUMN "public"."push_detail"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."push_detail"."is_send" IS '是否已发送，0--未发送，1--已发送';
COMMENT ON COLUMN "public"."push_detail"."push_content" IS '推送内容';
COMMENT ON COLUMN "public"."push_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."push_detail"."push_mode_id" IS '推送ID';
COMMENT ON COLUMN "public"."push_detail"."push_reply" IS '回复';
COMMENT ON COLUMN "public"."push_detail"."push_user_id" IS '回复用户';
COMMENT ON COLUMN "public"."push_detail"."push_jump_code" IS '跳转路由';
COMMENT ON COLUMN "public"."push_detail"."push_jump_param" IS '跳转参数';
COMMENT ON COLUMN "public"."push_detail"."message_type" IS '消息类型';
COMMENT ON COLUMN "public"."push_detail"."message_level" IS '消息级别';
COMMENT ON COLUMN "public"."push_detail"."push_title" IS '标题';
COMMENT ON COLUMN "public"."push_detail"."push_user_name" IS '读取人姓名';
COMMENT ON COLUMN "public"."push_detail"."remind_type" IS ' 提醒方式 0000：不需要任何提醒； 1000：需要短信提醒； 1100：需要短信和邮箱提醒。';
COMMENT ON COLUMN "public"."push_detail"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."push_detail"."pc_url" IS '消息详情 PC 端 url';
COMMENT ON COLUMN "public"."push_detail"."ph_page_type" IS '消息移动端页面类型（0：淮河能源 APP 内部原 生页面，1：H5 界面(code 登录认证) ，2：H5 界面(无登录认证) ，3： H5 界面(票据登录认证）。），可传空，默认是 3';
COMMENT ON COLUMN "public"."push_detail"."ph_url" IS '消息详情移动端 url';
COMMENT ON COLUMN "public"."push_detail"."count_url" IS '点击消息总数需要跳转到业务系统某个页面的 url';
COMMENT ON COLUMN "public"."push_detail"."push_type" IS '推送方式 1--通知  2--短信 3--通知+短信';
COMMENT ON COLUMN "public"."push_detail"."group_pc_url" IS '集团推送PC端url';
COMMENT ON COLUMN "public"."push_detail"."group_ph_url" IS '集团推送移动端url';

-- ----------------------------
-- Table structure for push_history
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_history";
CREATE TABLE "public"."push_history" (
  "id" int8 NOT NULL DEFAULT nextval('push_history_seq'::regclass),
  "user_id" int8,
  "push_json" varchar(2000) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "push_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_full" varchar(1) COLLATE "pg_catalog"."default",
  "count" varchar(1) COLLATE "pg_catalog"."default",
  "full_message" varchar(2000) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default",
  "group_id" int8,
  "is_handle" varchar(1) COLLATE "pg_catalog"."default",
  "is_read" varchar(1) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "detail_id" int8,
  "type" varchar(1) COLLATE "pg_catalog"."default",
  "update_time" varchar(50) COLLATE "pg_catalog"."default",
  "read_time" varchar(50) COLLATE "pg_catalog"."default",
  "gis_warning_alarm_time" varchar(50) COLLATE "pg_catalog"."default",
  "warning_alarm_info_id" int4
)
;
COMMENT ON COLUMN "public"."push_history"."id" IS '记录ID';
COMMENT ON COLUMN "public"."push_history"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."push_history"."push_json" IS ' 相关项结构化数据';
COMMENT ON COLUMN "public"."push_history"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."push_history"."push_code" IS '推送 的 推送方式 中的代码';
COMMENT ON COLUMN "public"."push_history"."is_full" IS '是否成功 1 是 0 不是';
COMMENT ON COLUMN "public"."push_history"."count" IS '失败计数  默认3次';
COMMENT ON COLUMN "public"."push_history"."full_message" IS '失败描述  "厂家返回的失败描述';
COMMENT ON COLUMN "public"."push_history"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."push_history"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."push_history"."message" IS '备注';
COMMENT ON COLUMN "public"."push_history"."group_id" IS '分组id';
COMMENT ON COLUMN "public"."push_history"."is_handle" IS '是否处理，0--未处理，1--已处理 2--处理中';
COMMENT ON COLUMN "public"."push_history"."is_read" IS '是否已读，0--未读，1--已读';
COMMENT ON COLUMN "public"."push_history"."phone" IS '手机号';
COMMENT ON COLUMN "public"."push_history"."email" IS '邮箱';
COMMENT ON COLUMN "public"."push_history"."detail_id" IS 'detail内容表id';
COMMENT ON COLUMN "public"."push_history"."type" IS '用户类型，0--发送人，1--抄送人';
COMMENT ON COLUMN "public"."push_history"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."push_history"."read_time" IS '读取时间';
COMMENT ON COLUMN "public"."push_history"."gis_warning_alarm_time" IS '预警报警时间';
COMMENT ON COLUMN "public"."push_history"."warning_alarm_info_id" IS '预警报警id';
COMMENT ON TABLE "public"."push_history" IS '推送信息历史表 用于描述 推送信息历史 那个用户什么时候推送过什么数据 推送的中心化结构数据为什么  是否错误 错误重试计数为什么 返回错误数据是什么';

-- ----------------------------
-- Table structure for push_sms
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_sms";
CREATE TABLE "public"."push_sms" (
  "id" int8 NOT NULL DEFAULT nextval('push_sms_id_seq'::regclass),
  "content" varchar(500) COLLATE "pg_catalog"."default",
  "phone" varchar(500) COLLATE "pg_catalog"."default",
  "create_time" varchar(255) COLLATE "pg_catalog"."default",
  "channel_code" varchar(2) COLLATE "pg_catalog"."default",
  "push_code" varchar(2) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."push_sms"."id" IS '主键自增';
COMMENT ON COLUMN "public"."push_sms"."content" IS '短信内容';
COMMENT ON COLUMN "public"."push_sms"."phone" IS '发送手机号';
COMMENT ON COLUMN "public"."push_sms"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."push_sms"."channel_code" IS '"A";  /// 联通
"B";  /// 短信机
"C";  /// 业务内部通知
"D";  /// 通知--极光推送
"E";  /// 融合推送
"F";  /// 阿里云    ';
COMMENT ON COLUMN "public"."push_sms"."push_code" IS '"A";  /// 短信
"B";  /// 邮件
"C";  /// 业务通知
"D";  /// 通知--极光推送 ';

-- ----------------------------
-- Table structure for send_account
-- ----------------------------
DROP TABLE IF EXISTS "public"."send_account";
CREATE TABLE "public"."send_account" (
  "id" int8 NOT NULL DEFAULT nextval('send_account_seq'::regclass),
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."send_account"."id" IS '记录ID';
COMMENT ON COLUMN "public"."send_account"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."send_account"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."send_account"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."send_account"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."send_account"."message" IS '备注';
COMMENT ON TABLE "public"."send_account" IS '发送方账号信息表 用于存储或者配置发送方商户账号信息体';

-- ----------------------------
-- Table structure for system_duration
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_duration";
CREATE TABLE "public"."system_duration" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "duration" int8
)
;
COMMENT ON COLUMN "public"."system_duration"."id" IS '主键';

-- ----------------------------
-- Table structure for user_account
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_account";
CREATE TABLE "public"."user_account" (
  "id" int8 NOT NULL DEFAULT nextval('user_account_seq'::regclass),
  "user_id" int4,
  "oss_id" int4,
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."user_account"."id" IS '记录ID';
COMMENT ON COLUMN "public"."user_account"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."user_account"."oss_id" IS '平台编号ID';
COMMENT ON COLUMN "public"."user_account"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."user_account"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."user_account"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."user_account"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."user_account"."message" IS '备注';
COMMENT ON TABLE "public"."user_account" IS '用户账号信息表 用于 用户 哪一个渠道的 推送渠道、账户的相关信息 核心为结构化数据';

-- ----------------------------
-- Table structure for warning_channel_classify
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_channel_classify";
CREATE TABLE "public"."warning_channel_classify" (
  "id" int4 NOT NULL DEFAULT nextval('warning_channel_classify_id_seq'::regclass),
  "type_name" varchar(255) COLLATE "pg_catalog"."default",
  "type_code" int4
)
;
COMMENT ON COLUMN "public"."warning_channel_classify"."id" IS '主键id';
COMMENT ON COLUMN "public"."warning_channel_classify"."type_name" IS '类型名称';
COMMENT ON COLUMN "public"."warning_channel_classify"."type_code" IS '类型代码 1--data预警报警，2--AI预警报警';
COMMENT ON TABLE "public"."warning_channel_classify" IS '报警渠道分类';

-- ----------------------------
-- Table structure for warning_rule_issue
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_rule_issue";
CREATE TABLE "public"."warning_rule_issue" (
  "id" int4 NOT NULL,
  "indictor_id" int4,
  "point_id" varchar(500) COLLATE "pg_catalog"."default",
  "warning_rule_id" int4,
  "start_time" varchar COLLATE "pg_catalog"."default",
  "end_time" varchar COLLATE "pg_catalog"."default" DEFAULT nextval('waring_rule_issue_id_seq'::regclass)
)
;
COMMENT ON COLUMN "public"."warning_rule_issue"."id" IS '主键';
COMMENT ON COLUMN "public"."warning_rule_issue"."indictor_id" IS '指标id';
COMMENT ON COLUMN "public"."warning_rule_issue"."point_id" IS '点位id';
COMMENT ON COLUMN "public"."warning_rule_issue"."warning_rule_id" IS '规则id';
COMMENT ON COLUMN "public"."warning_rule_issue"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."warning_rule_issue"."end_time" IS '结束时间';

-- ----------------------------
-- Table structure for warning_time_rule
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_time_rule";
CREATE TABLE "public"."warning_time_rule" (
  "id" int4 NOT NULL DEFAULT nextval('warning_time_rule_id_seq'::regclass),
  "time_rule" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "condition_value" int8,
  "indicator_id" int4,
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "unit_value" int8
)
;
COMMENT ON COLUMN "public"."warning_time_rule"."time_rule" IS '规则';
COMMENT ON COLUMN "public"."warning_time_rule"."condition" IS '条件';
COMMENT ON COLUMN "public"."warning_time_rule"."condition_value" IS '数值  单位ms(毫秒)';
COMMENT ON COLUMN "public"."warning_time_rule"."indicator_id" IS '指标id';
COMMENT ON COLUMN "public"."warning_time_rule"."unit" IS '单位';
COMMENT ON COLUMN "public"."warning_time_rule"."unit_value" IS '单位值';

-- ----------------------------
-- Primary Key structure for table ai_model_system
-- ----------------------------
ALTER TABLE "public"."ai_model_system" ADD CONSTRAINT "ai_model_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_alert_push_mid
-- ----------------------------
ALTER TABLE "public"."bdata_alert_push_mid" ADD CONSTRAINT "bdata_alert_push_mid_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_rule_push_mid
-- ----------------------------
ALTER TABLE "public"."bdata_rule_push_mid" ADD CONSTRAINT "bdata_rule_push_mid_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table gis_warning_alarm_info
-- ----------------------------
CREATE UNIQUE INDEX "pk_warning_alarm_info" ON "public"."gis_warning_alarm_info" USING btree (
  "warning_alarm_info_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table hazard_handle_employee
-- ----------------------------
ALTER TABLE "public"."hazard_handle_employee" ADD CONSTRAINT "hazard_handle_employee_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table hazard_warning_alarm_handle
-- ----------------------------
ALTER TABLE "public"."hazard_warning_alarm_handle" ADD CONSTRAINT "hazard_warning_alarm_handle_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table hazard_warning_time_rule
-- ----------------------------
ALTER TABLE "public"."hazard_warning_time_rule" ADD CONSTRAINT "hazard_warning_time_rule_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table indicator_system_relative
-- ----------------------------
ALTER TABLE "public"."indicator_system_relative" ADD CONSTRAINT "indicator_system_relative_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table push_sms
-- ----------------------------
ALTER TABLE "public"."push_sms" ADD CONSTRAINT "push_sms_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table send_account
-- ----------------------------
ALTER TABLE "public"."send_account" ADD CONSTRAINT "send_account_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table system_duration
-- ----------------------------
ALTER TABLE "public"."system_duration" ADD CONSTRAINT "system_duration_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table warning_channel_classify
-- ----------------------------
ALTER TABLE "public"."warning_channel_classify" ADD CONSTRAINT "warning_channel_classify_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table warning_rule_issue
-- ----------------------------
ALTER TABLE "public"."warning_rule_issue" ADD CONSTRAINT "waring_rule_issue_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table warning_time_rule
-- ----------------------------
ALTER TABLE "public"."warning_time_rule" ADD CONSTRAINT "warning_time_rule_pkey" PRIMARY KEY ("id");




--初始化sql语句
INSERT INTO public.ai_model_system (id, "name") VALUES('888888', 'AI预警报警系统');

INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(0, '子系统存活', 87, true, '子系统存活报警', '子系统断线超30分钟报警', '预警信息推送给信息中心负责人，尽快查明信号中断的原因，并及时恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(1, '水文参数', 30, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±0.2m/h。', '预警信息推送给地测科负责人，安排人员对观测孔进行实地勘察，对传感器进行校验，查明参数变化原因，及时采取相关措施，保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(2, '水文参数', 30, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±5m/天。', '预警信息推送给地测科负责人，安排人员对观测孔进行实地勘察，对传感器进行校验，查明参数变化原因，及时采取相关措施，保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(3, '矿井涌水量', 30, true, '矿井及区域涌水量变化异常', '矿井及区域涌水量 ≥80 并 ＜120', '预警信息推送给地测科负责人，安排人员对涌水量异常区进行实地测量，查明异常原因，及时采取相关防治水措施，保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(4, '地表水位标高', 30, true, '地表水位标高超过预警阈值', '地表水位标高超过预警阈值', '预警信息推送给地测科负责人，安排人员对地表水流进行实地勘察，查明水位标高升高原因，并采取相应措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(5, '监测通讯', 23, true, '通信状态', '信号中断>=30分钟', '预警信息推送给信息中心负责人，尽快查明信号中断的原因，并及时恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(6, '安全监测超限报警', 23, true, '超限报警', '超限时长小于20分钟', '预警信息推送给通防科、信息中心负责人，尽快查明超限原因，并及时解决恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(7, '人员定位超员', 23, true, '人员定位超员', '超过核定人数1000人', '预警信息推送给信息中心负责人，及时查明超员原因，尽快疏散人群。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(8, '人员定位超时', 23, true, '人员下井超时', '人员下井超过10小时。', '预警信息推送给信息中心负责人、调度室负责人及时联系井下超时人员及时上井。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(9, '水文监测超限', 23, true, '水文监测指标超限', '水文监测指标超过预警值，未超过报警值。', '预警信息推送给地测科负责人，尽快查明超限原因，计时解决恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(10, '皮带机检查', 27, true, '电机温度异常', '电机温度大于75摄氏度', '预警信息推送给机电科负责人，并建议检查电机的电流电压是否正常；是否有过载情况。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(11, '皮带机检查', 27, true, '减速机温度异常', '减速机温度大于75摄氏度', '预警信息推送给机电科负责人，并建议检查减速机的轴承及润滑情况；是否有过载情况', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(12, '皮带机检查', 27, true, '滚筒温度异常', '滚筒温度大于60度', '预警信息推送给机电科负责人，并建议检查滚筒润滑部位情况。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(13, '皮带机检查', 27, true, '电机振动异常', '振动传感器超过10mm/s', '预警信息推送给机电科负责人，并建议检查电机固定是否牢固，结合电流和负载情况进一步检查', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(14, '人员定位区域超员', 28, true, '超过报警值', '超过报警值', '推送信息给相关负责人，及时处理', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(15, '煤质化验', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(16, '调度排班', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(17, '调度日报', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(18, '生产月报', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(19, '生产年报', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(20, '回采月度收尺', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(21, '掘进月度收尺', 31, true, '数据未填报', '临近日期未填报，或到期未填报', '预警信息推送给调度室管理人员，及时安排调度员及相关负责人及时对数据进行填报。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(22, '压风机检查', 26, true, '排气温度过高', '排气温度超过100℃', '消息推送相关负责人，并建议检查机组冷却液是否液位过低；冷却器脏、堵；其他电气故障。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(23, '压风机检查', 26, true, '排气压力过大', '排气压力大于7.2', '消息推送相关负责人，并建议检查机组冷却液是否液位过高；冷却器脏、制冷系统有空气；其他电气故障。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(24, '压风机检查', 26, true, '主机温度过高', '主机温度大于108', '消息推送相关负责人，并建议检查冷却器是否堵塞，温控阀是否打开', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(25, '瓦斯涌出', 29, false, '瓦斯涌出明显异常', '甲烷数据异常', '预警信息推送给通防科负责人，联系井下瓦检工对传感器进行校验，查明现场浓度异常原因，及时采取措施保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(26, '瓦斯涌出', 29, false, '瓦斯涌出明显异常', '甲烷数据异常', '预警信息推送给通防科负责人，联系井下瓦检工对传感器进行校验，查明现场浓度异常原因，及时采取措施保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(27, 'co浓度超限', 29, true, 'co浓度异常', 'co浓度脱离稳定值持续上升超过10分钟或者超过24ppm。', '预警消息推送至调度室、安全科及通防科负责人，立刻通知现场作业人员，开启压风管路加强通风。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(28, '水泵试运转', 24, true, '水泵未联合试运转', '1年内没有出现所有水泵同时运行且运行时间大于5分钟（2月到6月）6月底统计报警。', '预警信息推送给机电科负责人，按照制定的联合试运转计划进行水泵的联合试运转，并出具试运转报告。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(29, '水泵效率', 24, true, '水泵效率低', '水泵效率低于额定值的50%', '预警信息推送给机电科负责人，查明水泵效率低于限定值的原因，尽快恢复水泵正常工作。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(30, '水泵电源', 24, true, '主排水泵房某一回路电源失电', '某一回路上级电源开关断电时间大于4小时', '预警信息推送给机电科负责人，查明电源断电的原因，尽快恢复水泵供电，启动备用水泵。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(31, '主通风机检查', 25, true, '主风机未反风', '年底1年内无风机“反风操作”“反向风门状态为1”操作且负压计为正压情况持续时间不超过1小时。', '预警信息推送给通防科负责人，按照制定的反风计划及时进行反风演练，并作出反风报告。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(32, '主通风机检查', 25, true, '主风机未运行', '单回路进线柜电压值为0大于2小时', '预警信息推送给机运科负责人，查明主风机停机原因，启动备用风机，并及时后恢复主风机运行状态', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(33, '主通风机检查', 25, true, '主风机电机温度异常', '非风机倒台情况下风机电机绕组温度比前一日同时期温度高15℃或大于105℃（电机一直有电流情况下）', '预警信息推送给机运科负责人，查明主风机电机温度异常的原因，及时采取措施已恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(34, '主通风机检查', 25, true, '主风机轴承温度异常', '非风机倒台情况下风机轴承温度比前一日同时期温度高15℃或大于75℃（电机一直有电流情况下）', '预警信息推送给机运科负责人，查明主风机轴承温度异常的原因，及时采取措施已恢复轴承正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(35, '轴承检查', 25, true, '轴承温度', '轴承温度大于60度，小于75度', '预警信息推送给机运科负责人，查明主风机电机温度异常的原因，及时采取措施已恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(36, '绕组检查', 25, true, '绕组温度', '温度大于95度，小于110度', '预警信息推送给机运科负责人，查明主风机电机绕组温度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(37, '震动检查', 25, true, '震动幅度', '幅度高于4.2mm，小于5mm', '预警信息推送给机运科负责人，查明震动幅度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(38, '自动化通讯', 22, false, '数据上传状态', '信号中断小于30分钟。', '预警信息推送给信息中心负责人，尽快查明信号中断的原因，并及时恢复。', NULL, true, '2021-11-23 10:00:00', 2, '000100010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(39, '设备故障停机', 22, true, '自动化设备因故障停机预警提醒。', '超过30分钟进行预警提醒。', '预警信息推送给机电科负责人，尽快安排人查明停机原因。', NULL, true, '2021-11-23 10:00:00', 2, '000100010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(40, '电力系统检查', 33, true, '电气春季预防性试验未进行', '高压进线馈电没有大于30分钟停电记录', '报警信息已推送给机电科负责人，按照制定的计划进行电气预防性试验，并保证正常供电。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(41, '电力系统检查', 33, true, '低压漏电试验未正常进行', '0点至24点之间，超过4台没有报漏电故障', '报警信息已推送给机电科负责人，按照制定的计划进行低压漏电试验，并保证正常供电。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(42, '电力系统检查', 33, true, '全矿井电源失电', '所有进线柜断路器状态为分闸状态大于10分钟', '报警信息已推送给机电科负责人，安排人员排查电源失电原因，尽快修复矿井电源供电。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(43, '电力系统检查', 33, true, '矿井单回路电源失电', 'I段或II段所有的进线断路器为分闸状态大于8小时', '报警信息已推送给机电科负责人，安排人员及时排查矿井单回路失电原因，启动其他回路供电。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(44, '架空装置检查', 34, true, '架空乘人装置断绳', '架空乘人装置断绳', '报警信息已推送给机电科负责人，安排人员及时排查现场情况，根据情况确定是否需要更换。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(45, '架空装置检查', 34, true, '架空乘人装置断轴', '架空乘人装置断轴', '报警信息已推送给机电科负责人，安排人员及时排查现场情况，根据情况确定是否需要更换。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(46, '架空装置检查', 34, true, '钢丝绳断绳', '钢丝绳断绳', '报警信息已推送给机电科负责人，安排人员及时排查现场情况，根据情况确定是否需要更换。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(47, '主煤流检查', 35, true, '主煤流皮带保护试验未正常进行', '3天内保护没有动作（打滑、跑偏、堆煤、撕裂、张力下降、沿线急停保护）', '报警信息已推送给机电科负责人，安排人员及时排查现场情况，根据情况确定是否需要更换。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(48, '主煤流检查', 35, true, '主煤流皮带保护试验未正常进行', '6天内保护没有动作（打滑、跑偏、堆煤、撕裂、张力下降、沿线急停保护）', '报警信息已推送给机电科负责人，安排人员及时排查现场情况，根据情况确定是否需要更换。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(49, '泵房电源', 36, true, '主排水泵房所有回路电源失电', '上级所有电源开关均断电', '报警信息已推送给机电科负责人，及时排查回路电源失电原因，尽快恢复电源供电，保证正常生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(50, '主通风机检查', 37, true, '主通风机双停', '两台风机均停机持续时间>10min', '报警信息已推送给通防科负责人，及时安排人员对主通风机停机情况进行排查，启动备用风机，尽快恢复通风机运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(51, '主通风机检查', 37, true, '主通风机双停', '两台风机均停机持续时间>30min', '报警信息已推送给通防科负责人，及时安排人员对主通风机停机情况进行排查，启动备用风机，尽快恢复通风机运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(52, '轴承温度超限', 37, true, '轴承温度超过限制', '轴承温度大于75°，持续时间超过5min。', '报警信息推送给机电科负责人，查明主风机轴承温度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(53, '绕组温度超限', 37, true, '绕组温度超过限制', '温度超过110°，持续时间大于5min。', '报警信息推送给机电科负责人，查明主风机绕组温度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(54, '震动幅度超限', 37, true, '主电机震动幅度', '震动幅度超过5mm，超限时间大于5min。', '报警信息推送给机电科负责人，查明主风机电机震动幅度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(55, '稀油站温度超限', 37, true, '稀油站温度异常', '温度≤45°或者温度≥55°', '报警信息推送给机电科负责人，查明稀油站温度异常的原因，及时采取措施恢复电机正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(56, '自动化通讯', 39, true, '信号上传状态', '信号中断大于等于30分钟。', '报警信息推送给信息中心负责人，尽快查明信号中断的原因，并及时恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(57, '人员定位超员报警', 40, true, '超过报警值850人', '超过报警值850人', '推送信息给相关负责人，及时处理', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(58, '安全监测超限', 40, true, '超限报警', '超限时长大于等于20分钟', '报警信息推送给通防科、信息中心负责人。尽快查明超限原因，并及时解决恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(59, '人员定位区域超员', 40, true, '区域超员', '超过区域核定人员数', '报警信息推送给信息中心负责人，及时查明超员原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(60, '人员定位超时', 40, true, '人员下井超时', '人员下井超过12小时。', '报警信息推送给信息中心、调度室负责人。尽快联系井下超时人员及时上井。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(61, '水文监测指标超限', 40, true, '水文监测指标超限', '水文监测指标超过报警值', '报警信息推送给地测科负责人，尽快查明超限原因解决问题。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(62, '监测通讯', 40, true, '通信中断', '监测信号中断大于等于30分钟', '报警信息推送给信息中心负责人，尽快查明信号中断原因，并及时恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(63, '水文参数', 41, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±0.3m/h。（焦煤公司变化幅度为≥±4.5m/h）', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明水位异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(64, '水文参数', 41, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±10m/天。（焦煤公司变化幅度为≥±200m/天）', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明水位异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(65, '水文参数', 41, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±0.5m/h。（焦煤公司变化幅度为≥±5.5m/h）', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明水位异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(66, '水文参数', 41, true, '水文观测系统参数变化异常', '矿井生产期间正常情况下，主要含水层水位变化幅度≥±15m/天。（焦煤公司变化幅度为≥±250m/天）', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明水位异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(67, '矿井涌水量', 41, true, '矿井及区域涌水量变化异常', '矿井及区域涌水量 ≥120', '报警信息已推送给地测科负责人，及时安排人员对现场情况进行勘察，查明涌水量变化原因，及时采取相关防治水措施，启动应急排水，以保证正常排水。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(68, '瓦斯超限', 42, true, '瓦斯超限', '2台以上传感器甲烷气体监测浓度同时>3.0%。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(69, '瓦斯超限', 42, true, '瓦斯超限', '1台甲烷气体监测浓度>1%，且持续时间>1min。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(70, '瓦斯超限', 42, true, '瓦斯超限', '1台甲烷气体监测浓度>1.5%，且持续时间>10min。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(71, '疑似突出', 42, true, '疑似突出', '1台甲烷浓度>5%，持续时间>5min，且同一地点风向传感器（逆风）报警。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(72, '疑似突出', 42, true, '疑似突出', '矿井多点（2个以上传感器）甲烷气体监测浓度在10%以上，且同一地点风向传感器（逆风）报警。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(73, '瓦斯排放', 42, true, '瓦斯排放一风吹', '矿井排放瓦斯地点，2个甲烷传感器浓度>3%', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(74, '瓦斯涌出', 42, true, '瓦斯涌出', '连续10min，矿井多地点（2个以上传感器）甲烷气体监测浓度>3.0%。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(75, '瓦斯异常停产', 43, true, '瓦斯异异常停产', '同一工作面瓦斯涌出量（甲烷浓度）连续三天出现二次以上浓度超过1%的', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(76, 'co浓度异常', 43, true, 'co浓度异常。', 'co浓度超过24ppm，并持续上升超过50ppm,持续时长超过20分钟。', '报警信息推送给值班矿领导及总工程师，应责令控制放煤量停止该区域内的生产活动，待数据正常后方可恢复生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(77, '矿井停风', 44, true, '矿井停风', '2台矿井主扇停电（断电）状态>30min，且风机负压下降50%以上', '报警信息已推送给通防科负责人，及时安排人员对主通风机停机情况进行排查，尽快恢复通风机运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(78, '矿井停风', 44, true, '矿井停风', '2台矿井主扇停电（断电）状态>10min，且风机负压下降50%以上', '报警信息已推送给通防科负责人，及时安排人员对主通风机停机情况进行排查，尽快恢复通风机运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(79, '通风堵塞', 44, true, '主要通风巷道堵塞', '主要通风机工作主扇风量下降20%，和电流和风机负压同时上升>20%', '报警信息已推送给通防科负责人，及时排查主扇风量下降的原因，查明通风巷道堵塞情况，尽快恢复正常通风条件。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(80, '抽采管路', 45, true, '瓦斯抽采管路损坏', '连续30min内，矿井抽采管路瓦斯监控浓度升高50%或下降50%，且抽采主管路负压下降<5kpa', '报警信息已推送给通防科负责人，安排专业人员排查设备情况，尽快恢复正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(81, '抽采系统', 45, true, '瓦斯抽采系统停运', '矿井地面瓦斯抽采泵出现停电4小时以上，且抽采主管路负压下降<5kpa', '报警信息已推送给通防科负责人，安排专业人员排查设备情况，尽快恢复正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(82, '抽采系统', 45, true, '瓦斯抽采系统停运', '矿井地面瓦斯抽采泵出现停电2小时以上，且抽采主管路负压下降<5kpa', '报警信息已推送给通防科负责人，安排专业人员排查设备情况，尽快恢复正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(83, 'CO超限', 46, true, 'CO超限', '1台CO传感器浓度大于500ppm，且持续时间超过30min', '报警信息已推送给通防科负责人，安排专业人员排查设备情况，尽快恢复正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(84, 'CO超限', 46, true, 'CO超限', '1台CO传感器浓度大于100ppm，持续时间超过30min', '报警信息已推送给通防科负责人，安排专业人员排查设备情况，尽快恢复正常运转。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(85, '监测监控', 47, true, '传感器数值超限', '传感器数值报警（甲烷，温度，开停，风门等）时长超过20分钟', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(86, '监测通讯', 47, true, '包含所有监测监控的上传信号', '断线>30分钟', '报警信息推送给信息办负责人，尽快查明信号中断的原因，并及时恢复。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(87, '交换机网络', 47, true, '交换机网络中断', '网络中断时长超过30分钟。', '报警信息推送给智能办负责人，尽快排查原因、解决问题。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(88, '自动化设备故障停机', 47, true, '自动化设备故障停机', '停机时间超过120分钟', '消息推送给机运科负责人，及时排查停机原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(89, '揭煤瓦斯', 48, true, '揭煤瓦斯超限', '揭煤地点，连续10min内，矿井多点（2个以上传感器）甲烷气体监测浓度在10%以上，且同一地点风向传感器（逆风）报警。', '报警信息已推送给通防科负责人，及时安排人员对现场传感器进行校验，查明现场气体异常的原因，及时采取防止措施。', NULL, NULL, '2021-11-23 10:00:00', 2, '000200030007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(90, '备件库', 49, true, '备件库库存不足', '备件库库存备件存在个数为0，消耗类低于库存80%', '预警信息已推送给机电科负责人，安排人员排查备件库库存情况，对不足备件及时提交材料计划，及时补充。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(91, '设备故障', 50, true, '大型设备出现故障', '大型设备故障报警提示。', '预警信息已推送给机电科负责人，及时安排人员对设备故障报警情况进行排查，查明故障原因，及时解决问题，保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(92, '设备库存', 51, true, '设备库设备库存不足', '设备库设备库存存在数量为0，主要消耗类低于库存80%', '预警信息已推送给机电科负责人，安排人员排查设备库库存情况，对不足设备及时提交材料计划，及时补充。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(93, '机电任务计划', 52, true, '未执行任务计划', '管理预警消息发出后，未更新任务计划回执。', '预警信息推送给任务责任人，及时反馈任务进度。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(94, '设备检验', 53, true, '设备检测检验到期（包括各类大型设备的定期检测检验，各部件功能的检测检验、各类设备的日常检修保养）', '各设备定期检测到期前1个月前，仍未更新检测信息。', '预警信息已推送给机电科负责人，及时安排人员联系相关单位对设备进行检测检验，并更新最新的检测检验信息。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(95, '矿井测风', 54, true, '矿井未定期测风', '10天内测风人员未进入矿井回风巷区域，或在回风巷停留不到10min。', '预警信息已推送给通防科负责人，及时安排人员对主要地区进行测风作业。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(96, '矿井反风', 54, true, '未进行反风演习', '1年内未出现风向传感器出现大于1个反风。', '预警信息已推送给通防科负责人，按照既定计划进行反风演练，并出具演习报告。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(97, '传感器调校', 55, true, '传感器未定期调校', '甲烷传感器调校超期提醒，甲烷传感器连续15天，未进行调校', '预警信息已推送给通防科负责人，及时安排人员对传感器进行调校。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(98, '防突验证', 56, true, '连续3天未进行区域验证', '突出矿井连续3天，“防突工”未进入煤巷掘工作面或采煤面。', '预警信息已推送给通防科负责人，及时安排防突工对工作面进行区域验证。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(99, '防突校检', 56, true, '防突效检人员未做到“三同时”', '防突效检人员做到“三同时”在工作面现场', '预警信息已推送给通防科负责人，安排防突校检人员在工作面现场。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(100, '揭煤管理', 56, true, '揭煤管理关键环节管控不到位', '副总工程师或防突矿长未在现场', '预警信息推送给通防科、调度室负责人，通知副总或负责矿长及时到达现场指挥作业。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(101, '放炮撤人', 57, true, '放炮不撤人', '"爆破作业时，启爆后粉尘超限、CO超限，高突矿井作业人员同时进出工作面时间间隔不足30min。 低瓦斯矿井间隔不足15min。"', '预警信息推送给工区管理、调度室负责人，通知现场人员按照规定要求进行放炮撤人，待炮烟吹散后方可进入。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(102, '爆破管理', 57, true, '爆破未执行三人连锁', '启爆后粉尘超限、CO超限，“班长”、“瓦斯检查员”、“放炮员”人员轨迹不一致。', '预警信息已推送给工区管理、调度室负责人，通知当班各类管理人员按照三人连锁放炮制进行放炮作业。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(103, '通风管理', 55, true, '通风管理关键环节管控不到位', '副总工程师或矿领导未在现场', '预警信息已推送给通防科、调度室负责人，及时安排相关领导抵达现场。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(104, '瓦斯管理', 55, true, '通风管理关键环节管控不到位', '通风队长或副总工程师未在现场', '预警信息已推送给通防科、调度室负责人，及时安排相关领导抵达现场。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(105, '超前物探', 59, true, '未进行超前钻探', '接近超前物探范围', '预警信息已推送给地测科负责人，及时进行超前物探作业，及时更新物探成果内容。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(106, '防治水台账', 59, true, '各类防治水台账未及时更新', '到期更新文档内容', '预警信息已推送给地测科负责人，及时更新台账内容。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(107, '隐患', 60, true, '出现较大隐患，隐患数量持续上升', '出现较大隐患，以及每天的隐患数量成持续上升趋势。', '预警信息已推送给安全科负责人，排查隐患出现原因，采取相关措施保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(108, '超期隐患', 60, true, '存在超期未整改隐患', '超期隐患<=1天', '预警信息已推送给安全科负责人，排查超期隐患，采取相关措施解决后保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(109, '三违增长率', 61, true, '三违个数', '本周三违增长率大于5%小于10%', '预警信息已推送给安全科负责人，排查三违出现的原因，采取相关促使，以保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(111, '科技成果管理', 63, true, '科技成果管理', '科技成果流程超期未处理', '预警信息已推送给生产科负责人，及时对流程进行处理。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(112, '井下超时', 64, true, '井下超时', '井下人员超时超过12小时', '预警信息已推送给信息科负责人，查明超时人员信息，尽快联系当事人，确认超时原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(113, '质量标准化', 65, true, '质量标准化（得分，评分）', '标准化得分低于90分', '预警信息已推送给相关专业负责人，排查评分较低的原因，及时修正。', NULL, NULL, '2021-11-23 10:00:00', 2, '000300040006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(114, '百人违章率', 66, true, '百人违章率', '百人违章率超过**、', '预警信息已推送给安全科负责人，查明违章率超限的原因，对好职工的安全生产意识。', NULL, true, '2021-11-23 10:00:00', 2, '000300040007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(115, '用电管理', 67, true, '用电量', '当前季度的三个月的用电量超过季度计划用电量', '预警信息推送给机运科负责人', NULL, NULL, '2021-11-23 10:00:00', 2, '000300050001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(116, '在库设备数量', 68, true, '在库设备数量', '在库设备数量低于或等于预警数量', '预警信息推送给机运科负责人', NULL, NULL, '2021-11-23 10:00:00', 2, '000300050002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(117, '电缆在库米数', 69, true, '电缆的在库米数', '电缆的在库米数低于或等于预警米数', '预警信息推送给机运科负责人', NULL, NULL, '2021-11-23 10:00:00', 2, '000300050003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(118, '电机车数量', 70, true, '电机车数量', '电机车数量小于或等于预警数量', '预警信息推送给机运科负责人', NULL, NULL, '2021-11-23 10:00:00', 2, '000300050004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(119, '充电机数量', 70, true, '充电机数量', '充电机数量小于或等于预警数量', '预警信息推送给机运科负责人', NULL, NULL, '2021-11-23 10:00:00', 2, '000300050004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(120, '地面场所检查', 71, true, '机电科科长未正常上岗', '少于1次/月', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(121, '地面场所检查', 71, true, '机电副总未正常上岗', '少于1次/月', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(122, '地面场所检查', 71, true, '机电副矿长未正常上岗', '少于1次/月', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(123, '电力场所检查', 72, true, '机电科科长未正常上岗', '矿井变电站特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(124, '电力场所检查', 72, true, '机电副总未正常上岗', '矿井变电站特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(125, '电力场所检查', 72, true, '机电副矿长未正常上岗', '矿井变电站特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(126, '主排水检查', 73, true, '机电科科长未正常上岗', '主排水系统特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(127, '主排水检查', 73, true, '机电副总未正常上岗', '主排水系统特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(128, '主排水检查', 73, true, '机电副矿长未正常上岗', '主排水系统特定人员轨迹一月小于1次*地点', '报警信息已推送给机电科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(129, '设备检修计划', 74, true, '填写检修记录', '管理报警消息发出后未及时添加设备检修记录', '报警消息推送给检修责任人，及时对设备进行检修和添加检修记录。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(130, '填写任务计划', 74, true, '填写任务进度', '管理报警消息发出后未及时添加任务进度记录', '报警消息已推送给任务责任人，及时执行任务并添加任务进度记录。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(131, '通风管理', 75, true, '通风管理关键环节管控不到位', '副总工程师或矿领导未在现场', '报警信息已推送给通防科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(132, '瓦斯管理', 75, true, '通风管理关键环节管控不到位', '通风队长或副总工程师未在现场', '报警信息已推送给通防科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(133, '防突验证', 76, true, '连续3天未进行区域验证', '突出矿井连续3天，“防突工”未进入煤巷掘工作面或采煤面。', '报警信息已推送给通防科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(134, '揭煤管理', 76, true, '揭煤管理关键环节管控不到位', '副总工程师或防突矿长未在现场', '报警信息已推送给通防科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(135, '防突校检', 76, true, '防突效检人员未做到“三同时”', '防突效检人员做到“三同时”在工作面现场', '报警信息已推送给通防科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(136, '放炮撤人', 77, true, '放炮不撤人', '"爆破作业时，启爆后粉尘超限、CO超限，高突矿井作业人员同时进出工作面时间间隔不足30min。 低瓦斯矿井间隔不足15min。"', '预警信息推送给工区管理、调度室负责人，通知现场人员按照规定要求进行放炮撤人，待炮烟吹散后方可进入。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(137, '爆破管理', 77, true, '爆破未执行三人连锁', '启爆后粉尘超限、CO超限，“班长”、“瓦斯检查员”、“放炮员”人员轨迹不一致。', '预警信息已推送给工区管理、调度室负责人，通知当班各类管理人员按照三人连锁放炮制进行放炮作业。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400020003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(138, '区域管控', 78, true, '重点管控区域未到岗（1）', '重点探放水区域或重点防治水管控区域，地测部门主要领导（钻探队长、地测科长、地测副总工程师）到现场次数少于3次/月。', '报警信息已推送给地测科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(139, '区域管控', 78, true, '重点管控区域未到岗（2）', '重点探放水区域或重点防治水管控区域，地测部门主要领导（钻探队长、地测科长、地测副总工程师、总工程师）到现场次数少于1次/月。', '报警信息已推送给地测科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(140, '突水事故处置', 78, true, '突水事故领导未到岗', '发生突水事故后，矿领导24小时内未到现场参与抢险指挥。地测部门主要领导（钻探队长、地测科长、地测副总工程师、总工程师）均未下井查明突水情况或参与抢险救援。', '报警信息已推送给地测科、调度室负责人，及时安排矿领导正常上岗巡查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400030001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(141, '本日产量', 79, true, '当日产量未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(142, '本周产量', 79, true, '周产量未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(143, '本周产量', 79, true, '周产量未完成计划', '<80%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(144, '本月产量', 79, true, '当月产量未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(145, '本月产量', 79, true, '当月产量未完成计划', '<85%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(146, '本月产量', 79, true, '当月产量未完成计划', '<80%', '报警信息已推送给调度室负责人，安排人员对产量情况进行二次统计，确认各工区工作面产量情况，排查产量欠产原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(147, '本日进尺', 80, true, '当日总进尺、开拓进尺未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(148, '本周进尺', 80, true, '当周总进尺、开拓进尺未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(149, '本周进尺', 80, true, '当周总进尺、开拓进尺未完成计划', '<85%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(150, '本月进尺', 80, true, '当月总进尺、开拓进尺未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(151, '本月进尺', 80, true, '当月总进尺、开拓进尺未完成计划', '<85%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(152, '本月进尺', 80, true, '当月总进尺、开拓进尺未完成计划', '<80%', '报警信息已推送给调度室负责人，安排人员对进尺情况进行二次统计，确认各工区工作面进尺情况，排查进尺延后的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(153, '本月销售', 81, true, '商品煤销量当月未完成计划', '<90%', '报警信息已推送给调度室负责人，安排人员对商品煤销量进行二次统计，确认商品煤销量欠销原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(154, '本月销售', 81, true, '商品煤销量当月未完成计划', '<85%', '报警信息已推送给调度室负责人，安排人员对商品煤销量进行二次统计，确认商品煤销量欠销原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(155, '本月销售', 81, true, '商品煤销量当月未完成计划', '<80%', '报警信息已推送给调度室负责人，安排人员对商品煤销量进行二次统计，确认商品煤销量欠销原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(156, '本月库存', 82, true, '煤炭库存报警', '>60%', '报警信息已推送给调度室负责人，安排人员对煤炭库存情况进行二次统计，确认库存不足及超限的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(157, '本月库存', 82, true, '煤炭库存报警', '>80%', '报警信息已推送给调度室负责人，安排人员对煤炭库存情况进行二次统计，确认库存不足及超限的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(158, '本月库存', 82, true, '煤炭库存报警', '>90%', '报警信息已推送给调度室负责人，安排人员对煤炭库存情况进行二次统计，确认库存不足及超限的原因。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400040004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(159, '隐患', 83, true, '隐患', '发生重大隐患，隐患超期未整改', '报警信息已推送给安全科负责人，对出现重大隐患的责任人进行重点教育，对超期未整改的隐患及时进行整改。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400050001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(160, '超期隐患', 83, true, '存在超期隐患', '超期隐患>1天<=3天', '预警信息已推送给安全科负责人，排查超期隐患，采取相关措施解决后保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400050001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(161, '红线三违', 84, true, '三违', '发生红线三违', '报警信息已推送给安全科负责人，对出现红线三违的人员进行重点教育管控，确保现场生产安全意识。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400050002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(162, '三违增长率', 84, true, '三违个数', '本周增长率大于10%', '预警信息已推送给安全科负责人，排查三违增长率过大原因，采取相关措施保证安全生产。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400050002', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(163, '井下超员', 85, true, '超员', '井下超员（区分交接班时间）', '报警信息已推送给机电科负责人，安排人员对井下人员进行排查，确定超员原因，及时安排人员进行疏散。', NULL, NULL, '2021-11-23 10:00:00', 2, '000400050003', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(164, '二主井提升东车', 86, true, '二主井提升东车故障、报警', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395971, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(165, '二主井提升西车', 86, true, '二主井提升西车故障、报警', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395972, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(166, '一副井提升单罐', 86, true, '一副井提升单罐故障、报警', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395973, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(167, '二副井提升单罐', 86, true, '二副井提升单罐故障、报警', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395975, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(168, '一副井提升双罐', 86, true, '一副井提升双罐故障、报警', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395974, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(169, '二副井提升双罐', 86, true, '二副井提升双罐故障、报警', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395976, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(170, '110KV变电所', 87, true, '110KV较高报警', '主变、设备故障跳闸等报警', '报警信息推送至区队长及矿领导，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(171, '110KV变电所', 87, true, '110KV一般报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(172, '一主井提升1#车', 86, true, '一主井提升1#车报警', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130361147691010, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(173, '一主井提升2#车', 86, true, '一主井提升2#车报警', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130555050364930, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(174, '二主井提升东车', 86, true, '二主井提升东车故障、报警(中级)', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395971, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(175, '二主井提升东车', 86, true, '二主井提升东车故障、报警(高级)', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395971, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(176, '二主井提升西车', 86, true, '二主井提升西车故障、报警(中级)', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395972, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(177, '二主井提升西车', 86, true, '二主井提升西车故障、报警(高级)', '急停、监控器跳闸等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395972, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(178, '二副井提升单罐', 86, true, '二副井提升单罐故障、报警(中级)', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395975, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(179, '二副井提升单罐', 86, true, '二副井提升单罐故障、报警(高级)', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395975, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(180, '二副井提升双罐', 86, true, '二副井提升双罐故障、报警(中级)', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395976, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(181, '二副井提升双罐', 86, true, '二副井提升双罐故障、报警(高级)', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395976, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(182, '一主井提升1#车', 86, true, '一主井提升1#车报警(中级)', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130361147691010, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(183, '一主井提升1#车', 86, true, '一主井提升1#车报警(高级)', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130361147691010, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(184, '一主井提升2#车', 86, true, '一主井提升2#车报警(中级)', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130555050364930, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(185, '一主井提升2#车', 86, true, '一主井提升2#车报警(高级)', '当控制状态为3（即自动模式）时启用报警推送，其余状态不推送', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1471130555050364930, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(186, '一副井提升单罐', 86, true, '一副井提升单罐故障、报警(中级)', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395973, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(187, '一副井提升单罐', 86, true, '一副井提升单罐故障、报警(高级)', '励磁柜故障、24V欠压等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395973, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(188, '一副井提升双罐', 86, true, '一副井提升双罐故障、报警(中级)', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395974, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(189, '一副井提升双罐', 86, true, '一副井提升双罐故障、报警(高级)', '电机过流、继电器紧停等报警', '报警信息推送至矿机电矿长、副总、机电科长、绞车队长及煤业公司部门岗位人员、分管领导、部门负责人，及时安排绞车队上岗检查。', NULL, NULL, '2021-11-23 10:00:00', 1469233799597395974, '000100010008', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(190, '110KV变电所', 87, true, '110KV低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(191, '矸石井', 86, true, '矸石井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(192, '矸石井', 86, true, '矸石井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(193, '矸石井', 86, true, '矸石井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(194, '风机房中央风井', 25, true, '风机房中央风井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(195, '风机房中央风井', 25, true, '风机房中央风井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(196, '风机房中央风井', 25, true, '风机房中央风井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(197, '35KV变电所东风井', 87, true, '35KV变电所东风井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(198, '35KV变电所东风井', 87, true, '35KV变电所东风井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(199, '35KV变电所东风井', 87, true, '35KV变电所东风井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(200, '35KV变电所西风井', 87, true, '35KV变电所西风井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(201, '35KV变电所西风井', 87, true, '35KV变电所西风井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(202, '35KV变电所西风井', 87, true, '35KV变电所西风井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(203, '风机房东风井', 25, true, '风机房东风井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(204, '风机房东风井', 25, true, '风机房东风井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(205, '风机房东风井', 25, true, '风机房东风井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(206, '风机房西风井', 25, true, '风机房西风井低级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(207, '风机房西风井', 25, true, '风机房西风井中级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(208, '风机房西风井', 25, true, '风机房西风井高级报警', '高、低供电设备温度异常等报警', '报警信息推送至区队长，即使安排人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', 2, '000100010009', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(209, '通风', 25, true, '通风异常报警', '通风异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000100010004', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(210, '压风', 26, true, '压风异常报警', '压风异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000100010005', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(211, '皮带', 27, true, '皮带异常报警', '皮带异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000100010006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(212, '功率', 47, true, '功率异常报警', '功率异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(213, '甲烷报警', 47, true, '甲烷异常报警', '甲烷异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(214, '电度', 47, true, '电度异常报警', '电度异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(215, '位移', 47, true, '位移异常报警', '位移异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(216, '降雨量', 47, true, '降雨量异常报警', '降雨量异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(217, '水质', 47, true, '水质异常报警', '水质异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(218, '顶板压力', 47, true, '顶板压力异常报警', '顶板压力异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(219, '风压', 47, true, '风压异常报警', '风压异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(220, '瓦斯', 47, true, '瓦斯异常报警', '瓦斯异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(221, '温度', 47, true, '温度异常报警', '温度异常时报警', '报警信息推送给相关人员进行报警排查。', ' ', NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(222, '粉尘', 47, true, '粉尘异常报警', '粉尘异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(223, '风速', 47, true, '风速异常报警', '风速异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(224, '浓度', 47, true, '浓度异常报警', '浓度异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(225, '流量', 47, true, '流量异常报警', '流量异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(226, '压力', 47, true, '压力异常报警', '压力异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(227, '低电压', 47, true, '低电压异常报警', '低电压异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(228, '噪音报警', 47, true, '噪音异常报警', '噪音异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(229, '电流报警', 47, true, '电流异常报警', '电流异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(230, '高电压', 47, true, '高电压异常报警', '高电压异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200030006', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(231, '高频率报警', 39, true, '高频率异常报警', '高频率异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200010007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(232, '低频率报警', 39, true, '低频率异常报警', '低频率异常时报警', '报警信息推送给相关人员进行报警排查。', NULL, NULL, '2021-11-23 10:00:00', NULL, '000200010007', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(235, '火灾报警', 29, true, '温度数据异常', '温度传感器超过设定值', '预警信息发送给通防科，联系人员排查点位', NULL, NULL, NULL, NULL, '000100020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(236, '火灾报警', 29, true, '烟雾传感器开', '烟雾传感器数值为1', '预警信息发送给通防科进行预警处理', NULL, NULL, NULL, NULL, '000100020001', NULL, NULL, NULL);
INSERT INTO public.bdata_alert_indictor (id, "name", type_id, active, indictor, "condition", oper, reminder, sms, warningcreatetime, channel_push_mode_id, "oid", alg_config_id, camera_id, "type") VALUES(254, '综合自动化报警', 39, false, '综合自动化数据异常', '综合自动化数据异常', '报警信息推送相关负责人进行报警排查', NULL, NULL, NULL, NULL, '000200010007', NULL, NULL, NULL);


INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(1, '系统预警', '系统预警', NULL, '0001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(2, '系统报警', '系统报警', NULL, '0002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(3, '管理预警', '管理预警', NULL, '0003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(4, '管理报警', '管理报警', NULL, '0004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(6, '系统预警', '一通三防', 1, '00010002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(7, '系统预警', '地测防治水', 1, '00010003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(8, '系统预警', '生产调度', 1, '00010004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(9, '系统报警', '机电专业', 2, '00020001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(10, '系统报警', '地测防治水', 2, '00020002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(11, '系统报警', '一通三防', 2, '00020003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(12, '管理预警', '机电专业', 3, '00030001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(13, '管理预警', '一通三防', 3, '00030002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(14, '管理预警', '地测防治水', 3, '00030003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(15, '管理预警', '安全管理', 3, '00030004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(16, '管理预警', '机电运输', 3, '00030005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(17, '管理报警', '机电运输专业', 4, '00040001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(18, '管理报警', '一通三防', 4, '00040002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(19, '管理报警', '地测防治水', 4, '00040003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(20, '管理报警', '生产调度', 4, '00040004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(21, '管理报警', '安全管理', 4, '00040005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(23, '系统预警', '监测监控', 5, '000100010002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(24, '系统预警', '主排水系统', 5, '000100010003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(25, '系统预警', '主通风机', 5, '000100010004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(26, '系统预警', '压风机', 5, '000100010005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(27, '系统预警', '皮带机', 5, '000100010006');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(28, '系统预警', '人员定位', 5, '000100010007');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(29, '系统预警', '重大灾害', 6, '000100020001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(30, '系统预警', '防治水', 7, '000100030001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(31, '系统预警', '数据填报', 8, '000100040001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(32, '系统预警', '调度管理', 8, '000100040002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(33, '系统报警', '电力系统', 9, '000200010001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(34, '系统报警', '辅助运输系统', 9, '000200010002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(35, '系统报警', '主煤流系统', 9, '000200010003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(36, '系统报警', '主排水系统', 9, '000200010004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(37, '系统报警', '主通风机', 9, '000200010005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(38, '系统报警', '人员定位', 9, '000200010006');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(39, '系统报警', '自动化系统', 9, '000200010007');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(40, '系统报警', '监测监控系统', 9, '000200010008');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(41, '系统报警', '防治水', 10, '000200020001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(42, '系统报警', '瓦斯防治', 11, '000200030001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(43, '系统报警', '重大灾害', 11, '000200030002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(44, '系统报警', '矿井通风', 11, '000200030003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(45, '系统报警', '瓦斯抽采', 11, '000200030004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(46, '系统报警', '防灭火', 11, '000200030005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(47, '系统报警', '安全监控', 11, '000200030006');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(48, '系统报警', '煤与瓦斯突出', 11, '000200030007');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(49, '管理预警', '备件库', 12, '000300010001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(50, '管理预警', '设备故障', 12, '000300010002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(51, '管理预警', '设备库存', 12, '000300010003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(52, '管理预警', '通用计划', 12, '000300010004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(53, '管理预警', '设备检验', 12, '000300010005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(54, '管理预警', '矿井通风', 13, '000300020001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(55, '管理预警', '安全监控', 13, '000300020002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(56, '管理预警', '煤与瓦斯突出', 13, '000300020003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(57, '管理预警', '矿井爆破', 13, '000300020004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(58, '管理预警', '瓦斯防治', 13, '000300020005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(59, '管理预警', '防治水', 14, '000300030001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(60, '管理预警', '隐患管理', 15, '000300040001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(61, '管理预警', '三违管理', 15, '000300040002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(62, '管理预警', 'AI分析', 15, '000300040003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(63, '管理预警', '科技成果', 15, '000300040004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(64, '管理预警', '井下超时', 15, '000300040005');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(65, '管理预警', '质量标准化', 15, '000300040006');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(66, '管理预警', '百人违章率', 15, '000300040007');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(67, '管理预警', '用电管理', 16, '000300050001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(68, '管理预警', '机电设备', 16, '000300050002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(69, '管理预警', '机电电缆', 16, '000300050003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(70, '管理预警', '运输管理', 16, '000300050004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(71, '管理报警', '地面要害场所', 17, '000400010001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(72, '管理报警', '电力系统场所', 17, '000400010002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(73, '管理报警', '主排水系统', 17, '000400010003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(74, '管理报警', '设备检修、任务计划', 17, '000400010004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(75, '管理报警', '瓦斯防治', 18, '000400020001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(76, '管理报警', '煤与瓦斯突出', 18, '000400020002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(77, '管理报警', '矿井爆破', 18, '000400020003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(78, '管理报警', '防治水', 19, '000400030001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(79, '管理报警', '产量管理', 20, '000400040001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(80, '管理报警', '进尺管理', 20, '000400040002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(81, '管理报警', '销量管理', 20, '000400040003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(82, '管理报警', '库存管理', 20, '000400040004');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(83, '管理报警', '隐患管理', 21, '000400050001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(84, '管理报警', '三违管理', 21, '000400050002');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(85, '管理报警', '井下超员', 21, '000400050003');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(86, '系统报警', '提升系统', 5, '000100010008');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(87, '系统报警', '110KV变电所', 5, '000100010009');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(5, '系统预警', '机电专业', 1, '00010001');
INSERT INTO public.bdata_alert_indictor_type (id, alert_type, "name", pid, "oid") VALUES(22, '系统预警', '自动化系统', 5, '000100010001');

INSERT INTO public.channel_push_type (id, chcode_a, chcode_b, is_push_flag, is_del, create_time, message, push_type) VALUES(1506960443749437441, '通知', '通知', NULL, NULL, NULL, NULL, 1);
INSERT INTO public.channel_push_type (id, chcode_a, chcode_b, is_push_flag, is_del, create_time, message, push_type) VALUES(1602933465076994049, '短信', '短信', NULL, NULL, NULL, NULL, 2);


INSERT INTO public.warning_channel_classify (id, type_name, type_code) VALUES(2, 'AI预警报警', 2);
INSERT INTO public.warning_channel_classify (id, type_name, type_code) VALUES(1, '预警报警', 1);