version: "3.5"

x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/gis/.env
  healthcheck:
    test: ["CMD","nginx","-t"]

x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../services/mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/gis/.env

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  gis-biz-log:
    external: true

services:
  # 前端 - GIS
  frontend-module-gis:
    <<: *nginx-reset
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-gis:${FRONTEND_GIS_VERSION}

  # 后端 gis业务系统
  backend-gis-biz:
    <<: *backend-defaults
    image: harbor.qdbdtd.com/bjproduct/platform/services/gis_biz:${BACKEND_GIS_VERSION}
    environment:
      - CONVER_MONITOR_URL=${BACKEND_DATA_MONITOR}
      #- AI_LIVE_WALL_HOST=${AI_LIVE_WALL_HOST}
      #- AI_LIVE_WALL_PORT=${AI_LIVE_WALL_PORT}
      - SAFE_URL_HOST=${UPSTREAM_GIS_API}
      - luoyang_url=${luoyang_url}
      #- GAO_DE_URL=restapi.amap.com
      #- GAO_DE_KEY=${GAO_DE_KEY}
      #- SRS_SERVER_URL=${SRS_SERVER_URL}
      #- GIS_MINIO_BUCKETNAME=${GIS_MINIO_BUCKETNAME}
      #- GIS_MQ_EX=${GIS_MQ_EX}
      #- GIS_MQ_QUEUE=${GIS_MQ_QUEUE}
      #- GIS_MQ_DIRECT=${GIS_MQ_DIRECT}
      - WEB_PORT_RABBITMQ=${WEB_PORT_RABBITMQ}
      - BLADE_MINIO_HOST=${BLADE_MINIO_HOST}
      - FACE_AUTO_ADVANCE=${FACE_AUTO_ADVANCE}
      - THREE_GIS_URL=${THREE_GIS_URL}
      - THREE_PASSWORD=${THREE_PASSWORD}
      - THREE_NAME=${THREE_NAME}
      - THREE_DEPLOY=${THREE_DEPLOY}
    volumes:
      - "gis-biz-log:/home/<USER>/logs"
  #ports:
  #    - "${PORT_GIS_BIZ}:9001"
