version: "3.5"

x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/iot/.env
  healthcheck:
    test: [ "CMD","nginx","-t" ]

x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../services/mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/iot/.env

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  data-monitor-image:
    external: true
  data-monitor-log:
    external: true
  data-storage-log:
    external: true
  data-warn-log:
    external: true
  converter-monitor-log:
    external: true

services:
#  # 前端 - 拓扑编辑器
#  frontend-module-topo_editor:
#    <<: *nginx-reset
#    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-topo-editor:${FRONTEND_TOPO_EDITOR_VERSION}
#
#  # 前端 - 拓扑渲染器
#  frontend-module-topo_render:
#    <<: *nginx-reset
#    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-topo-render:${FRONTEND_TOPO_RENDER_VERSION}
#
#  # 前端 - 拓扑渲染器（嵌入）
#  frontend-module-topo_embed:
#    <<: *nginx-reset
#    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-topo-render:embed-${FRONTEND_TOPO_RENDER_VERSION}
#
#  # 前端 - 新版topo
#  frontend-base-prtopo-editor:
#    <<: *nginx-reset
#    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-prtopo-editor:${FRONTEND_TOPO_VERSION}
#
#  # 前端 - 新版topo预览
#  frontend-base-prtopo-embed:
#    <<: *nginx-reset
#    image: harbor.qdbdtd.com/bjproduct/platform/front-services/base-prtopo-editor:embed-${FRONTEND_TOPO_VERSION}

  # 前端 - 页面编辑器
  frontend-module-page_builder:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-page-editor:${FRONTEND_PAGE_EDITOR_VERSION}

  # 前端 - 页面渲染器
  frontend-module-page_render:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-page-editor:render-${FRONTEND_PAGE_EDITOR_VERSION}

  # 前端 - 联网系统
  frontend-biz-data_ims:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/biz-data-ims:${FRONTEND_DATA_IMS_VERSION}

  # 前端 - 点表列表
  frontend-module-data_view:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-data-view:${FRONTEND_DATA_VIEW_VERSION}

  # 前端 - 数据接入
  frontend-module-data_access:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-data-access:${FRONTEND_DATA_ACCESS_VERSION}

  # 后端 数据接入
  backend-data-storage:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/dataroute:${BACKEND_ROUTE_VERSION}
    volumes:
      - "data-storage-log:/home/<USER>/logs"
    environment:
      - RABBITMQ_VIRTUALHOST=${RMB_DR_VH}
      #- RABBITMQ_PREFETCH_COUNT=50
      #- RABBITMQ_CONSUMERS_PER_QUEUE=20
      - DATAROUTE_APPROVAL_URL=${UPSTREAM_CMS}
      - DATA_ROUTE_ACTIVE_PROFILE=pro
      #- TDENGINE_CONNECT_TYPE=restful
      #- TDENGINE_HOST=**************
      #- TDENGINE_PORT=6041
      #- TDENGINE_USERNAME=root
      #- TDENGINE_PASSWORD=Dbqdtd@168
      #- SCHEDULED_DATA_MONITOR_UPDATE_RMQ_RESET_INIT_DELAY=86400000
      #- SCHEDULED_DATA_MONITOR_UPDATE_RMQ_RESET_FIXED_DELAY=86400000
      #- SCHEDULED_DATA_MONITOR_UPDATE_DATA_MONITOR_CRON=
      #- SCHEDULED_POSITION_CHECK_ENABLED=false
      #- POSITION_TRACK_MAX_LENGTH=35565
      #- POSITION_TRACK_EXTRACT_TIME_RANGE=30
      - POSITION_STATION_ALARM_TYPE_ID=
      - SAFETY_SUBSTATION_ALARM_TYPE_ID=
    deploy:
      resources:
        limits:
          cpus: '10'
          memory: 10G
        reservations:
          memory: 4G

#  # Hasura GraphQL Engine
#  graphql-engine:
#    << : *backend-defaults
#    image: harbor.qdbdtd.com/middleware/hasura/graphql-engine:${BAAS_VERSION}
#    environment:
#      - HASURA_GRAPHQL_ENABLE_CONSOLE=true
#      #- HASURA_GRAPHQL_DATABASE_URL=${POSTGRES_URL}
#    volumes:
#      - "./hasura/migrations:/hasura-migrations:ro"
#      - "./hasura/metadata:/hasura-metadata:ro"
#    deploy:
#      resources:
#        limits:
#          memory: ${HASURA_MEMORY_LIMIT}

  # 后端 数据处理
  backend-data-warn:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/data_warn:${BACKEND_WARN_VERSION}
    volumes:
      - "data-warn-log:/home/<USER>/logs"
    environment:
      - RABBITMQ_VIRTUALHOST=${RMB_DW_VH}

  # 后端 统一接口服务
  backend-data-monitor:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/timescale-monitor:${BACKEND_TSMONITOR_VERSION}
    environment:
      - TIMESCALE_MONITOR_ACTIVE_PROFILE=pro
      - RABBITMQ_VIRTUALHOST=${RMB_DM_VH}
      - DATAROUTE_APPROVAL_URL=${UPSTREAM_CMS}
      - MINECODE=${MINECODE}
      - SAFE_URL_HOST=${SAFE_URL_HOST}
      - SAFE_PORT=${SAFE_PORT}
      - BLADE_BACKEND=backend-process-user-permission
      - ENABLE_DATA_SCOPE=true
      - MOS_VIDEO_URL=
      #- TDENGINE_CONNECT_TYPE=restful
      #- TDENGINE_HOST=**************
      #- TDENGINE_PORT=6041
      #- SCHEDULED_MODEL_SYSTEM_ALIVE_CHECK_CRON=0 */5 * * * ?
      #- SCHEDULED_COLLECT_FILE_LOAD_DELAY=300000
      #- SCHEDULED_ACCESS_NODE_STATUS_DETECT_CHECK_MODE=0
      - SCHEDULED_COLLECT_FILE_LOAD_FILE_BASE_PATH=/home/<USER>/static/archive
      - SCHEDULED_SERVER_STATE_MONITOR_STATE_FILE=/home/<USER>/static/archive/docker_disk_usage.txt
      - SCHEDULED_SERVER_STATE_MONITOR_CRON=0 0/10 * * * ?
      # 测试值 32
      - SCHEDULED_SERVER_STATE_MONITOR_WARN_LIMIT=95
      - SCHEDULED_SERVER_STATE_MONITOR_NOTIFY_PHONE=15822780432
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_TIME_RANGE=1440
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_INIT_RATE=600000
      #- SCHEDULED_POSITION_SUPERIOR_UNIT_DATA_SYNC_FIXED_RATE=600000
      - SYSTEM_MONITOR_PUSH_MSG_TYPE_ID=88
      - SYNC_SYSTEM_MONITOR_TO_PUSH_MSG=false
      - MINIO_ENDPOINT=http://bladex-minio:9000
    volumes:
      - "data-monitor-log:/home/<USER>/logs"
      - "data-monitor-image:/home/<USER>"
      - "/home/<USER>/file_sync/archive:/home/<USER>/static/archive"
    #ports:
    #  - "${PORT_DATA_MONITOR}:9001"

  # 后端 数据解析
  backend-converter-monitor:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/parse-data:${BACKEND_PARSE_VERSION}
    volumes:
      - "converter-monitor-log:/home/<USER>/logs"
    environment:
      - RABBITMQ_VIRTUALHOST=${RMB_PD_VH}

