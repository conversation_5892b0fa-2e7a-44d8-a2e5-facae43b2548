version: "3.5"

x-video-defaults: &video_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  env_file:
    - .env
  networks:
    - middle

services:
  # 数据库
  postgres:
    <<: *video_defaults
    image: harbor.qdbdtd.com/middleware/postgis:${PG_VERSION}
    container_name: ivs_postgres
    volumes:
      - "ivs-postgres:/var/lib/postgresql/data"
      - "./init.sql:/docker-entrypoint-initdb.d/postgres_init.sql:ro"
    environment:
      - POSTGRES_USER=${POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DATABASE}

  # 后端
  ivs-server:
    << : *video_defaults
    image: harbor.qdbdtd.com/bjproduct/platform/services/ivs:${VERSION_BACKEND}
    container_name: ivs-server
    volumes:
      - "ivs-backend-log:/home/<USER>/logs"
    depends_on:
      - postgres

volumes:
  ivs-postgres:
    external: true
  ivs-backend-log:
    external: true

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}