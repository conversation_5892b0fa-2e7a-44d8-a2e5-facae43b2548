# SSH 端口转发

为方便调试，可以开启连接至 Docker `middle` 网络中的 ssh 服务，并通过端口转发调试 Docker 网络中的服务。

以下命令默认在部署文件夹的根目录中执行。

1. 根据实际情况修改 `./.config.env` 中 `SSH_PORT` 和 `SSH_PASSWORD` 环境变量。

```sh
...
# ssh 配置
export SSH_PASSWORD=tx123456
export SSH_PORT=2222
```

2. 通过脚本开启 ssh 服务

```sh
./auto_deploy.sh ssh start
```

3. 本机通过 ssh 登录，并开启端口转发

注意，由于 ssh 服务每次启动时会重新生成 Host Key，所以在连接时需要把 `StrictHostKeyChecking` 设置为 `no`，并把 `UserKnownHostsFile` 指向 `/dev/null`。

请根据实际需要修改 `-L` 端口转发配置和连接端口、地址.

```sh
ssh -o StrictHostKeyChecking=no \
    -o UserKnownHostsFile=/dev/null \
    -L 1985:video-srs:1985 \
    -L 5432:postgres:5432 \
    -L 8086:influxdb:8086 \
    -L 1880:rednode:1880 \
    -L 6379:redis:6379 \
    -L 15672:rabbitmq:15672 \
    -L 5672:rabbitmq:5672 \
    -L 9999:graphql-engine:8080 \
    -L 9001:backend-data-monitor:9001 \
    -L 9003:backend-data-storage:9001 \
    -L 9004:backend-data-warn:9001 \
    -L 9005:backend-converter-monitor:9005 \
    -p 2222 root@*************
```

Windows 用户请在 PowerShell 中执行，注意不用添加前两行配置，并且换行符有所区别。

```powershell
ssh -L 1985:video-srs:1985 `
    -L 5432:postgres:5432 `
    -L 8086:influxdb:8086 `
    -L 1880:rednode:1880 `
    -L 6379:redis:6379 `
    -L 15672:rabbitmq:15672 `
    -L 5672:rabbitmq:5672 `
    -L 9999:graphql-engine:8080 `
    -L 9001:backend-data-monitor:9001 `
    -L 9003:backend-data-storage:9001 `
    -L 9004:backend-data-warn:9001 `
    -L 9005:backend-converter-monitor:9005 `
    -p 2222 root@*************
```

BladeX 基础服务

```sh
-L 5432:bladex-postgres:5432 \
-L 9000:bladex-minio:9000 \
-L 6379:bladex-redis:6379 \
-L 15672:bladex-rabbitmq:15672 \
-L 5672:bladex-rabbitmq:5672 \
```

其他服务请参考 `docker-compose` 文件中服务名  

4. 调试结束后通过脚本关闭 ssh 服务

```sh
./auto_deploy.sh ssh stop
```
