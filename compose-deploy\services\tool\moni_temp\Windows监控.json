{"annotations": {"list": [{"$$hashKey": "object:17", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Windows的Prometheus监控看板展示，使用wmi_exporter 0.7.0+", "editable": true, "gnetId": 10467, "graphTooltip": 1, "id": 7, "iteration": 1589182869260, "links": [], "panels": [{"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "decimals": 0, "description": "", "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 0}, "id": 33, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "wmi_os_time{instance=~\"$server\"} - wmi_system_system_up_time{instance=~\"$server\"}", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Uptime", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": true, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 3, "x": 3, "y": 0}, "id": 19, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "%", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "100 - (avg by (instance) (irate(wmi_cpu_time_total{mode=\"idle\", instance=~\"$server\"}[1m])) * 100)", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "80,90", "title": "CPU使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 3, "x": 6, "y": 0}, "id": 21, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "%", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "(wmi_cs_physical_memory_bytes{instance=~\"$server\"} - wmi_os_physical_memory_free_bytes{instance=~\"$server\"}) / wmi_cs_physical_memory_bytes{instance=~\"$server\"} * 100", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "80,90", "title": "内存使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 3, "x": 9, "y": 0}, "id": 23, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "(sum(wmi_logical_disk_size_bytes{volume!~\"Harddisk.*\", instance=\"$server\"}) by (instance) - sum(wmi_logical_disk_free_bytes{volume!~\"Harddisk.*\", instance=\"$server\"}) by (instance)) / sum(wmi_logical_disk_size_bytes{volume!~\"Harddisk.*\", instance=\"$server\"}) by (instance) * 100", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": "80,90", "title": "磁盘使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": null, "decimals": 0, "format": "bps", "gauge": {"maxValue": 100000000, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 6, "w": 3, "x": 12, "y": 0}, "id": 17, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "(sum(irate(wmi_net_bytes_total{instance=\"$server\"}[1m])) > 1)* 8", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"expr": "  sum(irate(wmi_net_bytes_total{instance=~\"$server\"}[5m])) / sum(wmi_net_current_bandwidth{instance=~\"$server\"}/8) * 100", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "B"}], "thresholds": "70000000,90000000", "title": "带宽使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 0, "fillGradient": 0, "gridPos": {"h": 6, "w": 9, "x": 15, "y": 0}, "hiddenSeries": false, "hideTimeOverride": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 3, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "wmi_cs_physical_memory_bytes{instance=~\"$server\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Physical memory", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 5}, {"application": {"filter": ""}, "expr": "wmi_os_physical_memory_free_bytes{instance=~\"$server\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Free physical memory", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "C", "step": 5}, {"application": {"filter": ""}, "expr": "wmi_os_virtual_memory_bytes{instance=~\"$server\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Virtual memory", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 5}, {"expr": "wmi_os_virtual_memory_free_bytes{instance=~\"$server\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Free virtual memory", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "内存详情", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 0, "description": "", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 2}, "id": 35, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "wmi_cs_logical_processors{instance=~\"$server\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Processors", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorPostfix": false, "colorPrefix": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "Prometheus", "decimals": 1, "description": "", "format": "bytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 4}, "id": 37, "interval": "", "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "80%", "prefix": "", "prefixFontSize": "80%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "repeat": "host", "repeatDirection": "v", "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "wmi_cs_physical_memory_bytes{instance=~\"$server\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "RAM", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"aliasColors": {"idle": "dark-green", "user": "dark-red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 2, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "hideTimeOverride": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "sum by (mode) (rate(wmi_cpu_time_total{instance=~\"$server\"}[5m]))", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{mode}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU负载（16核）", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 1, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "(avg without (cpu) (sum(irate(wmi_cpu_time_total{instance=\"$server\",mode!=\"idle\"}[5m])) by (mode)) / 16)* 100", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{mode}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "hideTimeOverride": false, "id": 11, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "rate(wmi_net_bytes_sent_total{instance=~\"$server\"}[$interval]) >0", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Sent-上传", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 10}, {"application": {"filter": ""}, "expr": "- rate(wmi_net_bytes_received_total{instance=~\"$server\"}[$interval]) <0", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Received-下载", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "网络详情", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "decimals": 2, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 7, "x": 12, "y": 15}, "hiddenSeries": false, "hideTimeOverride": false, "id": 29, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(increase(wmi_net_bytes_received_total{instance=\"$server\"}[1h])) by (instance)", "format": "time_series", "interval": "1h", "intervalFactor": 1, "legendFormat": "Received-下载", "refId": "A"}, {"expr": "sum(increase(wmi_net_bytes_sent_total{instance=\"$server\"}[1h])) by (instance)", "format": "time_series", "interval": "1h", "intervalFactor": 1, "legendFormat": "Sent-上传", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每小时流量", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 5, "x": 19, "y": 15}, "hiddenSeries": false, "hideTimeOverride": false, "id": 10, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(wmi_net_packets_outbound_discarded{instance=~\"$server\"}[$interval]) + irate(wmi_net_packets_outbound_errors{instance=~\"$server\"}[$interval])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Sent-上传", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 15}, {"application": {"filter": ""}, "expr": "irate(wmi_net_packets_received_discarded{instance=~\"$server\"}[$interval]) + irate(wmi_net_packets_received_errors{instance=~\"$server\"}[$interval])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "interval": "", "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Received-下载", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 15}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network discarded/error packets", "tooltip": {"shared": false, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "pps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 2, "fillGradient": 0, "gridPos": {"h": 8, "w": 14, "x": 0, "y": 24}, "hiddenSeries": false, "hideTimeOverride": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "irate(wmi_logical_disk_write_bytes_total{instance=~\"$server\", volume !~\"HarddiskVolume.+\"}[30s])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Write {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}, {"application": {"filter": ""}, "expr": "- irate(wmi_logical_disk_read_bytes_total{instance=~\"$server\", volume !~\"HarddiskVolume.+\"}[30s])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "Read {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "B", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "每秒磁盘读写", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 5, "x": 14, "y": 24}, "hiddenSeries": false, "hideTimeOverride": false, "id": 15, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "wmi_logical_disk_free_bytes{instance=~\"$server\", volume !~\"HarddiskVolume.+\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "free {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "分区剩余空间", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 5, "x": 19, "y": 24}, "hiddenSeries": false, "hideTimeOverride": false, "id": 9, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "rate(wmi_logical_disk_split_ios_total{instance=~\"$server\", volume !~\"HarddiskVolume.+\"}[30s])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "i/o {{volume}}", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Hard disk i/o ops total", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 6, "x": 0, "y": 32}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "wmi_os_processes", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "Processes", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Received mysqld-exporter:9104": "#0A50A1", "stopped": "#2F575E"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 10, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 6, "y": 32}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "sum(wmi_service_state{instance=~\"$server\"}) by (state)", "format": "time_series", "functions": [], "group": {"filter": ""}, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "{{state}}", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 5}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Services by state", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 5, "x": 14, "y": 32}, "hiddenSeries": false, "hideTimeOverride": false, "id": 12, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "wmi_system_threads{instance=~\"$server\"}", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "system_threads", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "system_threads", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 5, "x": 19, "y": 32}, "hiddenSeries": false, "hideTimeOverride": false, "id": 13, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"application": {"filter": ""}, "expr": "rate(wmi_system_exception_dispatches_total{instance=~\"$server\"}[$interval])", "format": "time_series", "functions": [], "group": {"filter": ""}, "hide": false, "host": {"filter": ""}, "intervalFactor": 1, "item": {"filter": ""}, "legendFormat": "exceptions", "metric": "mysql_global_status_questions", "mode": 0, "options": {"showDisabledItems": false}, "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "System exception dispatches", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 22, "style": "dark", "tags": ["windows", "wmi_exporter", "prometheus"], "templating": {"list": [{"auto": false, "auto_count": 500, "auto_min": "30s", "current": {"selected": false, "text": "60s", "value": "60s"}, "hide": 2, "label": "Interval", "name": "interval", "options": [{"selected": true, "text": "60s", "value": "60s"}], "query": "60s", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"text": "************:9182", "value": "************:9182"}, "datasource": "Prometheus", "definition": "", "hide": 0, "includeAll": false, "index": -1, "label": "Server", "multi": false, "name": "server", "options": [], "query": "label_values(wmi_system_system_up_time, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "windows监控", "uid": "7UlnHoGZz", "variables": {"list": []}, "version": 4}