    
        
- targets: ['http://192.168.1.79:8001']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'config-data'          
- targets: ['http://192.168.1.79:8001/rabbit/']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'rabbitmq'          
- targets: ['http://192.168.1.79:8001/rednode/admin/']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'rednode'          
- targets: ['http://192.168.1.79:50004']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'gisserver_frontend'    
- targets: ['http://192.168.1.79:8001/hasura/healthz']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'hasura_mos'  
- targets: ['http://192.168.1.79:82']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'frontend_dc'      
- targets: ['http://192.168.1.79:4001']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'cloud_functions'          
- targets: ['http://192.168.1.79:9910/healthz']
  labels:
    env: '谢桥'
    nodename: 'xieqiao-test2'
    service: 'hasura_dc'