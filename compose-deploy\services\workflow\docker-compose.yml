version: "3.5"

x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/mes/.env
  healthcheck:
    test: ["CMD","nginx","-t"]

x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../services/mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../services/base/.env
    - ../services/workflow/.env
    - ../services/mos/.env

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}


services:

  # 前端 - 工作流
  frontend-base-workflow_serve:
    <<: *nginx-reset
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/bdtd-workflow-front:${FRONTEND_WF_VERSION}
  #  environment:
  #    - BACKEND_ENDPOINT=${BLADEX_BACKEND_ENDPOINT}


  # 审批流后端
  backend-workflow:
    <<: *backend-defaults
    image: harbor.qdbdtd.com/bjproduct/platform/bladex/bdtd-workflow-back:${BACKEND_WF_VERSION}
    environment:
      - MES_MINIO_BUCKETNAME=${MES_MINIO_BUCKETNAME}
      - MES_HOST=backend-mes-biz
      - MES_PORT=9001
   # ports:
   #   - "${POST_WORKFLOW_BACKEND}:8004"