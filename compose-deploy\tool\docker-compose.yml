version: "3.5"

x-tool-defaults: &tool_defaults
  restart: unless-stopped
  networks:
    - middle

volumes:
  tool-portainer-data:
    external: true
  tool-grafana-data:
    external: true
  tool-prometheus-data:
    external: true

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

services:
  grafana:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/grafana:1.0
    container_name: grafana
    hostname: grafana
    ports:
      - "30000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD__FILE: /tmp/grafana_password
    volumes:
      - tool-grafana-data:/var/lib/grafana
      - ./conf/grafana.ini:/etc/grafana/grafana.ini
      - ./secret/grafana_password:/tmp/grafana_password
      - /etc/localtime:/etc/localtime:ro


  prometheus:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/prometheus:1.0
    container_name: prometheus
    user: root
    hostname: prometheus
    ports:
      - "39090:9090"
    volumes:
      - tool-prometheus-data:/prometheus
      - ./conf/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./sd_config:/etc/prometheus/sd_config
      - ./rules:/etc/prometheus/rules
      - /etc/localtime:/etc/localtime:ro


  alertmanager:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/alertmanager:1.0
    container_name: alertmanager
    hostname: alertmanager
    ports:
      - "39093:9093"
    volumes:
      - ./conf/alertmanager.yml:/etc/alertmanager/alertmanager.yml


  alone-alert:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/alone-alert:1.0
    container_name: alone-alert
    hostname: alone-alert
    environment:
      - OPTS
      - TOKEN
    ports:
      - "35000:5000"


  node-exporter:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/node-exporter:1.0
    container_name: node-exporter
    hostname: node-exporter
    pid: host
    command:
      - '--path.rootfs=/host'
    volumes:
      - /:/host:ro,rslave

  blackbox-exporter:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/blackbox-exporter:1.0
    container_name: blackbox-exporter
    hostname: blackbox-exporter
    ports:
      - "39115:9115"


  cadvisor:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/cadvisor:1.0
    container_name: cadvisor
    hostname: cadvisor
    ports:
      - "38080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro


  portainer:
    <<: *tool_defaults
    image: harbor.qdbdtd.com/middleware/prometheus/portainer:1.0
    container_name: portainer
    command:
      -H unix:///var/run/docker.sock
      --admin-password-file /tmp/portainer_password
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - tool-portainer-data:/data
      - ./secret/portainer_password:/tmp/portainer_password
    ports:
      - "39000:9000"

