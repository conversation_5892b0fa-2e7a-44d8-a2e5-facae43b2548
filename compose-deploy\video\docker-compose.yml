version: "3.5"

x-video-defaults: &video_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  env_file:
    - ../base/.env
    - .env
  networks:
    - middle
x-nginx-defaults: &nginx_defaults
  << : *video_defaults
  healthcheck:
    test: ["CMD","nginx","-t"]

volumes:
  video-redis:
    external: true

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

services:
  # Redis
  video-redis:
    << : *video_defaults
    image: harbor.qdbdtd.com/middleware/redis:4.0.11
    container_name: video-redis
    volumes:
      - video-redis:/data

  # SRS
  video-srs:
    << : *video_defaults
    image: harbor.qdbdtd.com/middleware/srs:6.0.10
    container_name: video-srs
    volumes:
      - $PWD/srs.conf:/usr/local/srs/conf/docker.conf
    #ports:
    #  - "${PORT_PROXY}:8080"
    #  - "${PORT_SRS}:1935"

  # videojs-flow HTTP -> WebSocket
  videojs-flow:
    << : *video_defaults
    image: harbor.qdbdtd.com/middleware/videojs-flow:1.1.0
    environment:
      - HOST=http://video-srs:8080

  # Celery
  celery:
    << : *video_defaults
    image: harbor.qdbdtd.com/bjproduct/platform/videos/video-server:${VERSION_BACKEND}
    container_name: video-celery
    environment:
      - BROKER_URL=redis://video-redis:6379/0
      - BACKEND_URL=http://video-server:9000/devices
      - RTMP_SERVER_URI=rtmp://video-srs:1935/live
      - SRS_URL=http://video-srs:1985/api/v1/streams/?count=1500
      # if the video streams are more than 300, suggest to set 600
      - CYCLE_SECONDS=150
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    depends_on:
      - video-redis
    command: ["celery",  "-A", "c.app", "worker", "-B"]

  # Preview
  video-preview:
    << : *video_defaults
    image: harbor.qdbdtd.com/bjproduct/platform/videos/video_preview:${VERSION_BACKENDS}
    container_name: video-preview
    volumes:
      - ./image:/app/image
    #ports:
    #  - "9019:19850"

  # 后端
  video-server:
    << : *video_defaults
    image: harbor.qdbdtd.com/bjproduct/platform/videos/video-server:${VERSION_BACKEND}
    container_name: video-server
    environment:
      #- POSTGRES_URL=postgres://${POSTGRES_USERNAME:-postgres}:${POSTGRES_PASSWORD:-postgres}@${POSTGRES_HOST:-postgres}:${POSTGRES_PORT:-5432}/${POSTGRES_DATABASE:-gis}
      - BROKER_URL=redis://video-redis:6379/0
      - SRS_URL=http://video-srs:1985/api/v1/streams/?count=1500
    depends_on:
      - video-redis
    ## default work[-w]: 4,  scale in/out workers for production env.
    # command: ["gunicorn", "--timeout", "120", "-w", "24", "-b", ":9000", "--log-level", "debug", "--access-logfile", "-", "--error-logfile", "-", "run:app"]

  # 前端
  video-frontend:
    << : *nginx_defaults
    image: harbor.qdbdtd.com/bjproduct/platform/front-services/video-front:${VERSION_FRONTEND}
    container_name: video-frontend
    environment:
      - BACKEND_URL=http://video-server:9000
      - SRS_URL=http://video-srs:1985
    depends_on:
      - video-server
    #ports:
    #  - "${PORT_VIDEO_FRONTEND}:80"
