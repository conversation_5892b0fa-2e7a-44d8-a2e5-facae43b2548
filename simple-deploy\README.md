# Simple Deploy

一个类似PocketBase的单文件服务部署管理器，用于简化复杂的Docker Compose多文件部署方案。

## 🎯 项目目标

将当前基于多个`docker-compose.yml`文件的微服务部署方案，重构为一个统一的单文件管理程序，提供：

- 🚀 **单文件部署**：一个可执行文件包含所有功能
- 🎛️ **Web管理界面**：直观的服务管理和监控界面
- 🗄️ **统一配置管理**：所有配置存储在SQLite数据库中
- 🔄 **灵活的服务编排**：动态启停服务和服务组
- 📊 **实时监控**：服务状态监控和日志查看
- 🔧 **配置模板**：支持配置文件模板和变量替换

## 🏗️ 架构设计

```
┌─────────────────────────────────────┐
│           Web UI (Vue.js)           │
├─────────────────────────────────────┤
│         REST API (Gin)              │
├─────────────────────────────────────┤
│  Service Manager │ Config Manager   │
│  Container Mgr   │ Dependency Mgr   │
├─────────────────────────────────────┤
│        Docker Engine API            │
├─────────────────────────────────────┤
│      SQLite Database (GORM)         │
└─────────────────────────────────────┘
```

## 🚀 快速开始

### 方式一：使用快速启动脚本（推荐）

```bash
# Linux/macOS
chmod +x quick-start.sh
./quick-start.sh

# Windows
quick-start.bat
```

### 方式二：手动构建和运行

#### 1. 编译程序

```bash
cd simple-deploy
go mod tidy
go build -o simple-deploy cmd/main.go
```

#### 2. 从现有项目导入配置

```bash
# 导入现有的docker-compose配置
./simple-deploy import /path/to/your/project

# 示例：导入compose-deploy目录的配置
./simple-deploy import ../compose-deploy
```

#### 3. 启动Web服务

```bash
./simple-deploy serve
```

### 方式三：使用Docker运行

```bash
# 使用Docker Compose（推荐）
docker-compose up -d

# 或者直接使用Docker
docker build -t simple-deploy .
docker run -d -p 8080:8080 -v /var/run/docker.sock:/var/run/docker.sock simple-deploy
```

访问 http://localhost:8080 查看管理界面

## 📋 功能特性

### 服务管理
- ✅ 服务启动/停止/重启
- ✅ 服务组批量操作
- ✅ 服务状态实时监控
- ✅ 服务日志查看
- ✅ 依赖关系管理

### 配置管理
- ✅ 环境变量管理
- ✅ 端口映射配置
- ✅ 卷挂载配置
- ✅ 配置文件模板
- ✅ 变量替换支持

### Web界面
- ✅ 响应式设计
- ✅ 实时状态更新
- ✅ 服务分组展示
- ✅ 操作日志记录

## 🗂️ 项目结构

```
simple-deploy/
├── cmd/
│   └── main.go                 # 主程序入口
├── internal/
│   ├── api/                    # REST API处理器
│   │   └── handlers.go
│   ├── database/               # 数据库管理
│   │   └── database.go
│   ├── docker/                 # Docker客户端封装
│   │   └── client.go
│   ├── models/                 # 数据模型
│   │   └── service.go
│   ├── services/               # 业务逻辑
│   │   └── service_manager.go
│   └── migration/              # 数据迁移工具
│       └── docker_compose_importer.go
├── web/
│   └── dist/
│       └── index.html          # Web管理界面
├── go.mod
└── README.md
```

## 🔧 配置说明

### 服务配置

每个服务包含以下配置项：

```json
{
  "name": "service-name",
  "display_name": "服务显示名称",
  "category": "base|gis|video|mos|iot|mes|workflow",
  "image": "docker-image-name",
  "version": "image-tag",
  "ports": [
    {
      "host_port": "8080",
      "container_port": "80",
      "protocol": "tcp"
    }
  ],
  "volumes": [
    {
      "host_path": "/host/path",
      "container_path": "/container/path",
      "mode": "rw"
    }
  ],
  "environment": {
    "ENV_VAR": "value"
  },
  "depends_on": [1, 2, 3],
  "enabled": true
}
```

### 服务分组

系统预定义了以下服务分组：

- **base**: 基础服务（数据库、缓存、消息队列）
- **gis**: GIS地理信息服务
- **video**: 视频监控服务
- **mos**: MOS中台服务
- **iot**: IoT数据接入服务
- **mes**: MES制造执行系统
- **workflow**: 工作流服务

## 🔄 迁移指南

### 从Docker Compose迁移

1. **备份现有配置**
   ```bash
   cp -r /path/to/current/project /path/to/backup
   ```

2. **导入配置**
   ```bash
   ./simple-deploy import /path/to/current/project
   ```

3. **验证导入结果**
   - 启动Web界面检查服务配置
   - 测试服务启停功能
   - 验证依赖关系

4. **逐步替换**
   - 先在测试环境验证
   - 逐个服务组迁移
   - 保留原有脚本作为备份

### 配置调整

导入后可能需要手动调整的配置：

- 网络配置（默认使用`middle`网络）
- 卷挂载路径
- 环境变量值
- 服务依赖关系

## 🛠️ 开发指南

### 添加新功能

1. **数据模型**：在`internal/models/`中定义
2. **业务逻辑**：在`internal/services/`中实现
3. **API接口**：在`internal/api/`中添加
4. **前端界面**：修改`web/dist/index.html`

### 扩展服务类型

1. 在数据库中添加新的服务分组
2. 更新导入器的分组映射
3. 在前端界面添加新分组显示

### 自定义配置模板

```go
// 在Configuration模型中使用模板
config := &models.Configuration{
    Name: "nginx.conf",
    TemplateContent: `
server {
    listen {{.Port}};
    server_name {{.ServerName}};
    location / {
        proxy_pass {{.UpstreamURL}};
    }
}`,
    Variables: map[string]interface{}{
        "Port": 80,
        "ServerName": "example.com",
        "UpstreamURL": "http://backend:8080",
    },
}
```

## 🔍 故障排除

### 常见问题

1. **Docker连接失败**
   - 检查Docker守护进程是否运行
   - 确认用户有Docker权限

2. **服务启动失败**
   - 检查镜像是否存在
   - 验证端口是否被占用
   - 查看服务日志

3. **依赖关系错误**
   - 确认依赖服务已启动
   - 检查服务间网络连通性

### 日志查看

```bash
# 查看程序日志
./simple-deploy serve --log-level debug

# 查看服务日志（通过Web界面或API）
curl http://localhost:8080/api/v1/services/1/logs?tail=100
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License

## 🔗 相关链接

- [Docker SDK for Go](https://docs.docker.com/engine/api/sdk/)
- [Gin Web Framework](https://gin-gonic.com/)
- [GORM](https://gorm.io/)
- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
