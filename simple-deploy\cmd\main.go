package main

import (
	"embed"
	"fmt"
	"log"
	"net/http"
	"os"
	"simple-deploy/internal/api"
	"simple-deploy/internal/database"
	"simple-deploy/internal/migration"
	"simple-deploy/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

//go:embed web/dist/*
var webFiles embed.FS

var rootCmd = &cobra.Command{
    Use:   "simple-deploy",
    Short: "Simple Deploy - 简单部署管理器",
    Long:  "一个类似PocketBase的单文件服务部署管理器，用于简化Docker容器服务的部署和管理",
}

var serveCmd = &cobra.Command{
    Use:   "serve",
    Short: "启动Web服务器",
    Run:   runServer,
}

var importCmd = &cobra.Command{
    Use:   "import [project-path]",
    Short: "从现有项目导入Docker Compose配置",
    Args:  cobra.ExactArgs(1),
    Run:   runImport,
}

func init() {
    rootCmd.AddCommand(serveCmd)
    rootCmd.AddCommand(importCmd)
}

func main() {
    if err := rootCmd.Execute(); err != nil {
        log.Fatal(err)
    }
}

func runServer(cmd *cobra.Command, args []string) {
    // 初始化数据库
    if err := database.Initialize("./simple-deploy.db"); err != nil {
        log.Fatalf("Failed to initialize database: %v", err)
    }
    defer database.Close()

    // 创建服务管理器
    serviceManager, err := services.NewServiceManager()
    if err != nil {
        log.Fatalf("Failed to create service manager: %v", err)
    }
    defer serviceManager.Close()

    // 创建API处理器
    handler := api.NewHandler(serviceManager)

    // 设置Gin路由
    r := gin.Default()

    // 静态文件服务（内嵌的前端文件）
    r.StaticFS("/static", http.FS(webFiles))

    // 主页重定向到前端应用
    r.GET("/", func(c *gin.Context) {
        c.Redirect(http.StatusMovedPermanently, "/static/web/dist/index.html")
    })

    // API路由组
    apiV1 := r.Group("/api/v1")
    {
        // 仪表板
        apiV1.GET("/dashboard", handler.GetDashboard)

        // 服务管理
        services := apiV1.Group("/services")
        {
            services.GET("", handler.GetServices)
            services.POST("", handler.CreateService)
            services.GET("/:id", handler.GetService)
            services.PUT("/:id", handler.UpdateService)
            services.DELETE("/:id", handler.DeleteService)
            services.POST("/:id/start", handler.StartService)
            services.POST("/:id/stop", handler.StopService)
            services.POST("/:id/restart", handler.RestartService)
            services.GET("/:id/logs", handler.GetServiceLogs)
        }

        // 服务组管理
        groups := apiV1.Group("/groups")
        {
            groups.GET("/:group/services", handler.GetServicesByGroup)
            groups.POST("/:group/start", handler.StartGroup)
            groups.POST("/:group/stop", handler.StopGroup)
        }
    }

    // 启动服务器
    port := ":8080"
    fmt.Printf("Simple Deploy is running on http://localhost%s\n", port)
    fmt.Println("Access the web interface at: http://localhost:8080")

    if err := r.Run(port); err != nil {
        log.Fatalf("Failed to start server: %v", err)
    }
}

func runImport(cmd *cobra.Command, args []string) {
    projectPath := args[0]

    // 检查项目路径是否存在
    if _, err := os.Stat(projectPath); os.IsNotExist(err) {
        log.Fatalf("Project path does not exist: %s", projectPath)
    }

    // 初始化数据库
    if err := database.Initialize("./simple-deploy.db"); err != nil {
        log.Fatalf("Failed to initialize database: %v", err)
    }
    defer database.Close()

    // 创建导入器
    importer := migration.NewDockerComposeImporter(migration.ImporterConfig{
        ProjectPath: projectPath,
    })

    fmt.Printf("Importing Docker Compose configurations from: %s\n", projectPath)

    // 导入服务
    if err := importer.ImportFromProject(); err != nil {
        log.Fatalf("Failed to import project: %v", err)
    }

    // 更新依赖关系
    fmt.Println("Updating service dependencies...")
    if err := importer.UpdateDependencies(); err != nil {
        log.Printf("Warning: Failed to update dependencies: %v", err)
    }

    fmt.Println("Import completed successfully!")
    fmt.Println("Run 'simple-deploy serve' to start the web interface.")
}
