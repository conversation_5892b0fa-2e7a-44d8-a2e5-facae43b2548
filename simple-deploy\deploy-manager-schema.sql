-- Deploy Manager 数据库模型设计

-- 服务定义表
CREATE TABLE services (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200),
    description TEXT,
    category VARCHAR(50), -- base, gis, video, mos, iot, mes, workflow
    image VARCHAR(200) NOT NULL,
    version VARCHAR(50) DEFAULT 'latest',
    ports TEXT, -- JSON格式存储端口映射
    volumes TEXT, -- JSON格式存储卷映射
    environment_vars TEXT, -- JSON格式存储环境变量
    command TEXT,
    depends_on TEXT, -- JSON格式存储依赖服务ID列表
    health_check TEXT, -- JSON格式存储健康检查配置
    restart_policy VARCHAR(20) DEFAULT 'unless-stopped',
    enabled BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'stopped', -- stopped, starting, running, error
    container_id VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 服务分组表
CREATE TABLE service_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200),
    description TEXT,
    enabled BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 服务与分组关联表
CREATE TABLE service_group_relations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_id INTEGER NOT NULL,
    group_id INTEGER NOT NULL,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES service_groups(id) ON DELETE CASCADE,
    UNIQUE(service_id, group_id)
);

-- 配置文件管理表
CREATE TABLE configurations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_id INTEGER,
    config_type VARCHAR(50), -- env, volume, template
    name VARCHAR(200) NOT NULL,
    file_path VARCHAR(500),
    content TEXT,
    template_content TEXT, -- 模板内容，支持变量替换
    variables TEXT, -- JSON格式存储模板变量
    is_template BOOLEAN DEFAULT false,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- 系统配置表
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category VARCHAR(50),
    key VARCHAR(100) NOT NULL UNIQUE,
    value TEXT,
    description TEXT,
    data_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 部署历史表
CREATE TABLE deployments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_id INTEGER,
    action VARCHAR(20), -- start, stop, restart, update
    status VARCHAR(20), -- success, failed, running
    container_id VARCHAR(100),
    logs TEXT,
    error_message TEXT,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- 备份记录表
CREATE TABLE backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    backup_type VARCHAR(50), -- full, config, data
    name VARCHAR(200),
    file_path VARCHAR(500),
    file_size INTEGER,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 网络配置表
CREATE TABLE networks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    driver VARCHAR(50) DEFAULT 'bridge',
    options TEXT, -- JSON格式存储网络选项
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 数据卷配置表
CREATE TABLE volumes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    driver VARCHAR(50) DEFAULT 'local',
    options TEXT, -- JSON格式存储卷选项
    mount_point VARCHAR(500),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_services_category ON services(category);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_deployments_service_id ON deployments(service_id);
CREATE INDEX idx_deployments_started_at ON deployments(started_at);
CREATE INDEX idx_configurations_service_id ON configurations(service_id);
