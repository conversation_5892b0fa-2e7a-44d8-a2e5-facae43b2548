version: '3.8'

services:
  simple-deploy:
    build: .
    container_name: simple-deploy
    ports:
      - "8080:8080"
    volumes:
      # 挂载Docker socket以便管理其他容器
      - /var/run/docker.sock:/var/run/docker.sock
      # 持久化数据库文件
      - ./data:/app/data
      # 挂载配置目录（可选）
      - ./configs:/app/configs
    environment:
      - GIN_MODE=release
    restart: unless-stopped
    networks:
      - simple-deploy-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/v1/dashboard"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  simple-deploy-network:
    driver: bridge
    name: simple-deploy-network

volumes:
  simple-deploy-data:
    driver: local
