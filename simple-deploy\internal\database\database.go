package database

import (
	"fmt"
	"simple-deploy/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// Initialize 初始化数据库连接
func Initialize(dbPath string) error {
    var err error
    
    // 配置GORM
    config := &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    }
    
    // 连接SQLite数据库
    DB, err = gorm.Open(sqlite.Open(dbPath), config)
    if err != nil {
        return fmt.Errorf("failed to connect database: %v", err)
    }
    
    // 自动迁移数据库结构
    err = autoMigrate()
    if err != nil {
        return fmt.Errorf("failed to migrate database: %v", err)
    }
    
    // 初始化默认数据
    err = seedDefaultData()
    if err != nil {
        return fmt.Errorf("failed to seed default data: %v", err)
    }
    
    return nil
}

// autoMigrate 自动迁移数据库结构
func autoMigrate() error {
    return DB.AutoMigrate(
        &models.Service{},
        &models.ServiceGroup{},
        &models.Configuration{},
        &models.Deployment{},
        &models.SystemSetting{},
    )
}

// seedDefaultData 初始化默认数据
func seedDefaultData() error {
    // 创建默认服务分组
    defaultGroups := []models.ServiceGroup{
        {Name: "base", DisplayName: "基础服务", Description: "数据库、缓存、消息队列等基础服务", SortOrder: 1},
        {Name: "gis", DisplayName: "GIS服务", Description: "地理信息系统服务", SortOrder: 2},
        {Name: "video", DisplayName: "视频服务", Description: "视频监控和流媒体服务", SortOrder: 3},
        {Name: "mos", DisplayName: "MOS中台", Description: "用户权限和前端服务", SortOrder: 4},
        {Name: "iot", DisplayName: "IoT平台", Description: "数据接入和监控服务", SortOrder: 5},
        {Name: "mes", DisplayName: "MES系统", Description: "制造执行系统", SortOrder: 6},
        {Name: "workflow", DisplayName: "工作流", Description: "业务流程管理", SortOrder: 7},
    }
    
    for _, group := range defaultGroups {
        var existingGroup models.ServiceGroup
        if err := DB.Where("name = ?", group.Name).First(&existingGroup).Error; err == gorm.ErrRecordNotFound {
            if err := DB.Create(&group).Error; err != nil {
                return err
            }
        }
    }
    
    // 创建默认系统配置
    defaultSettings := []models.SystemSetting{
        {Category: "general", Key: "app_name", Value: "Deploy Manager", Description: "应用名称"},
        {Category: "general", Key: "app_version", Value: "1.0.0", Description: "应用版本"},
        {Category: "docker", Key: "docker_host", Value: "unix:///var/run/docker.sock", Description: "Docker守护进程地址"},
        {Category: "backup", Key: "backup_path", Value: "./backups", Description: "备份文件存储路径"},
        {Category: "config", Key: "config_path", Value: "./configs", Description: "配置文件存储路径"},
    }
    
    for _, setting := range defaultSettings {
        var existingSetting models.SystemSetting
        if err := DB.Where("key = ?", setting.Key).First(&existingSetting).Error; err == gorm.ErrRecordNotFound {
            if err := DB.Create(&setting).Error; err != nil {
                return err
            }
        }
    }
    
    return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
    return DB
}

// Close 关闭数据库连接
func Close() error {
    sqlDB, err := DB.DB()
    if err != nil {
        return err
    }
    return sqlDB.Close()
}
