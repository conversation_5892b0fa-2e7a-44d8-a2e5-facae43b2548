package migration

import (
	"fmt"
	"os"
	"path/filepath"
	"simple-deploy/internal/database"
	"simple-deploy/internal/models"
	"strings"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"gopkg.in/yaml.v3"
)

// DockerComposeService Docker Compose服务定义
type DockerComposeService struct {
    Image       string            `yaml:"image"`
    Ports       []string          `yaml:"ports"`
    Volumes     []string          `yaml:"volumes"`
    Environment map[string]string `yaml:"environment"`
    DependsOn   []string          `yaml:"depends_on"`
    Command     string            `yaml:"command"`
    Restart     string            `yaml:"restart"`
}

// DockerCompose Docker Compose文件结构
type DockerCompose struct {
    Version  string                           `yaml:"version"`
    Services map[string]DockerComposeService `yaml:"services"`
    Networks map[string]interface{}          `yaml:"networks"`
    Volumes  map[string]interface{}          `yaml:"volumes"`
}

// ImporterConfig 导入配置
type ImporterConfig struct {
    ProjectPath string
    GroupMappings map[string]string // 目录名到分组的映射
}

// DockerComposeImporter Docker Compose导入器
type DockerComposeImporter struct {
    config ImporterConfig
}

// NewDockerComposeImporter 创建导入器
func NewDockerComposeImporter(config ImporterConfig) *DockerComposeImporter {
    return &DockerComposeImporter{
        config: config,
    }
}

// ImportFromProject 从项目目录导入所有docker-compose文件
func (importer *DockerComposeImporter) ImportFromProject() error {
    // 默认分组映射
    if importer.config.GroupMappings == nil {
        importer.config.GroupMappings = map[string]string{
            "base":     "base",
            "gis":      "gis",
            "video":    "video",
            "mos":      "mos",
            "iot":      "iot",
            "mes":      "mes",
            "workflow": "workflow",
            "app":      "mos", // app归类到mos组
        }
    }

    // 扫描项目目录
    dirs := []string{"base", "gis", "video", "mos", "iot", "mes", "workflow", "app"}

    for _, dir := range dirs {
        composePath := filepath.Join(importer.config.ProjectPath, "services", dir, "docker-compose.yml")
        if err := importer.ImportFromFile(composePath, dir); err != nil {
            fmt.Printf("Warning: Failed to import from %s: %v\n", composePath, err)
            continue
        }
    }

    return nil
}

// ImportFromFile 从单个docker-compose文件导入
func (importer *DockerComposeImporter) ImportFromFile(filePath, category string) error {
    // 读取文件
    data, err := os.ReadFile(filePath)
    if err != nil {
        return fmt.Errorf("failed to read file %s: %v", filePath, err)
    }

    // 解析YAML
    var compose DockerCompose
    if err := yaml.Unmarshal(data, &compose); err != nil {
        return fmt.Errorf("failed to parse yaml: %v", err)
    }

    // 获取分组
    groupName := importer.config.GroupMappings[category]
    if groupName == "" {
        groupName = category
    }

    var group models.ServiceGroup
    if err := database.DB.Where("name = ?", groupName).First(&group).Error; err != nil {
        return fmt.Errorf("group %s not found", groupName)
    }

    // 导入服务
    for serviceName, composeService := range compose.Services {
        service := importer.convertToService(serviceName, composeService, category)

        // 检查服务是否已存在
        var existingService models.Service
        if err := database.DB.Where("name = ?", service.Name).First(&existingService).Error; err == nil {
            // 服务已存在，更新
            service.ID = existingService.ID
            if err := database.DB.Save(&service).Error; err != nil {
                fmt.Printf("Warning: Failed to update service %s: %v\n", serviceName, err)
                continue
            }
        } else {
            // 创建新服务
            if err := database.DB.Create(&service).Error; err != nil {
                fmt.Printf("Warning: Failed to create service %s: %v\n", serviceName, err)
                continue
            }
        }

        // 关联到分组
        database.DB.Model(&group).Association("Services").Append(&service)

        fmt.Printf("Imported service: %s (category: %s)\n", serviceName, category)
    }

    return nil
}

// convertToService 将Docker Compose服务转换为内部服务模型
func (importer *DockerComposeImporter) convertToService(name string, composeService DockerComposeService, category string) models.Service {
    service := models.Service{
        Name:        name,
        DisplayName: cases.Title(language.English).String(strings.ReplaceAll(name, "_", " ")),
        Category:    category,
        Image:       importer.extractImageName(composeService.Image),
        Version:     importer.extractImageTag(composeService.Image),
        Command:     composeService.Command,
        Environment: composeService.Environment,
        Enabled:     true,
        Status:      "stopped",
    }

    // 设置重启策略
    if composeService.Restart != "" {
        service.RestartPolicy = composeService.Restart
    }

    // 转换端口映射
    service.Ports = importer.convertPorts(composeService.Ports)

    // 转换卷映射
    service.Volumes = importer.convertVolumes(composeService.Volumes)

    // 转换依赖关系（这里需要后续处理，因为需要服务ID）
    // service.DependsOn = importer.convertDependencies(composeService.DependsOn)

    return service
}

// extractImageName 提取镜像名称
func (importer *DockerComposeImporter) extractImageName(image string) string {
    parts := strings.Split(image, ":")
    return parts[0]
}

// extractImageTag 提取镜像标签
func (importer *DockerComposeImporter) extractImageTag(image string) string {
    parts := strings.Split(image, ":")
    if len(parts) > 1 {
        return parts[1]
    }
    return "latest"
}

// convertPorts 转换端口映射
func (importer *DockerComposeImporter) convertPorts(ports []string) []models.PortMapping {
    var portMappings []models.PortMapping

    for _, port := range ports {
        parts := strings.Split(port, ":")
        if len(parts) >= 2 {
            portMapping := models.PortMapping{
                HostPort:      parts[0],
                ContainerPort: parts[1],
                Protocol:      "tcp",
            }

            // 检查是否指定了协议
            if strings.Contains(parts[1], "/") {
                containerParts := strings.Split(parts[1], "/")
                portMapping.ContainerPort = containerParts[0]
                portMapping.Protocol = containerParts[1]
            }

            portMappings = append(portMappings, portMapping)
        }
    }

    return portMappings
}

// convertVolumes 转换卷映射
func (importer *DockerComposeImporter) convertVolumes(volumes []string) []models.VolumeMapping {
    var volumeMappings []models.VolumeMapping

    for _, volume := range volumes {
        parts := strings.Split(volume, ":")
        if len(parts) >= 2 {
            volumeMapping := models.VolumeMapping{
                HostPath:      parts[0],
                ContainerPath: parts[1],
                Mode:          "rw",
            }

            // 检查是否指定了模式
            if len(parts) >= 3 {
                volumeMapping.Mode = parts[2]
            }

            volumeMappings = append(volumeMappings, volumeMapping)
        }
    }

    return volumeMappings
}

// UpdateDependencies 更新服务依赖关系（需要在所有服务导入后执行）
func (importer *DockerComposeImporter) UpdateDependencies() error {
    // 重新扫描所有docker-compose文件，更新依赖关系
    dirs := []string{"base", "gis", "video", "mos", "iot", "mes", "workflow", "app"}

    for _, dir := range dirs {
        composePath := filepath.Join(importer.config.ProjectPath, "services", dir, "docker-compose.yml")
        if err := importer.updateDependenciesFromFile(composePath); err != nil {
            fmt.Printf("Warning: Failed to update dependencies from %s: %v\n", composePath, err)
            continue
        }
    }

    return nil
}

// updateDependenciesFromFile 从文件更新依赖关系
func (importer *DockerComposeImporter) updateDependenciesFromFile(filePath string) error {
    data, err := os.ReadFile(filePath)
    if err != nil {
        return err
    }

    var compose DockerCompose
    if err := yaml.Unmarshal(data, &compose); err != nil {
        return err
    }

    for serviceName, composeService := range compose.Services {
        if len(composeService.DependsOn) == 0 {
            continue
        }

        // 查找服务
        var service models.Service
        if err := database.DB.Where("name = ?", serviceName).First(&service).Error; err != nil {
            continue
        }

        // 查找依赖服务的ID
        var dependencyIDs []uint
        for _, depName := range composeService.DependsOn {
            var depService models.Service
            if err := database.DB.Where("name = ?", depName).First(&depService).Error; err == nil {
                dependencyIDs = append(dependencyIDs, depService.ID)
            }
        }

        // 更新依赖关系
        service.DependsOn = dependencyIDs
        database.DB.Save(&service)
    }

    return nil
}
