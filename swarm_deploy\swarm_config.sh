# 创建配置
create_configs() {
  # Nginx 配置
  docker config create nginx_conf_v1 ./mos/config/nginx.conf
  
  # Prometheus 配置
  docker config create prometheus_yml_v1 ./tool/conf/prometheus.yml
  
  # 数据库密码
  echo "$POSTGRES_PASSWORD" | docker secret create postgres_password -
}

# 更新配置（版本化）
update_config() {
  local config_name=$1
  local config_file=$2
  local version=$3
  
  docker config create ${config_name}_v${version} $config_file
  
  # 更新使用该配置的服务
  docker service update --config-rm ${config_name}_v$((version-1)) \
    --config-add source=${config_name}_v${version},target=/etc/config \
    $service_name
}